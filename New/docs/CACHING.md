# API Client Intelligent Caching

This document describes the intelligent caching system implemented for the DermaCare DataSync API clients.

## Overview

The caching system provides automatic, intelligent caching for all GET requests in both ClinicalCare (CC) and AutoPatient (AP) API clients, with automatic cache invalidation for mutating operations.

## Features

### ✅ Implemented Features

- **Automatic GET Request Caching**: All GET requests are automatically cached
- **Cloudflare Workers Compatible**: Uses in-memory Map-based caching optimized for Workers
- **TTL (Time-to-Live) Expiration**: Configurable cache expiration (default: 5 minutes)
- **LRU Eviction**: Least Recently Used eviction when cache size limit is reached
- **Smart Cache Invalidation**: Automatic invalidation for POST/PUT/DELETE operations
- **Pattern-based Invalidation**: Invalidates related endpoints (e.g., `/contacts/123` invalidates `/contacts/`)
- **Optional Cache Bypass**: `invalidateCache?: boolean` parameter for all GET methods
- **Memory Efficient**: Configurable cache size limits (default: 100 entries)
- **Type Safe**: Full TypeScript support with preserved function signatures

### 🎯 Cache Configuration

```typescript
// Default cache settings
const defaultConfig = {
  ttl: 5 * 60 * 1000,    // 5 minutes
  maxSize: 100,          // 100 entries
  autoCleanup: true      // Automatic expired entry cleanup
};

// Specialized caches with different TTL
const patientCache = new CloudflareCache({
  ttl: 10 * 60 * 1000,  // 10 minutes (patient data changes less frequently)
  maxSize: 50
});

const appointmentCache = new CloudflareCache({
  ttl: 3 * 60 * 1000,   // 3 minutes (appointment data changes more frequently)
  maxSize: 50
});
```

## Usage Examples

### Basic Usage (Backward Compatible)

```typescript
import { ccClient, apClient } from './apiClient';

// All existing code works unchanged
const patient = await ccClient.patientReq.get(123);
const contact = await apClient.contactReq.get('contact-id');
const patients = await ccClient.patientReq.all({ page: 1, perPage: 20 });
```

### Cache Invalidation

```typescript
// Force fresh data (bypass cache)
const freshPatient = await ccClient.patientReq.get(123, true);
const freshContacts = await apClient.contactReq.all({ 
  limit: 50, 
  invalidateCache: true 
});
```

### Automatic Cache Invalidation

```typescript
// This GET request will be cached
const patient = await ccClient.patientReq.get(123);

// This POST request will automatically invalidate:
// - GET /patients/123 (exact match)
// - GET /patients/ (parent resource)
await ccClient.patientReq.update(123, updatedData);

// This GET request will fetch fresh data (cache was invalidated)
const updatedPatient = await ccClient.patientReq.get(123);
```

## Cache Key Strategy

Cache keys are generated using the format:
```
${method}:${url}:${JSON.stringify(params || {})}
```

Examples:
- `GET:/patients/123:{}`
- `GET:/patients/:{"page":1,"perPage":20,"active":true}`
- `GET:/contacts/:{"limit":50,"locationId":"abc123"}`

## Cache Invalidation Patterns

### Automatic Invalidation Rules

1. **Exact Match**: `DELETE /patients/123` invalidates `GET /patients/123`
2. **Parent Resource**: Any mutation to `/patients/123` also invalidates `GET /patients/`
3. **Pattern Matching**: Uses URL path analysis to determine related endpoints

### Invalidation Examples

| Mutating Request | Invalidated Cache Entries |
|------------------|---------------------------|
| `POST /patients` | All `/patients/*` entries |
| `PUT /patients/123` | `/patients/123` and `/patients/` |
| `DELETE /contacts/abc` | `/contacts/abc` and `/contacts/` |
| `POST /appointments` | All `/appointments/*` entries |

## API Changes

### Updated Function Signatures

All GET methods now accept an optional `invalidateCache` parameter:

```typescript
// CC Client
ccClient.patientReq.get(id: number, invalidateCache?: boolean)
ccClient.patientReq.search(emailOrPhone: string, invalidateCache?: boolean)
ccClient.patientReq.all(params?: {..., invalidateCache?: boolean})

// AP Client  
apClient.contactReq.get(id: string, invalidateCache?: boolean)
apClient.contactReq.all(params?: {..., invalidateCache?: boolean})
apClient.apAppointmentReq.get(apId: string, invalidateCache?: boolean)
```

### Backward Compatibility

- ✅ All existing function calls work unchanged
- ✅ No breaking changes to existing code
- ✅ Optional parameters default to `false` (use cache)
- ✅ All TypeScript types preserved

## Performance Benefits

### Cache Hit Scenarios

1. **Repeated Data Access**: Multiple calls to the same endpoint return cached data
2. **Related Data Queries**: Similar queries with different parameters benefit from caching
3. **Dashboard Loading**: Multiple widgets loading the same data share cache
4. **Background Sync**: Reduces API calls during data synchronization

### Expected Performance Improvements

- **Reduced API Latency**: Cache hits return data in ~1ms vs ~100-500ms API calls
- **Lower API Rate Limits**: Fewer actual API requests
- **Improved User Experience**: Faster page loads and data updates
- **Reduced Server Load**: Less stress on backend APIs

## Monitoring and Debugging

### Cache Statistics

```typescript
import { apiResponseCache } from '@utils/advancedCache';

// Get cache performance metrics
const stats = apiResponseCache.getStats();
console.log({
  size: stats.size,           // Current number of cached entries
  maxSize: stats.maxSize,     // Maximum cache size
  hitRate: stats.hitRate,     // Average hits per entry
  oldestEntry: stats.oldestEntry // Timestamp of oldest entry
});
```

### Debug Logging

The cache system logs invalidation events:

```
CC API: Invalidated 3 cache entries for PUT /patients/123
AP API: Invalidated 1 cache entries for DELETE /contacts/abc
```

## Configuration

### Environment-Specific Settings

```typescript
// Development: Shorter TTL for faster testing
const devCache = new CloudflareCache({
  ttl: 30 * 1000,  // 30 seconds
  maxSize: 50
});

// Production: Longer TTL for better performance
const prodCache = new CloudflareCache({
  ttl: 10 * 60 * 1000,  // 10 minutes
  maxSize: 200
});
```

### Memory Usage Guidelines

- **Default Settings**: ~100 entries × ~2KB average = ~200KB memory
- **Large Deployments**: Consider increasing `maxSize` to 200-500 entries
- **Memory Constrained**: Reduce `maxSize` to 50 entries or lower TTL

## Testing

Run the cache test suite:

```bash
# Run cache functionality tests
npx tsx test-cache.ts
```

The test suite covers:
- ✅ Basic get/set operations
- ✅ TTL expiration
- ✅ LRU eviction
- ✅ Pattern-based invalidation
- ✅ Cache statistics

## Troubleshooting

### Common Issues

1. **Stale Data**: Use `invalidateCache: true` to force fresh data
2. **Memory Usage**: Monitor cache size and adjust `maxSize` if needed
3. **Cache Misses**: Check TTL settings and invalidation patterns

### Performance Tuning

1. **Adjust TTL**: Longer TTL = better performance, higher stale data risk
2. **Tune Cache Size**: Larger cache = better hit rate, more memory usage
3. **Monitor Hit Rates**: Aim for >70% cache hit rate for optimal performance

## Future Enhancements

Potential improvements for future versions:

- 🔄 **Persistent Caching**: Use Cloudflare KV for cross-request persistence
- 📊 **Advanced Metrics**: Detailed cache analytics and monitoring
- 🎯 **Smart Prefetching**: Predictive cache warming
- 🔧 **Dynamic TTL**: Adaptive TTL based on data change frequency
- 🌐 **Distributed Cache**: Multi-region cache synchronization
