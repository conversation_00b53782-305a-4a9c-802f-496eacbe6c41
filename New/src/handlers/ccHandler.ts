/**
 * CliniCore Webhook Handler
 *
 * Handles incoming webhook events from CliniCore platform.
 * Processes patient creation and update events with unified logic,
 * proper validation, error handling, and database logging.
 */

import type { CCPatientWebhookPayload, CCWebhookPayload } from "@type";
import type { Context } from "hono";
import {
	patientSyncProcessor,
	validatePatientWebhookPayload,
} from "@/processors/patientProcessor";
import { logValidationError, logWebhookError } from "@/utils/errorLogger";
import { logInfo, logProcessingStep, logWebhook } from "@/utils/logger";

/**
 * Handle CliniCore webhook events
 *
 * @param c - Hono context object
 * @returns JSON response with processing results
 */
export async function handleCCWebhook(c: Context): Promise<Response> {
	const startTime = Date.now();
	const requestId = c.get("requestId");

	try {
		logInfo(requestId, "CC webhook received");

		// Parse JSON payload
		let payload: unknown;
		try {
			payload = await c.req.json();
		} catch (error) {
			await logValidationError(error as Error, requestId, "json_parsing");
			return c.json(
				{
					error: "Invalid JSON payload",
					requestId,
					timestamp: new Date().toISOString(),
				},
				400,
			);
		}

		// Validate webhook payload structure
		let validatedPayload: CCWebhookPayload;
		try {
			validatedPayload = payload as CCWebhookPayload;

			// Basic validation
			if (
				!validatedPayload.event ||
				!validatedPayload.model ||
				!validatedPayload.payload
			) {
				throw new Error("Missing required fields: event, model, or payload");
			}
		} catch (error) {
			await logValidationError(
				error as Error,
				requestId,
				"webhook_structure",
				payload as Record<string, unknown>,
			);
			return c.json(
				{
					error: `Invalid payload structure: ${error}`,
					requestId,
					timestamp: new Date().toISOString(),
				},
				400,
			);
		}

		// Only process patient creation and update events
		if (
			validatedPayload.model !== "Patient" ||
			(validatedPayload.event !== "EntityWasCreated" &&
			 validatedPayload.event !== "EntityWasUpdated")
		) {
			logInfo(
				requestId,
				`Ignoring event: ${validatedPayload.event} for model: ${validatedPayload.model}`,
			);
			return c.json(
				{
					message: "Event ignored - only processing patient creation and update events",
					event: validatedPayload.event,
					model: validatedPayload.model,
					requestId,
					timestamp: new Date().toISOString(),
				},
				200,
			);
		}

		// Validate patient-specific payload
		let patientPayload: CCPatientWebhookPayload;
		try {
			patientPayload = validatePatientWebhookPayload(validatedPayload);
		} catch (error) {
			await logValidationError(
				error as Error,
				requestId,
				"patient_payload",
				validatedPayload as Record<string, unknown>,
			);
			return c.json(
				{
					error: `Invalid patient payload: ${error}`,
					requestId,
					timestamp: new Date().toISOString(),
				},
				400,
			);
		}

		// Process patient sync (creation or update)
		try {
			logWebhook(
				requestId,
				patientPayload.event,
				patientPayload.model,
				patientPayload.payload.id,
			);
			await patientSyncProcessor(requestId, patientPayload);

			const processingTime = Date.now() - startTime;
			logProcessingStep(
				requestId,
				"Patient sync completed",
				processingTime,
			);

			return c.json(
				{
					message: "Patient synced successfully",
					patientId: patientPayload.payload.id,
					event: patientPayload.event,
					processingTime,
					requestId,
					timestamp: new Date().toISOString(),
				},
				200,
			);
		} catch (error) {
			const processingTime = Date.now() - startTime;
			await logWebhookError(error as Error, requestId, {
				event: patientPayload.event,
				model: patientPayload.model,
				patientId: patientPayload.payload.id,
				processingTime,
			});

			return c.json(
				{
					error: "Patient sync failed",
					details: String(error),
					patientId: patientPayload.payload.id,
					event: patientPayload.event,
					processingTime,
					requestId,
					timestamp: new Date().toISOString(),
				},
				500,
			);
		}
	} catch (error) {
		const processingTime = Date.now() - startTime;
		await logWebhookError(error as Error, requestId, {
			processingTime,
			stage: "general_processing",
		});

		return c.json(
			{
				error: "Internal server error",
				details: String(error),
				processingTime,
				requestId,
				timestamp: new Date().toISOString(),
			},
			500,
		);
	}
}
