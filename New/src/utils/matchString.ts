/**
 * String matching utility for data comparison and synchronization
 *
 * Performs fuzzy string matching by normalizing values to alphanumeric-only lowercase strings.
 * This utility is essential for comparing data between CC and AP platforms where formatting
 * differences (spaces, punctuation, case) might cause false mismatches during sync operations.
 *
 * **Normalization Process:**
 * 1. Converts any data type to string representation
 * 2. Removes all non-alphanumeric characters (spaces, punctuation, symbols)
 * 3. Converts to lowercase for case-insensitive comparison
 * 4. Compares the normalized strings for equality
 *
 * **Use Cases:**
 * - Patient name comparison between platforms
 * - Email address validation and matching
 * - Phone number comparison (ignoring formatting)
 * - Address matching with different formatting
 * - Custom field value comparison
 *
 * **Performance:**
 * - O(n) time complexity where n is the length of the longer string
 * - Minimal memory overhead with string operations
 * - Optimized for high-volume data comparison
 *
 * @param value1 - First value to compare (any type, will be converted to string)
 * @param value2 - Second value to compare (any type, will be converted to string)
 *
 * @returns True if the normalized strings match, false otherwise
 * @returns True if both values are null/undefined
 * @returns False if only one value is null/undefined
 *
 * @example
 * ```typescript
 * // Basic string matching
 * matchString("<PERSON>", "john-doe") // true
 * matchString("(*************", "**********") // true
 * matchString("<EMAIL>", "<EMAIL>") // true
 *
 * // Object comparison
 * matchString({ name: "John" }, '{"name":"john"}') // true
 *
 * // Null/undefined handling
 * matchString(null, undefined) // true
 * matchString("value", null) // false
 *
 * // Different types
 * matchString(123, "123") // true
 * matchString(true, "true") // true
 * ```
 *
 * @since 1.0.0
 * @version 1.0.0
 */
export const matchString = (value1: unknown, value2: unknown): boolean => {
	// Handle null/undefined cases
	if (value1 == null && value2 == null) return true;
	if (value1 == null || value2 == null) return false;

	/**
	 * Converts any data type to string representation
	 * Handles objects by attempting JSON serialization
	 *
	 * @param value - Value to convert to string
	 * @returns String representation of the value
	 */
	const getString = (value: unknown): string => {
		if (typeof value === "object") {
			try {
				return JSON.stringify(value);
			} catch {
				return String(value);
			}
		}
		return String(value);
	};

	/**
	 * Normalizes strings by removing all non-alphanumeric characters and converting to lowercase
	 * This creates a standardized format for comparison that ignores formatting differences
	 *
	 * @param str - String to normalize
	 * @returns Normalized string with only lowercase alphanumeric characters
	 */
	const cleanString = (str: string): string => {
		return str.replace(/[^a-zA-Z0-9]/g, "").toLowerCase();
	};

	const cleanValue1 = cleanString(getString(value1));
	const cleanValue2 = cleanString(getString(value2));

	return cleanValue1 === cleanValue2;
};

export default matchString;
