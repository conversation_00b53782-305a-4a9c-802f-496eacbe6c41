{"id": "997d6a59-158d-4c24-b62d-ee6dd27a00a9", "prevId": "9c55e746-8c37-41a3-9d84-f19f24475436", "version": "7", "dialect": "postgresql", "tables": {"public.ap_custom_fields": {"name": "ap_custom_fields", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ap_id": {"name": "ap_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"ap_custom_fields_ap_id_unique": {"name": "ap_custom_fields_ap_id_unique", "nullsNotDistinct": false, "columns": ["ap_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.appointments": {"name": "appointments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ap_id": {"name": "ap_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "cc_id": {"name": "cc_id", "type": "integer", "primaryKey": false, "notNull": false}, "patient_id": {"name": "patient_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ap_updated_at": {"name": "ap_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "cc_updated_at": {"name": "cc_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "ap_data": {"name": "ap_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "cc_data": {"name": "cc_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "ap_note_id": {"name": "ap_note_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"appointments_patient_id_patients_id_fk": {"name": "appointments_patient_id_patients_id_fk", "tableFrom": "appointments", "tableTo": "patients", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"appointments_ap_id_unique": {"name": "appointments_ap_id_unique", "nullsNotDistinct": false, "columns": ["ap_id"]}, "appointments_cc_id_unique": {"name": "appointments_cc_id_unique", "nullsNotDistinct": false, "columns": ["cc_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.cc_custom_fields": {"name": "cc_custom_fields", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "cc_id": {"name": "cc_id", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"cc_custom_fields_cc_id_unique": {"name": "cc_custom_fields_cc_id_unique", "nullsNotDistinct": false, "columns": ["cc_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.error_logs": {"name": "error_logs", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "request_id": {"name": "request_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "stack": {"name": "stack", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.patients": {"name": "patients", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ap_id": {"name": "ap_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "cc_id": {"name": "cc_id", "type": "integer", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ap_updated_at": {"name": "ap_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "cc_updated_at": {"name": "cc_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "ap_data": {"name": "ap_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "cc_data": {"name": "cc_data", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}