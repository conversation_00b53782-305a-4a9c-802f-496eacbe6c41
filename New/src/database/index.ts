import { getConfig } from "@config";
import { neon } from "@neondatabase/serverless";
import { drizzle } from "drizzle-orm/neon-http";
import * as dbSchema from "./schema";

/**
 * Drizzle ORM client instance, connected to Neon Postgres.
 *
 * Uses the serverless neon HTTP driver without connection pooling,
 * which is optimal for Cloudflare Workers stateless runtime.
 * Each request creates a new connection that is automatically
 * cleaned up when the request completes.
 *
 * @see https://orm.drizzle.team/docs/connect-neon
 */
export const getDb = () => {
	const sql = neon(getConfig("databaseUrl") as string);
	return drizzle({ client: sql, schema: dbSchema });
};

export { dbSchema };
