/**
 * Legacy socket event type (for reference) - removed as it's not used
 */

/**
 * New webhook event structure
 * Replaces socket events with standardized webhook format
 */
export interface WebhookEvent {
	/** Event type (e.g., "EntityWasCreated", "EntityWasUpdated", "EntityWasDeleted") */
	event:
		| "EntityWasCreated"
		| "EntityWasUpdated"
		| "EntityWasDeleted"
		| "AppointmentWasCreated"
		| "AppointmentWasUpdated"
		| "AppointmentWasDeleted";
	/** Model type (e.g., "Patient", "Appointment", "Invoice", "Payment") */
	model: "Patient" | "Appointment" | "Invoice" | "Payment" | "Service";
	/** Entity ID */
	id: number;
	/** Entity data payload */
	payload: Record<string, unknown>;
	/** Optional timestamp */
	timestamp?: string;
}

/**
 * Webhook processing context
 */
export interface WebhookContext {
	/** Webhook event data */
	event: WebhookEvent;
	/** Processing timestamp */
	processedAt: Date;
	/** Request ID for tracking */
	requestId: string;
}

/**
 * Context-Aware Sync Types
 *
 * These types support the intelligent sync decision logic that determines
 * the appropriate action to take based on existing database state.
 */

/**
 * Sync action types that determine how to handle platform synchronization
 *
 * - `skip`: Record already synced to target platform, no action needed
 * - `create`: Create new record in target platform
 * - `update`: Update existing record in target platform
 * - `search_then_create`: Search target platform first, create if not found
 */
export type SyncAction = "skip" | "create" | "update" | "search_then_create";

/**
 * Platform identifiers for sync operations
 *
 * - `cc`: CliniCore (medical practice management platform)
 * - `ap`: AutoPatient (patient engagement and communication platform)
 */
export type SyncPlatform = "cc" | "ap";

/**
 * Operation types for sync context
 *
 * - `create`: Creating a new record (from webhook create events)
 * - `update`: Updating an existing record (from webhook update events)
 */
export type SyncOperation = "create" | "update";

// KeyValue type removed as it's not used

export * from "./APTypes";
export * from "./CCTypes";
