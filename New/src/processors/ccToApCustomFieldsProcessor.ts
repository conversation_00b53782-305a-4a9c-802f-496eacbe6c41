/**
 * CC to AP Custom Fields Processor
 *
 * Handles synchronization of custom fields from CliniCore (CC) to AutoPatient (AP) by:
 * 1. Fetching CC patient custom field data using the custom field IDs
 * 2. Filtering out standard contact fields to prevent conflicts
 * 3. Mapping CC custom field values to AP custom field format with proper Unicode handling
 * 4. Creating AP custom fields with appropriate data types if they don't exist
 * 5. Updating the AP contact with the synchronized custom field values
 *
 * Features:
 * - Unicode-aware field name matching for international characters
 * - Data type mapping between CC and AP custom field types
 * - Prevention of standard contact field conversion to custom fields
 * - Proper handling of multiple values in CC custom fields
 */

import type {
	APGetCustomFieldType,
	APPostCustomfieldType,
	GetCCCustomField,
	GetCCPatientCustomField,
	GetCCPatientType,
	PostAPContactType,
} from "@type";
import { apCustomfield, contactReq, patientReq } from "@/apiClient";
import { logApiError } from "@/utils/errorLogger";
import { logDebug, logError, logInfo, logProcessingStep } from "@/utils/logger";

/**
 * Standard contact fields that should not be converted to custom fields
 * Based on PostAPContactType interface to prevent conflicts with core contact data
 */
const STANDARD_CONTACT_FIELDS = [
	"email",
	"phone",
	"name",
	"firstName",
	"lastName",
	"timezone",
	"dnd",
	"source",
	"assignedTo",
	"address1",
	"city",
	"state",
	"country",
	"postalCode",
	"tags",
	"dateOfBirth",
	"ssn",
	"gender",
	"customFields",
	// Common variations and translations
	"first name",
	"last name",
	"date of birth",
	"phone number",
	"email address",
	"postal code",
	"zip code",
	"address",
	"vorname",
	"nachname",
	"geburtsdatum",
	"telefon",
	"e-mail",
	"adresse",
	"postleitzahl",
];

/**
 * Mapping configuration for CC custom fields to AP standard contact fields
 * Maps CC custom field names/labels to AP standard field names
 */
const CC_TO_AP_STANDARD_FIELD_MAPPING: Record<string, string> = {
	// Phone field variations
	"phone-mobile": "phone",
	phonemobile: "phone",
	"phone mobile": "phone",
	"telefon mobil": "phone",
	"telefon-mobil": "phone",
	telefon: "phone",
	mobile: "phone",
	handy: "phone",
	mobiltelefon: "phone",
	"cell phone": "phone",
	"cell-phone": "phone",
	cellular: "phone",
	"mobile phone": "phone",
	"mobile number": "phone",
	"mobile-number": "phone",
	cell: "phone",
	cellphone: "phone",
	handynummer: "phone",
	mobilnummer: "phone",

	// Email field variations (for future use)
	"e-mail": "email",
	"email address": "email",
	"e-mail address": "email",
	"e-mail-adresse": "email",
	"email-adresse": "email",
	"electronic mail": "email",
};

/**
 * CC to AP data type mapping
 * Maps CliniCore custom field types to AutoPatient data types
 */
const CC_TO_AP_DATA_TYPE_MAPPING: Record<string, string> = {
	// Text-based fields
	text: "TEXT",
	textarea: "LARGE_TEXT",
	string: "TEXT",

	// Numeric fields
	number: "NUMERICAL",
	integer: "NUMERICAL",
	decimal: "FLOAT",
	float: "FLOAT",
	currency: "MONETORY",
	money: "MONETORY",

	// Contact fields
	phone: "PHONE",
	telephone: "PHONE",

	// Boolean fields
	boolean: "CHECKBOX",
	checkbox: "CHECKBOX",

	// Selection fields
	select: "SINGLE_OPTIONS",
	dropdown: "SINGLE_OPTIONS",
	radio: "SINGLE_OPTIONS",
	multiselect: "MULTIPLE_OPTIONS",

	// Date/Time fields
	date: "DATE",
	datetime: "DATE",
	time: "TIME",

	// File fields
	file: "FILE_UPLOAD",
	upload: "FILE_UPLOAD",
	attachment: "FILE_UPLOAD",

	// Signature
	signature: "SIGNATURE",

	// Default fallback
	default: "TEXT",
};

/**
 * Interface for custom field mapping result
 */
interface CustomFieldMapping {
	/** AP custom field ID */
	id: string;
	/** Field value to set */
	value: string;
}

/**
 * Normalize string for Unicode-aware comparison
 * Handles German Umlaut characters and other special characters
 */
function normalizeFieldName(name: string): string {
	return name
		.toLowerCase()
		.normalize("NFD")
		.replace(/[\u0300-\u036f]/g, "") // Remove diacritics
		.replace(/[^\w\s]/g, "") // Remove special characters except word chars and spaces
		.replace(/\s+/g, " ") // Normalize whitespace
		.trim();
}

/**
 * Check if two field names match using Unicode-aware comparison
 */
function fieldNamesMatch(name1: string, name2: string): boolean {
	const normalized1 = normalizeFieldName(name1);
	const normalized2 = normalizeFieldName(name2);
	return normalized1 === normalized2;
}

/**
 * Generate AP fieldKey from CC field name
 *
 * Normalizes the field name to match AutoPatient's auto-generation pattern.
 * This function attempts to replicate how AP generates fieldKeys from field names.
 *
 * @param ccFieldName - CC field name to convert
 * @returns Normalized fieldKey that should match AP's generation
 */
function generateApFieldKey(ccFieldName: string): string {
	// Step-by-step normalization with intermediate logging for debugging
	const step1 = ccFieldName.toLowerCase();
	const step2 = step1.normalize("NFD"); // Decompose Unicode characters
	const step3 = step2.replace(/[\u0300-\u036f]/g, ""); // Remove diacritics (ä -> a, ü -> u, etc.)
	const step4 = step3.replace(/[^a-z0-9]/g, ""); // Remove all non-alphanumeric characters
	const step5 = step4.trim();

	// For debugging complex field names, we can enable detailed logging
	// This is commented out for performance but can be enabled for troubleshooting
	/*
	if (ccFieldName.includes("Befunde") || ccFieldName.length > 10) {
		console.log(`FieldKey generation for "${ccFieldName}":`, {
			step1_lowercase: step1,
			step2_normalized: step2,
			step3_no_diacritics: step3,
			step4_alphanumeric_only: step4,
			step5_final: step5
		});
	}
	*/

	return step5;
}

/**
 * Determine if enhanced debugging should be triggered for a field
 *
 * Only triggers for fields that actually have issues or complex characteristics
 * to avoid excessive logging and performance impact.
 *
 * @param ccField - CC field to analyze
 * @param apCustomFields - Available AP fields for context
 * @param expectedFieldKey - Generated fieldKey for the field
 * @returns True if enhanced debugging should be enabled
 */
function shouldTriggerEnhancedDebugging(
	ccField: GetCCCustomField,
	apCustomFields: APGetCustomFieldType[],
	expectedFieldKey: string
): boolean {
	const fieldName = ccField.name || "";
	const fieldLabel = ccField.label || "";

	// Only trigger for genuinely complex cases
	const hasUnicodeChars = /[^\x00-\x7F]/.test(fieldName) || /[^\x00-\x7F]/.test(fieldLabel);
	const hasComplexPunctuation = /[^\w\s-]/.test(fieldName) || /[^\w\s-]/.test(fieldLabel);
	const isVeryShort = fieldName.length <= 2;
	const isVeryLong = fieldName.length > 40;

	// Check if there are potential conflicts (similar fieldKeys)
	const potentialConflicts = apCustomFields.filter(f =>
		f.fieldKey?.toLowerCase().includes(expectedFieldKey.toLowerCase()) ||
		expectedFieldKey.toLowerCase().includes(f.fieldKey?.toLowerCase() || "")
	).length > 0;

	// Only trigger if field has complex characteristics OR potential conflicts
	return (hasUnicodeChars || hasComplexPunctuation || isVeryShort || isVeryLong) && potentialConflicts;
}

/**
 * Find fields that are actually relevant to the current field
 *
 * Uses precise matching to avoid false positives and reduce log noise
 *
 * @param ccField - CC field to find relevant fields for
 * @param apCustomFields - Available AP fields
 * @param expectedFieldKey - Generated fieldKey
 * @returns Array of relevant AP fields
 */
function findRelevantFields(
	ccField: GetCCCustomField,
	apCustomFields: APGetCustomFieldType[],
	expectedFieldKey: string
): APGetCustomFieldType[] {
	const fieldName = ccField.name.toLowerCase();
	const fieldLabel = ccField.label.toLowerCase();

	return apCustomFields.filter(f => {
		const apName = f.name.toLowerCase();
		const apFieldKey = f.fieldKey?.toLowerCase() || "";

		// Only include truly relevant fields
		const nameExactMatch = apName === fieldName || apName === fieldLabel;
		const fieldKeyExactMatch = apFieldKey === expectedFieldKey.toLowerCase() ||
								   apFieldKey === `contact.${expectedFieldKey}`.toLowerCase();
		const substantialOverlap = (fieldName.length > 4 && apName.includes(fieldName)) ||
								   (apName.length > 4 && fieldName.includes(apName));

		return nameExactMatch || fieldKeyExactMatch || substantialOverlap;
	});
}

/**
 * Duplicate field analysis result
 */
interface DuplicateAnalysis {
	fieldKey: string;
	duplicateCount: number;
	selectedField: APGetCustomFieldType;
	rejectedFields: APGetCustomFieldType[];
	selectionReason: string;
}

/**
 * Select the best field from a group of duplicates using intelligent criteria
 *
 * Selection priority:
 * 1. Field name quality (prefer exact names over variations)
 * 2. Field metadata completeness
 * 3. Field ID (assuming newer IDs are better)
 *
 * @param duplicateFields - Array of duplicate fields
 * @param requestId - Request ID for logging
 * @returns The best field to keep
 */
function selectBestDuplicateField(
	duplicateFields: APGetCustomFieldType[],
	requestId: string
): APGetCustomFieldType {
	if (duplicateFields.length === 1) {
		return duplicateFields[0];
	}

	// Score each field based on quality criteria
	const scoredFields = duplicateFields.map(field => {
		let score = 0;
		let reasons: string[] = [];

		// Criterion 1: Field name quality
		const hasCleanName = !field.name.includes("_") && !field.name.includes("  ");
		if (hasCleanName) {
			score += 10;
			reasons.push("clean name");
		}

		// Criterion 2: Metadata completeness
		if (field.dataType) {
			score += 5;
			reasons.push("has dataType");
		}

		// Criterion 3: Field ID pattern (later alphabetically = newer)
		// This is a heuristic - newer fields often have IDs that sort later
		const idScore = field.id.localeCompare("") * 0.1; // Small weight
		score += idScore;

		// Criterion 4: Prefer shorter, cleaner field names
		if (field.name.length < 30 && !field.name.includes("_")) {
			score += 3;
			reasons.push("concise name");
		}

		return {
			field,
			score,
			reasons
		};
	});

	// Sort by score (highest first)
	scoredFields.sort((a, b) => b.score - a.score);

	const selectedField = scoredFields[0];
	logDebug(
		requestId,
		`Field selection for duplicates: Selected "${selectedField.field.name}" (score: ${selectedField.score.toFixed(2)}, reasons: ${selectedField.reasons.join(", ")})`
	);

	return selectedField.field;
}

/**
 * Get human-readable reason for field selection
 *
 * @param selectedField - The field that was selected
 * @param allFields - All duplicate fields
 * @returns Human-readable selection reason
 */
function getFieldSelectionReason(
	selectedField: APGetCustomFieldType,
	allFields: APGetCustomFieldType[]
): string {
	const reasons: string[] = [];

	// Check name quality
	const hasCleanName = !selectedField.name.includes("_") && !selectedField.name.includes("  ");
	const othersHaveMessyNames = allFields.some(f => f.id !== selectedField.id && (f.name.includes("_") || f.name.includes("  ")));

	if (hasCleanName && othersHaveMessyNames) {
		reasons.push("cleaner field name");
	}

	// Check metadata
	if (selectedField.dataType && allFields.some(f => f.id !== selectedField.id && !f.dataType)) {
		reasons.push("has complete metadata");
	}

	// Check if it's the "newest" based on ID
	const isLatestId = allFields.every(f => f.id <= selectedField.id);
	if (isLatestId && allFields.length > 1) {
		reasons.push("latest field ID");
	}

	return reasons.length > 0 ? reasons.join(", ") : "first occurrence";
}

/**
 * Advanced duplicate detection and resolution with intelligent field selection
 *
 * Analyzes duplicate fieldKeys and selects the best field to keep based on:
 * - Field name quality (exact names vs variations)
 * - Field metadata completeness
 * - Field ID patterns (newer fields typically have later IDs)
 *
 * @param apCustomFields - Array of AP custom fields to analyze
 * @param requestId - Request ID for logging
 * @returns Validation result with detailed duplicate analysis
 */
function validateAndCleanApFields(
	apCustomFields: APGetCustomFieldType[],
	requestId: string
): { duplicatesRemoved: number; issues: string[]; duplicateAnalysis: DuplicateAnalysis[] } {
	const issues: string[] = [];
	const duplicateAnalysis: DuplicateAnalysis[] = [];
	let duplicatesRemoved = 0;

	// Group fields by fieldKey
	const fieldKeyMap = new Map<string, APGetCustomFieldType[]>();

	apCustomFields.forEach(field => {
		if (field.fieldKey) {
			const key = field.fieldKey.toLowerCase();
			if (!fieldKeyMap.has(key)) {
				fieldKeyMap.set(key, []);
			}
			fieldKeyMap.get(key)!.push(field);
		}
	});

	// Analyze and resolve duplicates
	const duplicateKeys = Array.from(fieldKeyMap.entries()).filter(([_, fields]) => fields.length > 1);

	if (duplicateKeys.length > 0) {
		logError(
			requestId,
			`CRITICAL: Found ${duplicateKeys.length} duplicate fieldKeys in AutoPatient API response - this indicates data integrity issues`
		);

		duplicateKeys.forEach(([key, fields]) => {
			// Detailed analysis of each duplicate group
			logError(
				requestId,
				`Duplicate fieldKey "${key}" found in ${fields.length} fields:`
			);

			fields.forEach((field, index) => {
				logError(
					requestId,
					`  Field ${index + 1}: "${field.name}" (ID: ${field.id}, dataType: ${field.dataType || 'undefined'})`
				);
			});

			// Select the best field using intelligent criteria
			const selectedField = selectBestDuplicateField(fields, requestId);
			const rejectedFields = fields.filter(f => f.id !== selectedField.id);

			// Create analysis record
			const analysis: DuplicateAnalysis = {
				fieldKey: key,
				duplicateCount: fields.length,
				selectedField,
				rejectedFields,
				selectionReason: getFieldSelectionReason(selectedField, fields)
			};

			duplicateAnalysis.push(analysis);

			logInfo(
				requestId,
				`Selected field "${selectedField.name}" (ID: ${selectedField.id}) as canonical for fieldKey "${key}". Reason: ${analysis.selectionReason}`
			);

			// Remove rejected fields from the main array
			rejectedFields.forEach(rejectedField => {
				const index = apCustomFields.findIndex(f => f.id === rejectedField.id);
				if (index !== -1) {
					apCustomFields.splice(index, 1);
					duplicatesRemoved++;
					logDebug(
						requestId,
						`Removed duplicate field "${rejectedField.name}" (ID: ${rejectedField.id})`
					);
				}
			});
		});

		issues.push(`Resolved ${duplicateKeys.length} duplicate fieldKey groups`);
		logInfo(
			requestId,
			`Duplicate resolution completed: ${duplicatesRemoved} duplicate fields removed, ${apCustomFields.length} fields remaining`
		);
	}

	// Check for other data quality issues
	const fieldsWithoutFieldKey = apCustomFields.filter(f => !f.fieldKey);
	if (fieldsWithoutFieldKey.length > 0) {
		issues.push(`${fieldsWithoutFieldKey.length} fields missing fieldKey`);
		logDebug(requestId, `Fields without fieldKey: ${fieldsWithoutFieldKey.map(f => f.name).slice(0, 5).join(", ")}`);
	}

	return { duplicatesRemoved, issues, duplicateAnalysis };
}

/**
 * Find existing AP custom field by name or predicted fieldKey
 *
 * Uses multiple matching strategies to handle various fieldKey formats and Unicode issues:
 * 1. Direct name matching (CC name vs AP name)
 * 2. Label matching (CC label vs AP name)
 * 3. Multiple fieldKey matching strategies to handle different AP formats
 * 4. Case-insensitive fallback matching
 *
 * @param apCustomFields - Array of existing AP custom fields
 * @param ccField - CC custom field to find match for
 * @param requestId - Request ID for detailed logging
 * @returns Matching AP field or undefined
 */
function findExistingApField(
	apCustomFields: APGetCustomFieldType[],
	ccField: GetCCCustomField,
	requestId: string,
): APGetCustomFieldType | undefined {
	const expectedFieldKey = generateApFieldKey(ccField.name);

	logDebug(
		requestId,
		`Searching for existing field: CC name="${ccField.name}", CC label="${ccField.label}", expected fieldKey="${expectedFieldKey}"`
	);
	logDebug(requestId, `Available AP fields: ${apCustomFields.length} total`);

	// Log sample of AP fields for debugging (first 3 and any that might match)
	const sampleFields = apCustomFields.slice(0, 3);
	sampleFields.forEach((field, index) => {
		logDebug(
			requestId,
			`AP Field ${index + 1}: name="${field.name}", fieldKey="${field.fieldKey || 'undefined'}"`
		);
	});

	// Enhanced debugging only for fields that actually need it (performance optimization)
	const needsEnhancedDebugging = shouldTriggerEnhancedDebugging(ccField, apCustomFields, expectedFieldKey);
	if (needsEnhancedDebugging) {
		logInfo(requestId, `ENHANCED DEBUG for complex field "${ccField.name}": Searching among ${apCustomFields.length} AP fields`);

		// Log only truly relevant fields to avoid noise
		const relevantFields = findRelevantFields(ccField, apCustomFields, expectedFieldKey);

		if (relevantFields.length > 0) {
			logInfo(requestId, `Found ${relevantFields.length} relevant fields for analysis:`);
			relevantFields.slice(0, 3).forEach((field, index) => {
				logInfo(
					requestId,
					`Relevant Field ${index + 1}: name="${field.name}", fieldKey="${field.fieldKey}", id="${field.id}"`
				);
			});
			if (relevantFields.length > 3) {
				logInfo(requestId, `... and ${relevantFields.length - 3} more relevant fields`);
			}
		} else {
			logDebug(requestId, `No relevant fields found for "${ccField.name}" - this may indicate a new field`);
		}
	}

	// Find potential matches and log detailed comparison
	const potentialMatches = apCustomFields.filter((apField) => {
		const nameMatchDirect = fieldNamesMatch(apField.name, ccField.name);
		const nameMatchLabel = fieldNamesMatch(apField.name, ccField.label);

		// Multiple fieldKey matching strategies to handle different AP formats
		const fieldKeyMatchDirect = apField.fieldKey === expectedFieldKey;
		const fieldKeyMatchWithPrefix = apField.fieldKey === `contact.${expectedFieldKey}`;
		const fieldKeyMatchWithoutPrefix = apField.fieldKey?.replace("contact.", "") === expectedFieldKey;

		// Case-insensitive fallback for fieldKey
		const fieldKeyMatchCaseInsensitive = apField.fieldKey?.toLowerCase() === expectedFieldKey.toLowerCase();
		const fieldKeyMatchPrefixCaseInsensitive = apField.fieldKey?.toLowerCase() === `contact.${expectedFieldKey}`.toLowerCase();

		const isMatch = nameMatchDirect || nameMatchLabel || fieldKeyMatchDirect ||
						fieldKeyMatchWithPrefix || fieldKeyMatchWithoutPrefix ||
						fieldKeyMatchCaseInsensitive || fieldKeyMatchPrefixCaseInsensitive;

		if (isMatch) {
			logDebug(
				requestId,
				`POTENTIAL MATCH: AP field "${apField.name}" (fieldKey: "${apField.fieldKey}") vs CC field "${ccField.name}"`
			);
			logDebug(
				requestId,
				`Match reasons: nameMatchDirect=${nameMatchDirect}, nameMatchLabel=${nameMatchLabel}, ` +
				`fieldKeyMatchDirect=${fieldKeyMatchDirect}, fieldKeyMatchWithPrefix=${fieldKeyMatchWithPrefix}, ` +
				`fieldKeyMatchWithoutPrefix=${fieldKeyMatchWithoutPrefix}, fieldKeyMatchCaseInsensitive=${fieldKeyMatchCaseInsensitive}`
			);
		}

		return isMatch;
	});

	if (potentialMatches.length === 0) {
		logDebug(requestId, `No existing field found for CC field "${ccField.name}"`);
		return undefined;
	}

	if (potentialMatches.length > 1) {
		logDebug(
			requestId,
			`Multiple potential matches found for CC field "${ccField.name}": ${potentialMatches.map(f => f.name).join(", ")}`
		);
		// Return the first match, but log this for investigation
	}

	const selectedMatch = potentialMatches[0];
	logDebug(
		requestId,
		`Selected match: AP field "${selectedMatch.name}" (ID: ${selectedMatch.id}, fieldKey: "${selectedMatch.fieldKey}")`
	);

	return selectedMatch;
}

/**
 * Enhanced field existence check with API query fallback
 * First checks local cache, then queries AP API if needed
 */
async function findExistingApFieldWithApiCheck(
	apCustomFields: APGetCustomFieldType[],
	ccField: GetCCCustomField,
	requestId: string,
): Promise<APGetCustomFieldType | undefined> {
	// First check local cache
	let existingField = findExistingApField(apCustomFields, ccField, requestId);

	if (existingField) {
		logDebug(
			requestId,
			`Found existing field in cache: "${existingField.name}" (ID: ${existingField.id})`,
		);
		return existingField;
	}

	// If not found in cache, refresh from API to ensure we have latest data
	logDebug(
		requestId,
		`Field not found in cache, refreshing AP custom fields from API`,
	);

	try {
		const refreshedFields = await apCustomfield.all();
		logDebug(
			requestId,
			`Refreshed AP custom fields from API, total: ${refreshedFields.length}`,
		);
		existingField = findExistingApField(refreshedFields, ccField, requestId);

		if (existingField) {
			logDebug(
				requestId,
				`Found existing field after API refresh: "${existingField.name}" (ID: ${existingField.id})`,
			);
			// Update the cache with fresh data
			apCustomFields.length = 0;
			apCustomFields.push(...refreshedFields);
		}

		return existingField;
	} catch (error) {
		logError(requestId, `Failed to refresh AP custom fields:`, error);
		return undefined;
	}
}

/**
 * Extract fieldKey from AutoPatient "already exists" error message
 *
 * Parses error messages like "contact.befunde already exists" to extract the fieldKey
 *
 * @param errorMessage - Error message from AutoPatient API
 * @returns Extracted fieldKey or null if not found
 */
function extractFieldKeyFromError(errorMessage: string): string | null {
	// Pattern to match "contact.{fieldKey} already exists" or similar
	const patterns = [
		/contact\.([a-zA-Z0-9_-]+)\s+already\s+exists/i,
		/field\s+['""]?contact\.([a-zA-Z0-9_-]+)['""]?\s+already\s+exists/i,
		/([a-zA-Z0-9_-]+)\s+already\s+exists/i, // Fallback pattern
	];

	for (const pattern of patterns) {
		const match = errorMessage.match(pattern);
		if (match && match[1]) {
			return match[1];
		}
	}

	return null;
}

/**
 * Find existing field by extracted fieldKey from error message
 *
 * Uses the fieldKey extracted from the error message to find the existing field
 *
 * @param apCustomFields - Array of AP custom fields
 * @param extractedFieldKey - FieldKey extracted from error message
 * @param requestId - Request ID for logging
 * @returns Matching field or undefined
 */
function findFieldByExtractedKey(
	apCustomFields: APGetCustomFieldType[],
	extractedFieldKey: string,
	requestId: string,
): APGetCustomFieldType | undefined {
	logDebug(requestId, `Searching for field with extracted fieldKey: "${extractedFieldKey}"`);

	const field = apCustomFields.find((apField) => {
		// Try multiple variations of the extracted fieldKey
		const fieldKeyMatches = [
			apField.fieldKey === extractedFieldKey,
			apField.fieldKey === `contact.${extractedFieldKey}`,
			apField.fieldKey?.replace("contact.", "") === extractedFieldKey,
			apField.fieldKey?.toLowerCase() === extractedFieldKey.toLowerCase(),
			apField.fieldKey?.toLowerCase() === `contact.${extractedFieldKey}`.toLowerCase(),
		];

		return fieldKeyMatches.some(match => match);
	});

	if (field) {
		logDebug(
			requestId,
			`Found field by extracted fieldKey: "${field.name}" (ID: ${field.id}, fieldKey: "${field.fieldKey}")`
		);
	} else {
		logDebug(requestId, `No field found with extracted fieldKey: "${extractedFieldKey}"`);
	}

	return field;
}

/**
 * Check if a field name represents a standard contact field
 */
function isStandardContactField(fieldName: string): boolean {
	const normalizedFieldName = normalizeFieldName(fieldName);
	return STANDARD_CONTACT_FIELDS.some(
		(standardField) =>
			normalizeFieldName(standardField) === normalizedFieldName,
	);
}

/**
 * Transform boolean field value to Yes/No format for AutoPatient
 *
 * Converts various boolean representations from CliniCore to standardized
 * Yes/No format expected by AutoPatient RADIO fields.
 *
 * @param value - The field value to transform
 * @param fieldType - The CC field type to determine if transformation is needed
 * @returns Transformed value ("Yes"/"No" for boolean fields, original value otherwise)
 *
 * @example
 * ```typescript
* transformBooleanValue("true", "boolean")   // Returns "Yes"
 * transformBooleanValue("false", "boolean")  // Returns "No"
 * transformBooleanValue("1", "boolean")      // Returns "Yes"
 * transformBooleanValue("0", "boolean")      // Returns "No"
 * transformBooleanValue("text", "string")    // Returns "text" (unchanged)
 *
```
 */
function transformBooleanValue(value: string, fieldType: string): string {
	const normalizedType = fieldType.toLowerCase().trim();

	if (normalizedType === "boolean") {
		const normalizedValue = value.toLowerCase().trim();

		// Handle various boolean representations
		if (normalizedValue === "true" || normalizedValue === "1" || normalizedValue === "yes") {
			return "Yes";
		} else if (normalizedValue === "false" || normalizedValue === "0" || normalizedValue === "no") {
			return "No";
		}

		// Default to "No" for any other value
		return "No";
	}

	// Return original value for non-boolean fields
	return value;
}

/**
 * Map CC custom field type to AP data type with enhanced logic
 *
 * Provides intelligent mapping between CliniCore and AutoPatient field types,
 * with special handling for boolean fields (mapped to RADIO with Yes/No options)
 * and select fields with multiple values.
 *
 * @param ccField - CliniCore custom field definition containing type and properties
 * @returns AutoPatient data type string (e.g., "RADIO", "MULTIPLE_OPTIONS", "TEXT")
 *
 * @example
 * ```typescript
* // Boolean field mapping
 * mapCcToApDataType({type: "boolean", ...}) // Returns "RADIO"
 *
 * // Multi-select field mapping
 * mapCcToApDataType({type: "select", allowMultipleValues: true, ...}) // Returns "MULTIPLE_OPTIONS"
 *
 * // Standard text field mapping
 * mapCcToApDataType({type: "text", ...}) // Returns "TEXT"
 *
```
 */
function mapCcToApDataType(ccField: GetCCCustomField): string {
	const normalizedType = ccField.type.toLowerCase().trim();

	// Handle special mapping cases based on field type and properties
	if (normalizedType === "boolean") {
		// Map boolean fields to RADIO with Yes/No options
		return "RADIO";
	}

	if (normalizedType === "select" && ccField.allowMultipleValues === true) {
		return "MULTIPLE_OPTIONS";
	}

	// Use standard mapping for other cases
	return (
		CC_TO_AP_DATA_TYPE_MAPPING[normalizedType] ||
		CC_TO_AP_DATA_TYPE_MAPPING.default
	);
}

/**
 * Check if a CC custom field should be mapped to an AP standard field
 * Returns the AP standard field name if mapping exists, null otherwise
 */
function getStandardFieldMapping(
	ccFieldName: string,
	ccFieldLabel: string,
): string | null {
	const normalizedName = normalizeFieldName(ccFieldName);
	const normalizedLabel = normalizeFieldName(ccFieldLabel);

	// Check both field name and label against the mapping
	for (const [ccFieldPattern, apStandardField] of Object.entries(
		CC_TO_AP_STANDARD_FIELD_MAPPING,
	)) {
		const normalizedPattern = normalizeFieldName(ccFieldPattern);
		if (
			normalizedPattern === normalizedName ||
			normalizedPattern === normalizedLabel
		) {
			return apStandardField as string;
		}
	}

	return null;
}

/**
 * Extract standard field mappings from CC custom fields
 * Returns a map of AP standard field names to their values
 */
function extractStandardFieldMappings(
	ccPatientCustomFields: GetCCPatientCustomField[],
	requestId: string,
): Record<string, string> {
	const standardMappings: Record<string, string> = {};

	logInfo(
		requestId,
		`Extracting standard field mappings from ${ccPatientCustomFields.length} CC custom fields`,
	);

	for (const ccCustomField of ccPatientCustomFields) {
		const fieldName = ccCustomField.field.name;
		const fieldLabel = ccCustomField.field.label;

		// Check if this CC custom field should map to an AP standard field
		const apStandardField = getStandardFieldMapping(fieldName, fieldLabel);

		if (apStandardField) {
			// Extract field value
			const fieldValue = extractFieldValues(ccCustomField);

			if (fieldValue && fieldValue.trim() !== "") {
				// Handle multiple mappings to the same standard field - prioritize non-empty values
				if (
					!standardMappings[apStandardField] ||
					standardMappings[apStandardField].trim() === ""
				) {
					standardMappings[apStandardField] = fieldValue.trim();
					logDebug(
						requestId,
						`Standard field mapping: "${fieldName}" (${fieldLabel}) -> AP.${apStandardField} = "${fieldValue.substring(
							0,
							50,
						)}${fieldValue.length > 50 ? "..." : ""}"`,
					);
				} else {
					logDebug(
						requestId,
						`Skipping duplicate standard field mapping: "${fieldName}" (${fieldLabel}) -> AP.${apStandardField} (already mapped)`,
					);
				}
			} else {
				logDebug(
					requestId,
					`Skipping standard field mapping for empty value: ${fieldName} (${fieldLabel})`,
				);
			}
		}
	}

	logInfo(
		requestId,
		`Extracted ${
			Object.keys(standardMappings).length
		} standard field mappings`,
	);
	return standardMappings;
}

/**
 * Extract and convert CC allowed values to AP textBoxListOptions format
 *
 * Processes CliniCore custom field allowed values and converts them to the format
 * expected by AutoPatient custom fields. Ensures the current field value is included
 * as an option even if not in the original allowed values list.
 *
 * @param ccField - CC custom field with allowedValues array
 * @param currentValue - Current field value to include as an option if not in allowedValues
 * @param requestId - Request ID for logging correlation
 * @returns Array of option strings for AP custom field creation
 *
 * @example
 * ```typescript
* const options = extractTextBoxListOptions(
 *   { allowedValues: [{value: "Option1"}, {value: "Option2"}] },
 *   "Option3",
 *   "req-123"
 * );
 * // Returns: ["Option1", "Option2", "Option3"]
 *
```
 */
function extractTextBoxListOptions(
	ccField: GetCCCustomField,
	currentValue: string,
	requestId: string,
): string[] {
	const options: string[] = [];

	logDebug(
		requestId,
		`Extracting options for field ${ccField.label} (type: ${ccField.type})`,
	);
	logDebug(
		requestId,
		`CC allowedValues: ${JSON.stringify(ccField.allowedValues, null, 2)}`,
	);

	// Extract allowed values from CC field
	if (ccField.allowedValues && ccField.allowedValues.length > 0) {
		for (const allowedValue of ccField.allowedValues) {
			if (allowedValue.value && allowedValue.value.trim() !== "") {
				const trimmedValue = allowedValue.value.trim();
				options.push(trimmedValue);
				logDebug(requestId, `Added option: "${trimmedValue}"`);
			}
		}
	}

	// Ensure current value is included as an option if it's not already present
	if (currentValue && currentValue.trim() !== "") {
		const currentValueTrimmed = currentValue.trim();
		const existingOption = options.find(
			(option) => option.toLowerCase() === currentValueTrimmed.toLowerCase(),
		);

		if (!existingOption) {
			options.push(currentValueTrimmed);
			logDebug(
				requestId,
				`Added current value as option: "${currentValueTrimmed}"`,
			);
		}
	}

	// If no options were found, create a default option based on current value
	if (options.length === 0 && currentValue && currentValue.trim() !== "") {
		const trimmedValue = currentValue.trim();
		options.push(trimmedValue);
		logDebug(requestId, `Created default option: "${trimmedValue}"`);
	}

	return options;
}

/**
 * Extract and combine multiple values from CC custom field
 * Handles different separation strategies based on field type
 */
function extractFieldValues(ccCustomField: GetCCPatientCustomField): string {
	if (!ccCustomField.values || ccCustomField.values.length === 0) {
		return "";
	}

	const values = ccCustomField.values
		.map((v) => v.value)
		.filter((v): v is string => v != null && v.trim() !== "");

	if (values.length === 0) {
		return "";
	}

	// For single value, return as-is
	if (values.length === 1) {
		return values[0];
	}

	// For multiple values, choose separation strategy based on field type
	const fieldType = ccCustomField.field.type?.toLowerCase() || "";

	if (fieldType.includes("multiselect") || fieldType.includes("checkbox")) {
		// Use comma separation for multi-select fields
		return values.join(", ");
	} else if (fieldType.includes("textarea") || fieldType.includes("text")) {
		// Use newline separation for text areas
		return values.join("\n");
	} else {
		// Default to comma separation
		return values.join(", ");
	}
}

/**
 * Synchronize custom fields from CC patient to AP contact
 *
 * @param requestId - Request ID from Hono context for logging correlation
 * @param localPatientId - Local database patient ID for logging context
 * @param ccPatientData - CC patient data containing custom field IDs
 * @param apContactId - AP contact ID to update with custom fields
 * @returns Promise<void> - Completes sync or throws error
 */
export async function syncCcToApCustomFields(
	requestId: string,
	localPatientId: string,
	ccPatientData: GetCCPatientType,
	apContactId: string,
): Promise<void> {
	logProcessingStep(
		requestId,
		`Starting custom field sync for CC Patient ${ccPatientData.id} -> AP Contact ${apContactId} (Local Patient ID: ${localPatientId})`,
	);

	// Step 1: Check if patient has custom fields
	if (!ccPatientData.customFields || ccPatientData.customFields.length === 0) {
		logInfo(
			requestId,
			`No custom fields found for CC patient ${ccPatientData.id}`,
		);
		return;
	}

	try {
		// Step 2: Fetch CC patient custom field data
		logInfo(
			requestId,
			`Fetching ${ccPatientData.customFields.length} custom fields from CC`,
		);
		const ccPatientCustomFields = await patientReq.customFields(
			ccPatientData.customFields,
		);

		if (!ccPatientCustomFields || ccPatientCustomFields.length === 0) {
			logInfo(requestId, `No custom field data returned from CC`);
			return;
		}

		// Step 3: Extract and apply standard field mappings
		const standardFieldMappings = extractStandardFieldMappings(
			ccPatientCustomFields,
			requestId,
		);

		// Build update payload for standard fields
		const standardFieldUpdate: Partial<PostAPContactType> = {};

		// Apply standard field mappings to AP contact if any exist
		if (Object.keys(standardFieldMappings).length > 0) {
			logInfo(
				requestId,
				`Applying ${
					Object.keys(standardFieldMappings).length
				} standard field mappings to AP contact`,
			);

			for (const [fieldName, value] of Object.entries(standardFieldMappings)) {
				if (fieldName === "phone") {
					standardFieldUpdate.phone = value;
				} else if (fieldName === "email") {
					standardFieldUpdate.email = value;
				}
				// Add more standard field mappings as needed
			}
		}

		// Step 4: Filter out excluded fields and extract valid custom field mappings
		const validCustomFields = await filterAndMapCustomFields(
			ccPatientCustomFields,
			requestId,
		);

		if (validCustomFields.length === 0) {
			logInfo(
				requestId,
				`No valid custom fields to sync after filtering`,
			);
			return;
		}

		// Step 4: Get all AP custom fields for mapping (with validation)
		logInfo(requestId, `Fetching AP custom fields for mapping`);
		const apCustomFields = await apCustomfield.all();

		// Validate and clean the field data with advanced duplicate resolution
		const validationResult = validateAndCleanApFields(apCustomFields, requestId);
		if (validationResult.duplicatesRemoved > 0) {
			logInfo(requestId, `Resolved ${validationResult.duplicateAnalysis.length} duplicate fieldKey groups, removed ${validationResult.duplicatesRemoved} duplicate fields`);

			// CRITICAL: Log detailed duplicate analysis for upstream investigation
			logError(
				requestId,
				`UPSTREAM INVESTIGATION: AutoPatient API returned duplicate custom fields. This indicates data integrity issues that need investigation.`
			);

			// Log summary of duplicate resolution for monitoring
			validationResult.duplicateAnalysis.forEach(analysis => {
				logError(
					requestId,
					`Duplicate fieldKey "${analysis.fieldKey}": Found ${analysis.duplicateCount} fields, selected "${analysis.selectedField.name}" (${analysis.selectedField.id}), rejected ${analysis.rejectedFields.length} duplicates`
				);

				// Log rejected fields for forensic analysis
				analysis.rejectedFields.forEach(rejected => {
					logError(
						requestId,
						`  Rejected: "${rejected.name}" (ID: ${rejected.id}, dataType: ${rejected.dataType || 'undefined'})`
					);
				});
			});

			// Add monitoring alert for operations team
			logError(
				requestId,
				`MONITORING ALERT: ${validationResult.duplicatesRemoved} duplicate custom fields detected and resolved. This may indicate AutoPatient API issues or data corruption.`
			);
		}

		// Step 5: Map CC fields to AP format and create missing fields
		const apCustomFieldMappings = await mapToApCustomFields(
			validCustomFields,
			apCustomFields,
			requestId,
		);

		if (apCustomFieldMappings.length === 0) {
			logInfo(requestId, `No custom field mappings created`);
			return;
		}

		// Step 6: Update AP contact with custom fields
		logInfo(
			requestId,
			`Updating AP contact with ${apCustomFieldMappings.length} custom fields`,
		);
		let updatePayload: Partial<PostAPContactType> = {
			customFields: apCustomFieldMappings,
		};
		if (
			Object.keys(standardFieldMappings).length > 0 &&
			Object.keys(standardFieldUpdate).length > 0
		) {
			updatePayload = {
				...updatePayload,
				...standardFieldUpdate,
			};
		}
		await contactReq.update(apContactId, updatePayload);

		logInfo(requestId, `Custom field sync completed successfully`);
	} catch (error) {
		logError(requestId, `Custom field sync failed:`, error);

		// Log the error but don't throw to avoid blocking main patient processing
		await logApiError(
			error as Error,
			requestId,
			"custom_field_sync",
			"cc_to_ap_sync",
			{
				ccPatientId: ccPatientData.id,
				apContactId,
				customFieldCount: ccPatientData.customFields?.length || 0,
			},
		);

		// Re-throw to let caller decide how to handle
		throw error;
	}
}

/**
 * Filter CC custom fields and extract valid field mappings
 * Excludes standard contact fields and empty values
 *
 * @param ccPatientCustomFields - CC patient custom field data
 * @param requestId - Request ID for logging
 * @returns Promise<Array> - Valid custom field data
 */
async function filterAndMapCustomFields(
	ccPatientCustomFields: GetCCPatientCustomField[],
	requestId: string,
): Promise<Array<{ field: GetCCCustomField; value: string }>> {
	const validFields: Array<{ field: GetCCCustomField; value: string }> = [];

	for (const ccCustomField of ccPatientCustomFields) {
		const fieldName = ccCustomField.field.name;
		const fieldLabel = ccCustomField.field.label;

		// Check if field is a standard contact field that should not be converted to custom field
		if (
			isStandardContactField(fieldName) ||
			isStandardContactField(fieldLabel)
		) {
			logDebug(
				requestId,
				`Excluding standard contact field: ${fieldName} (${fieldLabel})`,
			);
			continue;
		}

		// Check if field should be mapped to an AP standard field instead of custom field
		const standardFieldMapping = getStandardFieldMapping(fieldName, fieldLabel);
		if (standardFieldMapping) {
			logDebug(
				requestId,
				`Excluding field mapped to standard field: ${fieldName} (${fieldLabel}) -> AP.${standardFieldMapping}`,
			);
			continue;
		}

		// Extract field value using improved multiple values handling
		const fieldValue = extractFieldValues(ccCustomField);

		if (!fieldValue || fieldValue.trim() === "") {
			logDebug(
				requestId,
				`Skipping field with empty value: ${fieldName}`,
			);
			continue;
		}

		validFields.push({
			field: ccCustomField.field,
			value: fieldValue,
		});

		logDebug(
			requestId,
			`Valid field: ${fieldName} (${fieldLabel}) = ${fieldValue.substring(
				0,
				100,
			)}${fieldValue.length > 100 ? "..." : ""}`,
		);
	}

	return validFields;
}

/**
 * Map CC custom fields to AP custom field format
 * Creates missing AP custom fields with proper data types as needed
 *
 * @param validCustomFields - Filtered CC custom field data
 * @param apCustomFields - Existing AP custom fields
 * @param requestId - Request ID for logging
 * @returns Promise<CustomFieldMapping[]> - AP custom field mappings
 */
async function mapToApCustomFields(
	validCustomFields: Array<{ field: GetCCCustomField; value: string }>,
	apCustomFields: APGetCustomFieldType[],
	requestId: string,
): Promise<CustomFieldMapping[]> {
	const mappings: CustomFieldMapping[] = [];
	let createdCount = 0;
	let existingCount = 0;

	logInfo(
		requestId,
		`Starting custom field mapping for ${validCustomFields.length} CC fields against ${apCustomFields.length} existing AP fields`,
	);

	for (const { field, value } of validCustomFields) {
		try {
			// Try to find existing AP custom field using enhanced matching with API fallback
			let apCustomField = await findExistingApFieldWithApiCheck(
				apCustomFields,
				field,
				requestId,
			);

			// Create AP custom field if it doesn't exist
			if (!apCustomField) {
				const mappedDataType = mapCcToApDataType(field);
				const fieldKey = generateApFieldKey(field.name);

				// CRITICAL: Pre-creation duplicate check to prevent contributing to duplicate problem
				const preCreationCheck = apCustomFields.filter(f =>
					f.fieldKey?.toLowerCase() === fieldKey.toLowerCase() ||
					f.fieldKey?.toLowerCase() === `contact.${fieldKey}`.toLowerCase()
				);

				if (preCreationCheck.length > 0) {
					logError(
						requestId,
						`DUPLICATE PREVENTION: Found ${preCreationCheck.length} existing fields with fieldKey "${fieldKey}" before creation attempt. This suggests our field detection logic missed existing fields.`
					);
					preCreationCheck.forEach((existingField, index) => {
						logError(
							requestId,
							`Existing field ${index + 1}: "${existingField.name}" (ID: ${existingField.id}, fieldKey: "${existingField.fieldKey}")`
						);
					});

					// Use the first existing field instead of creating a duplicate
					apCustomField = preCreationCheck[0];
					logInfo(
						requestId,
						`Using existing field "${apCustomField.name}" (ID: ${apCustomField.id}) to prevent duplicate creation`
					);
					existingCount++;
				} else {
					logInfo(
						requestId,
						`Creating new AP custom field: "${field.label}" with fieldKey: "${fieldKey}" (type: ${field.type}${field.allowMultipleValues ? ' (multiple)' : ''} -> ${mappedDataType}${field.type.toLowerCase() === 'boolean' ? ' with Yes/No options' : ''})`,
					);

					const createData: APPostCustomfieldType = {
					name: field.label, // Use CC label as AP name (user-friendly)
					dataType: mappedDataType,
					model: "contact", // Set model to contact as required
					fieldKey: fieldKey, // Use CC name for fieldKey (normalized)
				};

				// Add conditional properties based on data type
				if (mappedDataType === "RADIO" && field.type.toLowerCase() === "boolean") {
					// For boolean fields mapped to RADIO, add Yes/No options
					createData.options = ["Yes", "No"];

					logDebug(
						requestId,
						`Adding Yes/No options for boolean field: ${field.label}`,
					);
				} else if (
					mappedDataType === "SINGLE_OPTIONS" ||
					mappedDataType === "MULTIPLE_OPTIONS"
				) {
					// Extract allowed values from CC field and convert to AP format
					const textBoxListOptions = extractTextBoxListOptions(field, value, requestId);

					if (textBoxListOptions.length > 0) {
						// Based on successful test: API expects "options" as simple string array
						createData.options = textBoxListOptions;

						logDebug(
							requestId,
							`Adding ${textBoxListOptions.length} options to ${
								field.label
							}: ${textBoxListOptions.join(", ")}`,
						);
						logDebug(
							requestId,
							`Using simple string array for options (confirmed working structure)`,
						);
					} else {
						// If no options can be created, fall back to TEXT type to avoid API error
						logDebug(
							requestId,
							`No options available for ${field.label}, falling back to TEXT type`,
						);
						createData.dataType = "TEXT";
					}
				}

				try {
					apCustomField = await apCustomfield.create(createData);

					// Add to our local cache to avoid duplicate creation
					apCustomFields.push(apCustomField);

					logInfo(
						requestId,
						`Successfully created AP custom field: "${field.label}" with ID: ${apCustomField.id}`,
					);
					createdCount++;
				} catch (createError: unknown) {
					const errorMessage =
						createError instanceof Error
							? createError.message
							: String(createError);

					// Handle duplicate fieldKey error by trying to find the existing field
					if (errorMessage.includes("already exists")) {
						logInfo(
							requestId,
							`Field creation failed - already exists. Error: "${errorMessage}"`,
						);
						logDebug(
							requestId,
							`Attempting to find existing field for CC field "${field.name}" with generated fieldKey "${fieldKey}"`,
						);

						// Strategy 1: Try enhanced field finding with fresh API data
						apCustomField = await findExistingApFieldWithApiCheck(
							apCustomFields,
							field,
							requestId,
						);

						// Strategy 2: If not found, try extracting fieldKey from error message
						if (!apCustomField) {
							const extractedFieldKey = extractFieldKeyFromError(errorMessage);
							if (extractedFieldKey) {
								logDebug(
									requestId,
									`Extracted fieldKey "${extractedFieldKey}" from error message, searching for field`,
								);

								// Refresh fields and search by extracted fieldKey
								try {
									const freshFields = await apCustomfield.all();
									apCustomField = findFieldByExtractedKey(freshFields, extractedFieldKey, requestId);

									if (apCustomField) {
										// Update cache with fresh data
										apCustomFields.length = 0;
										apCustomFields.push(...freshFields);
									}
								} catch (refreshError) {
									logError(requestId, `Failed to refresh fields for extracted fieldKey search:`, refreshError);
								}
							}
						}

						if (apCustomField) {
							logInfo(
								requestId,
								`Successfully found existing AP custom field: "${apCustomField.name}" (ID: ${apCustomField.id}, fieldKey: "${apCustomField.fieldKey}")`,
							);
							existingCount++;
						} else {
							logError(
								requestId,
								`Could not find existing field after duplicate error. CC field: "${field.name}", generated fieldKey: "${fieldKey}", error: "${errorMessage}"`,
							);
							throw createError;
						}
					} else {
						throw createError;
					}
				}
				} // Close the else block for field creation
			} else {
				logDebug(
					requestId,
					`Using existing AP custom field: "${apCustomField.name}" with ID: ${apCustomField.id}`,
				);
				existingCount++;
			}

			// Transform value for boolean fields
			const transformedValue = transformBooleanValue(value, field.type);

			mappings.push({
				id: apCustomField.id,
				value: transformedValue,
			});

			logDebug(
				requestId,
				`Mapped: "${field.label}" -> AP Field ID ${
					apCustomField.id
				} (${transformedValue.substring(0, 50)}${transformedValue.length > 50 ? "..." : ""})${
					transformedValue !== value ? ` [transformed from: ${value}]` : ""
				}`,
			);
		} catch (error) {
			logError(
				requestId,
				`Failed to map field "${field.name}":`,
				error,
			);
			// Continue with other fields rather than failing completely
		}
	}

	logInfo(
		requestId,
		`Custom field mapping completed: ${mappings.length} total mappings (${createdCount} created, ${existingCount} existing)`,
	);

	return mappings;
}
