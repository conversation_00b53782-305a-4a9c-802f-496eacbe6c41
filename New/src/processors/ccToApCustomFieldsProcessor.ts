/**
 * CC to AP Custom Fields Processor
 *
 * Handles synchronization of custom fields from CliniCore (CC) to AutoPatient (AP) by:
 * 1. Fetching CC patient custom field data using the custom field IDs
 * 2. Filtering out standard contact fields to prevent conflicts
 * 3. Mapping CC custom field values to AP custom field format with proper Unicode handling
 * 4. Creating AP custom fields with appropriate data types if they don't exist
 * 5. Updating the AP contact with the synchronized custom field values
 *
 * Features:
 * - Unicode-aware field name matching for international characters
 * - Data type mapping between CC and AP custom field types
 * - Prevention of standard contact field conversion to custom fields
 * - Proper handling of multiple values in CC custom fields
 */

import type {
	APGetCustomFieldType,
	APPostCustomfieldType,
	GetCCCustomField,
	GetCCPatientCustomField,
	GetCCPatientType,
	PostAPContactType,
} from "@type";
import { apCustomfield, contactReq, patientReq } from "@/apiClient";
import { logApiError } from "@/utils/errorLogger";
import { logDebug, logError, logInfo, logProcessingStep } from "@/utils/logger";

/**
 * Standard contact fields that should not be converted to custom fields
 * Based on PostAPContactType interface to prevent conflicts with core contact data
 */
const STANDARD_CONTACT_FIELDS = [
	"email",
	"phone",
	"name",
	"firstName",
	"lastName",
	"timezone",
	"dnd",
	"source",
	"assignedTo",
	"address1",
	"city",
	"state",
	"country",
	"postalCode",
	"tags",
	"dateOfBirth",
	"ssn",
	"gender",
	"customFields",
	// Common variations and translations
	"first name",
	"last name",
	"date of birth",
	"phone number",
	"email address",
	"postal code",
	"zip code",
	"address",
	"vorname",
	"nachname",
	"geburtsdatum",
	"telefon",
	"e-mail",
	"adresse",
	"postleitzahl",
];

/**
 * Mapping configuration for CC custom fields to AP standard contact fields
 * Maps CC custom field names/labels to AP standard field names
 */
const CC_TO_AP_STANDARD_FIELD_MAPPING: Record<string, string> = {
	// Phone field variations
	"phone-mobile": "phone",
	phonemobile: "phone",
	"phone mobile": "phone",
	"telefon mobil": "phone",
	"telefon-mobil": "phone",
	telefon: "phone",
	mobile: "phone",
	handy: "phone",
	mobiltelefon: "phone",
	"cell phone": "phone",
	"cell-phone": "phone",
	cellular: "phone",
	"mobile phone": "phone",
	"mobile number": "phone",
	"mobile-number": "phone",
	cell: "phone",
	cellphone: "phone",
	handynummer: "phone",
	mobilnummer: "phone",

	// Email field variations (for future use)
	"e-mail": "email",
	"email address": "email",
	"e-mail address": "email",
	"e-mail-adresse": "email",
	"email-adresse": "email",
	"electronic mail": "email",
};

/**
 * CC to AP data type mapping
 * Maps CliniCore custom field types to AutoPatient data types
 */
const CC_TO_AP_DATA_TYPE_MAPPING: Record<string, string> = {
	// Text-based fields
	text: "TEXT",
	textarea: "LARGE_TEXT",
	string: "TEXT",

	// Numeric fields
	number: "NUMERICAL",
	integer: "NUMERICAL",
	decimal: "FLOAT",
	float: "FLOAT",
	currency: "MONETORY",
	money: "MONETORY",

	// Contact fields
	phone: "PHONE",
	telephone: "PHONE",

	// Boolean fields
	boolean: "CHECKBOX",
	checkbox: "CHECKBOX",

	// Selection fields
	select: "SINGLE_OPTIONS",
	dropdown: "SINGLE_OPTIONS",
	radio: "SINGLE_OPTIONS",
	multiselect: "MULTIPLE_OPTIONS",

	// Date/Time fields
	date: "DATE",
	datetime: "DATE",
	time: "TIME",

	// File fields
	file: "FILE_UPLOAD",
	upload: "FILE_UPLOAD",
	attachment: "FILE_UPLOAD",

	// Signature
	signature: "SIGNATURE",

	// Default fallback
	default: "TEXT",
};

/**
 * Interface for custom field mapping result
 */
interface CustomFieldMapping {
	/** AP custom field ID */
	id: string;
	/** Field value to set */
	value: string;
}

/**
 * Normalize string for Unicode-aware comparison
 * Handles German Umlaut characters and other special characters
 */
function normalizeFieldName(name: string): string {
	return name
		.toLowerCase()
		.normalize("NFD")
		.replace(/[\u0300-\u036f]/g, "") // Remove diacritics
		.replace(/[^\w\s]/g, "") // Remove special characters except word chars and spaces
		.replace(/\s+/g, " ") // Normalize whitespace
		.trim();
}

/**
 * Calculate string similarity using Levenshtein distance
 *
 * @param str1 - First string
 * @param str2 - Second string
 * @returns Similarity ratio between 0 and 1
 */
function calculateStringSimilarity(str1: string, str2: string): number {
	const maxLength = Math.max(str1.length, str2.length);
	if (maxLength === 0) return 1;

	const distance = levenshteinDistance(str1, str2);
	return (maxLength - distance) / maxLength;
}

/**
 * Calculate Levenshtein distance between two strings
 *
 * @param str1 - First string
 * @param str2 - Second string
 * @returns Edit distance between strings
 */
function levenshteinDistance(str1: string, str2: string): number {
	const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

	for (let i = 0; i <= str1.length; i++) {
		matrix[0][i] = i;
	}

	for (let j = 0; j <= str2.length; j++) {
		matrix[j][0] = j;
	}

	for (let j = 1; j <= str2.length; j++) {
		for (let i = 1; i <= str1.length; i++) {
			const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
			matrix[j][i] = Math.min(
				matrix[j][i - 1] + 1, // deletion
				matrix[j - 1][i] + 1, // insertion
				matrix[j - 1][i - 1] + indicator // substitution
			);
		}
	}

	return matrix[str2.length][str1.length];
}

/**
 * Check if two field names match using multiple strategies
 *
 * Uses progressive matching strategies to handle various edge cases:
 * 1. Exact normalized match
 * 2. Fuzzy matching for similar names
 * 3. Substring matching for partial matches
 * 4. Unicode-aware matching
 *
 * @param name1 - First field name
 * @param name2 - Second field name
 * @returns True if names are considered a match
 */
function fieldNamesMatch(name1: string, name2: string): boolean {
	// Handle null/undefined cases
	if (!name1 || !name2) {
		return false;
	}

	// Strategy 1: Exact normalized match
	const normalized1 = normalizeFieldName(name1);
	const normalized2 = normalizeFieldName(name2);
	if (normalized1 === normalized2) {
		return true;
	}

	// Strategy 2: Case-insensitive exact match (before normalization)
	if (name1.toLowerCase().trim() === name2.toLowerCase().trim()) {
		return true;
	}

	// Strategy 3: Fuzzy matching for very similar names (handle minor differences)
	const similarity = calculateStringSimilarity(normalized1, normalized2);
	if (similarity > 0.9 && Math.abs(normalized1.length - normalized2.length) <= 2) {
		return true;
	}

	// Strategy 4: Substring matching for partial matches (be careful with this)
	if (normalized1.length > 3 && normalized2.length > 3) {
		if (normalized1.includes(normalized2) || normalized2.includes(normalized1)) {
			// Only match if the difference isn't too large
			const lengthRatio = Math.min(normalized1.length, normalized2.length) / Math.max(normalized1.length, normalized2.length);
			if (lengthRatio > 0.7) {
				return true;
			}
		}
	}

	return false;
}

/**
 * Determine if enhanced debugging should be enabled for a field
 *
 * Triggers enhanced debugging based on field characteristics rather than specific names
 *
 * @param ccField - CC field to analyze
 * @returns True if enhanced debugging should be enabled
 */
function shouldEnhanceDebugging(ccField: GetCCCustomField): boolean {
	const fieldName = ccField.name || "";
	const fieldLabel = ccField.label || "";

	// Trigger enhanced debugging for complex field names
	const hasUnicode = /[^\x00-\x7F]/.test(fieldName) || /[^\x00-\x7F]/.test(fieldLabel);
	const hasSpecialChars = /[^a-zA-Z0-9\s]/.test(fieldName) || /[^a-zA-Z0-9\s]/.test(fieldLabel);
	const isVeryShort = fieldName.length <= 2;
	const isVeryLong = fieldName.length > 50;
	const hasComplexStructure = fieldName.includes("_") || fieldName.includes("-") || /\d/.test(fieldName);

	return hasUnicode || hasSpecialChars || isVeryShort || isVeryLong || hasComplexStructure;
}

/**
 * Generate AP fieldKey from CC field name with enhanced edge case handling
 *
 * Normalizes the field name to match AutoPatient's auto-generation pattern.
 * Handles Unicode characters, special characters, and edge cases robustly.
 *
 * @param ccFieldName - CC field name to convert
 * @returns Normalized fieldKey that should match AP's generation
 */
function generateApFieldKey(ccFieldName: string): string {
	// Handle edge cases
	if (!ccFieldName || typeof ccFieldName !== "string") {
		return "";
	}

	// Trim whitespace first
	const trimmed = ccFieldName.trim();
	if (trimmed.length === 0) {
		return "";
	}

	// Step-by-step normalization for better Unicode handling
	const step1 = trimmed.toLowerCase();
	const step2 = step1.normalize("NFD"); // Decompose Unicode characters (ä -> a + ¨)
	const step3 = step2.replace(/[\u0300-\u036f]/g, ""); // Remove diacritics (ä -> a, ü -> u, etc.)
	const step4 = step3.replace(/[^a-z0-9]/g, ""); // Remove all non-alphanumeric characters
	const step5 = step4.trim();

	// Handle edge case where normalization results in empty string
	if (step5.length === 0) {
		// Fallback: use original with basic cleaning
		const fallback = ccFieldName.toLowerCase().replace(/[^a-z0-9]/g, "");
		return fallback || "field"; // Ultimate fallback
	}

	// Handle very long fieldKeys (AP might have limits)
	if (step5.length > 50) {
		return step5.substring(0, 50);
	}

	return step5;
}

/**
 * Find existing AP custom field by name or predicted fieldKey
 *
 * Uses multiple matching strategies to handle various fieldKey formats and Unicode issues:
 * 1. Direct name matching (CC name vs AP name)
 * 2. Label matching (CC label vs AP name)
 * 3. Multiple fieldKey matching strategies to handle different AP formats
 * 4. Case-insensitive fallback matching
 *
 * @param apCustomFields - Array of existing AP custom fields
 * @param ccField - CC custom field to find match for
 * @param requestId - Request ID for detailed logging
 * @returns Matching AP field or undefined
 */
function findExistingApField(
	apCustomFields: APGetCustomFieldType[],
	ccField: GetCCCustomField,
	requestId: string,
): APGetCustomFieldType | undefined {
	const expectedFieldKey = generateApFieldKey(ccField.name);

	logDebug(
		requestId,
		`Searching for existing field: CC name="${ccField.name}", CC label="${ccField.label}", expected fieldKey="${expectedFieldKey}"`
	);
	logDebug(requestId, `Available AP fields: ${apCustomFields.length} total`);

	// Log sample of AP fields for debugging (first 3 and any that might match)
	const sampleFields = apCustomFields.slice(0, 3);
	sampleFields.forEach((field, index) => {
		logDebug(
			requestId,
			`AP Field ${index + 1}: name="${field.name}", fieldKey="${field.fieldKey || 'undefined'}"`
		);
	});

	// For complex fields, log more detailed information
	if (shouldEnhanceDebugging(ccField)) {
		logInfo(requestId, `ENHANCED DEBUG for complex field "${ccField.name}": Searching among ${apCustomFields.length} AP fields`);

		// Log all fields that might be related
		const potentialMatches = apCustomFields.filter(f => {
			const nameContainsField = f.name.toLowerCase().includes(ccField.name.toLowerCase());
			const fieldKeyContainsExpected = f.fieldKey?.toLowerCase().includes(expectedFieldKey.toLowerCase());
			const fieldContainsName = ccField.name.toLowerCase().includes(f.name.toLowerCase());
			const hasUnicodeChars = /[^\x00-\x7F]/.test(f.name) || /[^\x00-\x7F]/.test(ccField.name);

			return nameContainsField || fieldKeyContainsExpected || fieldContainsName || hasUnicodeChars;
		});

		if (potentialMatches.length > 0) {
			logInfo(requestId, `Found ${potentialMatches.length} potentially related fields:`);
			potentialMatches.slice(0, 5).forEach((field, index) => {
				logInfo(
					requestId,
					`Related Field ${index + 1}: name="${field.name}", fieldKey="${field.fieldKey}", id="${field.id}"`
				);
			});
			if (potentialMatches.length > 5) {
				logInfo(requestId, `... and ${potentialMatches.length - 5} more related fields`);
			}
		} else {
			logInfo(requestId, `No potentially related fields found for "${ccField.name}"`);
		}
	}

	// Find potential matches and log detailed comparison
	const potentialMatches = apCustomFields.filter((apField) => {
		const nameMatchDirect = fieldNamesMatch(apField.name, ccField.name);
		const nameMatchLabel = fieldNamesMatch(apField.name, ccField.label);

		// Multiple fieldKey matching strategies to handle different AP formats
		const fieldKeyMatchDirect = apField.fieldKey === expectedFieldKey;
		const fieldKeyMatchWithPrefix = apField.fieldKey === `contact.${expectedFieldKey}`;
		const fieldKeyMatchWithoutPrefix = apField.fieldKey?.replace("contact.", "") === expectedFieldKey;

		// Case-insensitive fallback for fieldKey
		const fieldKeyMatchCaseInsensitive = apField.fieldKey?.toLowerCase() === expectedFieldKey.toLowerCase();
		const fieldKeyMatchPrefixCaseInsensitive = apField.fieldKey?.toLowerCase() === `contact.${expectedFieldKey}`.toLowerCase();

		const isMatch = nameMatchDirect || nameMatchLabel || fieldKeyMatchDirect ||
						fieldKeyMatchWithPrefix || fieldKeyMatchWithoutPrefix ||
						fieldKeyMatchCaseInsensitive || fieldKeyMatchPrefixCaseInsensitive;

		if (isMatch) {
			logDebug(
				requestId,
				`POTENTIAL MATCH: AP field "${apField.name}" (fieldKey: "${apField.fieldKey}") vs CC field "${ccField.name}"`
			);
			logDebug(
				requestId,
				`Match reasons: nameMatchDirect=${nameMatchDirect}, nameMatchLabel=${nameMatchLabel}, ` +
				`fieldKeyMatchDirect=${fieldKeyMatchDirect}, fieldKeyMatchWithPrefix=${fieldKeyMatchWithPrefix}, ` +
				`fieldKeyMatchWithoutPrefix=${fieldKeyMatchWithoutPrefix}, fieldKeyMatchCaseInsensitive=${fieldKeyMatchCaseInsensitive}`
			);
		}

		return isMatch;
	});

	if (potentialMatches.length === 0) {
		logDebug(requestId, `No existing field found for CC field "${ccField.name}"`);
		return undefined;
	}

	if (potentialMatches.length > 1) {
		logDebug(
			requestId,
			`Multiple potential matches found for CC field "${ccField.name}": ${potentialMatches.map(f => f.name).join(", ")}`
		);
		// Return the first match, but log this for investigation
	}

	const selectedMatch = potentialMatches[0];
	logDebug(
		requestId,
		`Selected match: AP field "${selectedMatch.name}" (ID: ${selectedMatch.id}, fieldKey: "${selectedMatch.fieldKey}")`
	);

	return selectedMatch;
}

/**
 * Enhanced field existence check with API query fallback
 * First checks local cache, then queries AP API if needed
 */
async function findExistingApFieldWithApiCheck(
	apCustomFields: APGetCustomFieldType[],
	ccField: GetCCCustomField,
	requestId: string,
): Promise<APGetCustomFieldType | undefined> {
	// First check local cache
	let existingField = findExistingApField(apCustomFields, ccField, requestId);

	if (existingField) {
		logDebug(
			requestId,
			`Found existing field in cache: "${existingField.name}" (ID: ${existingField.id})`,
		);
		return existingField;
	}

	// If not found in cache, refresh from API to ensure we have latest data
	logInfo(
		requestId,
		`Field not found in cache, refreshing AP custom fields from API (forced refresh)`,
	);

	try {
		const refreshedFields = await apCustomfield.all(true); // Force cache invalidation
		logInfo(
			requestId,
			`Refreshed AP custom fields from API, total: ${refreshedFields.length}`,
		);

		// CRITICAL: Always update the original cache array for consistency
		apCustomFields.length = 0; // Clear the original array
		apCustomFields.push(...refreshedFields); // Update with fresh data
		logDebug(requestId, `Updated original cache array with ${apCustomFields.length} fresh fields`);

		// Search in the refreshed data
		existingField = findExistingApField(apCustomFields, ccField, requestId);

		if (existingField) {
			logInfo(
				requestId,
				`Found existing field after API refresh: "${existingField.name}" (ID: ${existingField.id}, fieldKey: "${existingField.fieldKey}")`,
			);
		} else {
			logDebug(
				requestId,
				`Field still not found after API refresh for CC field: "${ccField.name}"`,
			);

			// Log available fields for debugging
			const availableFieldKeys = apCustomFields.map(f => f.fieldKey).filter(Boolean).slice(0, 10);
			logDebug(
				requestId,
				`Available fieldKeys after refresh (first 10): ${availableFieldKeys.join(", ")}${apCustomFields.length > 10 ? "..." : ""}`
			);
		}

		return existingField;
	} catch (error) {
		logError(requestId, `Failed to refresh AP custom fields:`, error);
		return undefined;
	}
}

/**
 * Extract fieldKey from AutoPatient "already exists" error message
 *
 * Parses error messages like "contact.befunde already exists" to extract the fieldKey
 *
 * @param errorMessage - Error message from AutoPatient API
 * @returns Extracted fieldKey or null if not found
 */
function extractFieldKeyFromError(errorMessage: string): string | null {
	// Pattern to match "contact.{fieldKey} already exists" or similar
	const patterns = [
		/contact\.([a-zA-Z0-9_-]+)\s+already\s+exists/i,
		/field\s+['""]?contact\.([a-zA-Z0-9_-]+)['""]?\s+already\s+exists/i,
		/([a-zA-Z0-9_-]+)\s+already\s+exists/i, // Fallback pattern
	];

	for (const pattern of patterns) {
		const match = errorMessage.match(pattern);
		if (match && match[1]) {
			return match[1];
		}
	}

	return null;
}

/**
 * Find existing field by extracted fieldKey from error message
 *
 * Uses the fieldKey extracted from the error message to find the existing field
 *
 * @param apCustomFields - Array of AP custom fields
 * @param extractedFieldKey - FieldKey extracted from error message
 * @param requestId - Request ID for logging
 * @returns Matching field or undefined
 */
function findFieldByExtractedKey(
	apCustomFields: APGetCustomFieldType[],
	extractedFieldKey: string,
	requestId: string,
): APGetCustomFieldType | undefined {
	logDebug(requestId, `Searching for field with extracted fieldKey: "${extractedFieldKey}"`);

	const field = apCustomFields.find((apField) => {
		// Try multiple variations of the extracted fieldKey
		const fieldKeyMatches = [
			apField.fieldKey === extractedFieldKey,
			apField.fieldKey === `contact.${extractedFieldKey}`,
			apField.fieldKey?.replace("contact.", "") === extractedFieldKey,
			apField.fieldKey?.toLowerCase() === extractedFieldKey.toLowerCase(),
			apField.fieldKey?.toLowerCase() === `contact.${extractedFieldKey}`.toLowerCase(),
		];

		return fieldKeyMatches.some(match => match);
	});

	if (field) {
		logDebug(
			requestId,
			`Found field by extracted fieldKey: "${field.name}" (ID: ${field.id}, fieldKey: "${field.fieldKey}")`
		);
	} else {
		logDebug(requestId, `No field found with extracted fieldKey: "${extractedFieldKey}"`);
	}

	return field;
}

/**
 * Validate that the AP custom fields cache contains reasonable data
 *
 * Performs basic validation to detect potential API or cache issues
 *
 * @param apCustomFields - Array of AP custom fields to validate
 * @param requestId - Request ID for logging
 * @returns Validation result with any issues found
 */
function validateApFieldsCache(
	apCustomFields: APGetCustomFieldType[],
	requestId: string,
): { isValid: boolean; issues: string[] } {
	const issues: string[] = [];

	// Check if we have any fields at all
	if (apCustomFields.length === 0) {
		issues.push("No custom fields returned from API");
	}

	// Check for fields with missing fieldKeys
	const fieldsWithoutFieldKey = apCustomFields.filter(f => !f.fieldKey);
	if (fieldsWithoutFieldKey.length > 0) {
		issues.push(`${fieldsWithoutFieldKey.length} fields missing fieldKey property`);
	}

	// Check for duplicate fieldKeys
	const fieldKeys = apCustomFields.map(f => f.fieldKey).filter(Boolean);
	const uniqueFieldKeys = new Set(fieldKeys);
	if (fieldKeys.length !== uniqueFieldKeys.size) {
		issues.push("Duplicate fieldKeys detected in API response");
	}

	// Log validation results
	if (issues.length > 0) {
		logError(requestId, `AP fields cache validation issues: ${issues.join(", ")}`);
	} else {
		logDebug(requestId, `AP fields cache validation passed: ${apCustomFields.length} fields with valid structure`);
	}

	return {
		isValid: issues.length === 0,
		issues
	};
}

/**
 * Check if a field name represents a standard contact field
 */
function isStandardContactField(fieldName: string): boolean {
	const normalizedFieldName = normalizeFieldName(fieldName);
	return STANDARD_CONTACT_FIELDS.some(
		(standardField) =>
			normalizeFieldName(standardField) === normalizedFieldName,
	);
}

/**
 * Transform boolean field value to Yes/No format for AutoPatient
 *
 * Converts various boolean representations from CliniCore to standardized
 * Yes/No format expected by AutoPatient RADIO fields.
 *
 * @param value - The field value to transform
 * @param fieldType - The CC field type to determine if transformation is needed
 * @returns Transformed value ("Yes"/"No" for boolean fields, original value otherwise)
 *
 * @example
 * ```typescript
 * transformBooleanValue("true", "boolean")   // Returns "Yes"
 * transformBooleanValue("false", "boolean")  // Returns "No"
 * transformBooleanValue("1", "boolean")      // Returns "Yes"
 * transformBooleanValue("0", "boolean")      // Returns "No"
 * transformBooleanValue("text", "string")    // Returns "text" (unchanged)
 * ```javascript
*/
function transformBooleanValue(value: string, fieldType: string): string {
	const normalizedType = fieldType.toLowerCase().trim();

	if (normalizedType === "boolean") {
		const normalizedValue = value.toLowerCase().trim();

		// Handle various boolean representations
		if (normalizedValue === "true" || normalizedValue === "1" || normalizedValue === "yes") {
			return "Yes";
		} else if (normalizedValue === "false" || normalizedValue === "0" || normalizedValue === "no") {
			return "No";
		}

		// Default to "No" for any other value
		return "No";
	}

	// Return original value for non-boolean fields
	return value;
}

/**
 * Map CC custom field type to AP data type with enhanced logic
 *
 * Provides intelligent mapping between CliniCore and AutoPatient field types,
 * with special handling for boolean fields (mapped to RADIO with Yes/No options)
 * and select fields with multiple values.
 *
 * @param ccField - CliniCore custom field definition containing type and properties
 * @returns AutoPatient data type string (e.g., "RADIO", "MULTIPLE_OPTIONS", "TEXT")
 *
 * @example
 *
```typescript
 * // Boolean field mapping
 * mapCcToApDataType({type: "boolean", ...}) // Returns "RADIO"
 *
 * // Multi-select field mapping
 * mapCcToApDataType({type: "select", allowMultipleValues: true, ...}) // Returns "MULTIPLE_OPTIONS"
 *
 * // Standard text field mapping
 * mapCcToApDataType({type: "text", ...}) // Returns "TEXT"
 * ```javascript
*/
function mapCcToApDataType(ccField: GetCCCustomField): string {
	const normalizedType = ccField.type.toLowerCase().trim();

	// Handle special mapping cases based on field type and properties
	if (normalizedType === "boolean") {
		// Map boolean fields to RADIO with Yes/No options
		return "RADIO";
	}

	if (normalizedType === "select" && ccField.allowMultipleValues === true) {
		return "MULTIPLE_OPTIONS";
	}

	// Use standard mapping for other cases
	return (
		CC_TO_AP_DATA_TYPE_MAPPING[normalizedType] ||
		CC_TO_AP_DATA_TYPE_MAPPING.default
	);
}

/**
 * Check if a CC custom field should be mapped to an AP standard field
 * Returns the AP standard field name if mapping exists, null otherwise
 */
function getStandardFieldMapping(
	ccFieldName: string,
	ccFieldLabel: string,
): string | null {
	const normalizedName = normalizeFieldName(ccFieldName);
	const normalizedLabel = normalizeFieldName(ccFieldLabel);

	// Check both field name and label against the mapping
	for (const [ccFieldPattern, apStandardField] of Object.entries(
		CC_TO_AP_STANDARD_FIELD_MAPPING,
	)) {
		const normalizedPattern = normalizeFieldName(ccFieldPattern);
		if (
			normalizedPattern === normalizedName ||
			normalizedPattern === normalizedLabel
		) {
			return apStandardField as string;
		}
	}

	return null;
}

/**
 * Extract standard field mappings from CC custom fields
 * Returns a map of AP standard field names to their values
 */
function extractStandardFieldMappings(
	ccPatientCustomFields: GetCCPatientCustomField[],
	requestId: string,
): Record<string, string> {
	const standardMappings: Record<string, string> = {};

	logInfo(
		requestId,
		`Extracting standard field mappings from ${ccPatientCustomFields.length} CC custom fields`,
	);

	for (const ccCustomField of ccPatientCustomFields) {
		const fieldName = ccCustomField.field.name;
		const fieldLabel = ccCustomField.field.label;

		// Check if this CC custom field should map to an AP standard field
		const apStandardField = getStandardFieldMapping(fieldName, fieldLabel);

		if (apStandardField) {
			// Extract field value
			const fieldValue = extractFieldValues(ccCustomField);

			if (fieldValue && fieldValue.trim() !== "") {
				// Handle multiple mappings to the same standard field - prioritize non-empty values
				if (
					!standardMappings[apStandardField] ||
					standardMappings[apStandardField].trim() === ""
				) {
					standardMappings[apStandardField] = fieldValue.trim();
					logDebug(
						requestId,
						`Standard field mapping: "${fieldName}" (${fieldLabel}) -> AP.${apStandardField} = "${fieldValue.substring(
							0,
							50,
						)}${fieldValue.length > 50 ? "..." : ""}"`,
					);
				} else {
					logDebug(
						requestId,
						`Skipping duplicate standard field mapping: "${fieldName}" (${fieldLabel}) -> AP.${apStandardField} (already mapped)`,
					);
				}
			} else {
				logDebug(
					requestId,
					`Skipping standard field mapping for empty value: ${fieldName} (${fieldLabel})`,
				);
			}
		}
	}

	logInfo(
		requestId,
		`Extracted ${
			Object.keys(standardMappings).length
		} standard field mappings`,
	);
	return standardMappings;
}

/**
 * Extract and convert CC allowed values to AP textBoxListOptions format
 *
 * Processes CliniCore custom field allowed values and converts them to the format
 * expected by AutoPatient custom fields. Ensures the current field value is included
 * as an option even if not in the original allowed values list.
 *
 * @param ccField - CC custom field with allowedValues array
 * @param currentValue - Current field value to include as an option if not in allowedValues
 * @param requestId - Request ID for logging correlation
 * @returns Array of option strings for AP custom field creation
 *
 * @example
 *
```typescript
 * const options = extractTextBoxListOptions(
 *   { allowedValues: [{value: "Option1"}, {value: "Option2"}] },
 *   "Option3",
 *   "req-123"
 * );
 * // Returns: ["Option1", "Option2", "Option3"]
 * ```
 */
function extractTextBoxListOptions(
	ccField: GetCCCustomField,
	currentValue: string,
	requestId: string,
): string[] {
	const options: string[] = [];

	logDebug(
		requestId,
		`Extracting options for field ${ccField.label} (type: ${ccField.type})`,
	);
	logDebug(
		requestId,
		`CC allowedValues: ${JSON.stringify(ccField.allowedValues, null, 2)}`,
	);

	// Extract allowed values from CC field
	if (ccField.allowedValues && ccField.allowedValues.length > 0) {
		for (const allowedValue of ccField.allowedValues) {
			if (allowedValue.value && allowedValue.value.trim() !== "") {
				const trimmedValue = allowedValue.value.trim();
				options.push(trimmedValue);
				logDebug(requestId, `Added option: "${trimmedValue}"`);
			}
		}
	}

	// Ensure current value is included as an option if it's not already present
	if (currentValue && currentValue.trim() !== "") {
		const currentValueTrimmed = currentValue.trim();
		const existingOption = options.find(
			(option) => option.toLowerCase() === currentValueTrimmed.toLowerCase(),
		);

		if (!existingOption) {
			options.push(currentValueTrimmed);
			logDebug(
				requestId,
				`Added current value as option: "${currentValueTrimmed}"`,
			);
		}
	}

	// If no options were found, create a default option based on current value
	if (options.length === 0 && currentValue && currentValue.trim() !== "") {
		const trimmedValue = currentValue.trim();
		options.push(trimmedValue);
		logDebug(requestId, `Created default option: "${trimmedValue}"`);
	}

	return options;
}

/**
 * Extract and combine multiple values from CC custom field
 * Handles different separation strategies based on field type
 */
function extractFieldValues(ccCustomField: GetCCPatientCustomField): string {
	if (!ccCustomField.values || ccCustomField.values.length === 0) {
		return "";
	}

	const values = ccCustomField.values
		.map((v) => v.value)
		.filter((v): v is string => v != null && v.trim() !== "");

	if (values.length === 0) {
		return "";
	}

	// For single value, return as-is
	if (values.length === 1) {
		return values[0];
	}

	// For multiple values, choose separation strategy based on field type
	const fieldType = ccCustomField.field.type?.toLowerCase() || "";

	if (fieldType.includes("multiselect") || fieldType.includes("checkbox")) {
		// Use comma separation for multi-select fields
		return values.join(", ");
	} else if (fieldType.includes("textarea") || fieldType.includes("text")) {
		// Use newline separation for text areas
		return values.join("\n");
	} else {
		// Default to comma separation
		return values.join(", ");
	}
}

/**
 * Synchronize custom fields from CC patient to AP contact
 *
 * @param requestId - Request ID from Hono context for logging correlation
 * @param localPatientId - Local database patient ID for logging context
 * @param ccPatientData - CC patient data containing custom field IDs
 * @param apContactId - AP contact ID to update with custom fields
 * @returns Promise<void> - Completes sync or throws error
 */
export async function syncCcToApCustomFields(
	requestId: string,
	localPatientId: string,
	ccPatientData: GetCCPatientType,
	apContactId: string,
): Promise<void> {
	logProcessingStep(
		requestId,
		`Starting custom field sync for CC Patient ${ccPatientData.id} -> AP Contact ${apContactId} (Local Patient ID: ${localPatientId})`,
	);

	// Step 1: Check if patient has custom fields
	if (!ccPatientData.customFields || ccPatientData.customFields.length === 0) {
		logInfo(
			requestId,
			`No custom fields found for CC patient ${ccPatientData.id}`,
		);
		return;
	}

	try {
		// Step 2: Fetch CC patient custom field data
		logInfo(
			requestId,
			`Fetching ${ccPatientData.customFields.length} custom fields from CC`,
		);
		const ccPatientCustomFields = await patientReq.customFields(
			ccPatientData.customFields,
		);

		if (!ccPatientCustomFields || ccPatientCustomFields.length === 0) {
			logInfo(requestId, `No custom field data returned from CC`);
			return;
		}

		// Step 3: Extract and apply standard field mappings
		const standardFieldMappings = extractStandardFieldMappings(
			ccPatientCustomFields,
			requestId,
		);

		// Build update payload for standard fields
		const standardFieldUpdate: Partial<PostAPContactType> = {};

		// Apply standard field mappings to AP contact if any exist
		if (Object.keys(standardFieldMappings).length > 0) {
			logInfo(
				requestId,
				`Applying ${
					Object.keys(standardFieldMappings).length
				} standard field mappings to AP contact`,
			);

			for (const [fieldName, value] of Object.entries(standardFieldMappings)) {
				if (fieldName === "phone") {
					standardFieldUpdate.phone = value;
				} else if (fieldName === "email") {
					standardFieldUpdate.email = value;
				}
				// Add more standard field mappings as needed
			}
		}

		// Step 4: Filter out excluded fields and extract valid custom field mappings
		const validCustomFields = await filterAndMapCustomFields(
			ccPatientCustomFields,
			requestId,
		);

		if (validCustomFields.length === 0) {
			logInfo(
				requestId,
				`No valid custom fields to sync after filtering`,
			);
			return;
		}

		// Step 4: Get all AP custom fields for mapping (force cache refresh)
		logInfo(requestId, `Fetching AP custom fields for mapping (forcing cache refresh)`);
		const apCustomFields = await apCustomfield.all(true); // Force cache invalidation

		// Validate that we received field data
		if (apCustomFields.length === 0) {
			logError(requestId, "No AP custom fields returned from API - this may indicate an API issue or empty location");
			logInfo(requestId, "Proceeding with empty field list - new fields will be created as needed");
		} else {
			logInfo(requestId, `Successfully loaded ${apCustomFields.length} AP custom fields from API`);

			// Validate the loaded fields
			const validation = validateApFieldsCache(apCustomFields, requestId);
			if (!validation.isValid) {
				logError(requestId, `AP fields cache validation failed: ${validation.issues.join(", ")}`);
			}

			// Log sample of loaded fields for debugging
			const sampleFields = apCustomFields.slice(0, 5);
			logDebug(
				requestId,
				`Sample AP fields: ${sampleFields.map(f => `"${f.name}" (fieldKey: "${f.fieldKey || 'undefined'}")`).join(", ")}${apCustomFields.length > 5 ? "..." : ""}`
			);

			// Log field distribution for debugging
			const fieldKeyStats = {
				withPrefix: apCustomFields.filter(f => f.fieldKey?.startsWith("contact.")).length,
				withoutPrefix: apCustomFields.filter(f => f.fieldKey && !f.fieldKey.startsWith("contact.")).length,
				withUnicode: apCustomFields.filter(f => /[^\x00-\x7F]/.test(f.name || "")).length,
				withSpecialChars: apCustomFields.filter(f => /[^a-zA-Z0-9\s]/.test(f.name || "")).length,
			};
			logDebug(
				requestId,
				`Field distribution: ${fieldKeyStats.withPrefix} with "contact." prefix, ${fieldKeyStats.withoutPrefix} without prefix, ${fieldKeyStats.withUnicode} with Unicode, ${fieldKeyStats.withSpecialChars} with special chars`
			);
		}

		// Step 5: Map CC fields to AP format and create missing fields
						requestId,
								`Successfully found existing AP custom field: "${apCustomField.name}" (ID: ${apCustomField.id}, fieldKey: "${apCustomField.fieldKey}")`,
							);
							existingCount++;
						} else {
							logError(
								requestId,
								`Could not find existing field after duplicate error. CC field: "${field.name}", generated fieldKey: "${fieldKey}", error: "${errorMessage}"`,
							);
							logError(
								requestId,
								`Available AP fields: ${apCustomFields.map(f => `"${f.name}" (fieldKey: "${f.fieldKey}")`).slice(0, 10).join(", ")}${apCustomFields.length > 10 ? "..." : ""}`,
							);
							throw createError;
						}
					} else {
						throw createError;
					}
				}
			} else {
				logDebug(
					requestId,
					`Using existing AP custom field: "${apCustomField.name}" with ID: ${apCustomField.id}`,
				);
				existingCount++;
			}

			// Transform value for boolean fields
			const transformedValue = transformBooleanValue(value, field.type);

			mappings.push({
				id: apCustomField.id,
				value: transformedValue,
			});

			logDebug(
				requestId,
				`Mapped: "${field.label}" -> AP Field ID ${
					apCustomField.id
				} (${transformedValue.substring(0, 50)}${transformedValue.length > 50 ? "..." : ""})${
					transformedValue !== value ? ` [transformed from: ${value}]` : ""
				}`,
			);
		} catch (error) {
			logError(
				requestId,
				`Failed to map field "${field.name}":`,
				error,
			);
			// Continue with other fields rather than failing completely
		}
	}

	logInfo(
		requestId,
		`Custom field mapping completed: ${mappings.length} total mappings (${createdCount} created, ${existingCount} existing)`,
	);

	return mappings;
}
