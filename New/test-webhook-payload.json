{"event": "EntityWasCreated", "model": "Patient", "id": 1766, "payload": {"id": 1766, "createdAt": "2025-07-25T22:44:33.000Z", "updatedAt": "2025-07-25T22:44:33.000Z", "createdBy": 5003, "updatedBy": 5003, "firstName": "Labore possimus con", "lastName": "<PERSON><PERSON><PERSON> esse alias", "dob": "1930-01-28T00:00:00.000Z", "ssn": "Qui id necessitatibu", "flashMessage": "", "active": true, "phoneMobile": "+880 1750-690455", "phonePersonal": null, "phoneBusiness": null, "email": "<EMAIL>", "title": null, "titleSuffix": null, "healthInsurance": null, "gender": "<PERSON><PERSON><PERSON><PERSON>", "addresses": [{"id": 1759, "label": null, "name": null, "street": "Pariatur Dicta lore", "streetNumber": "Illum eum ipsam qui", "postalCode": "A", "city": "Voluptas eiusmod id", "country": "CM", "primary": 1}], "categories": [14], "customFields": [11315, 11316, 11317, 11318, 11319, 11320], "invoices": [], "payments": [], "files": [], "history": [], "appointments": [], "messages": [], "medications": [], "personalWebForms": [], "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1766.png", "avatarUrl": null}, "timestamp": "2025-07-25T22:44:33.278Z"}