{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "skipLibCheck": true, "lib": ["ESNext"], "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@database": ["./src/database"], "@database/*": ["./src/database/*"], "@config": ["./src/utils/configs"], "@utils": ["./src/utils"], "@utils/*": ["./src/utils/*"], "@type": ["./src/type"], "@type/*": ["./src/type/*"]}}, "exclude": ["old"]}