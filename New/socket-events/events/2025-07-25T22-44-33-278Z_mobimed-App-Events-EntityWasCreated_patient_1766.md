# mobimed:App\Events\EntityWasCreated

## Event Information

- **Event Type**: mobimed:App\Events\EntityWasCreated
- **Model**: patient
- **Event ID**: 1766
- **Timestamp**: 2025-07-25T22:44:33.279Z
- **Webhook Event**: EntityWasCreated
- **Webhook Model**: Patient

## Original Socket Event Data

```json
{
  "type": "patient",
  "payload": {
    "id": 1766,
    "createdAt": "2025-07-25T22:44:33.000Z",
    "updatedAt": "2025-07-25T22:44:33.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "Labore possimus con",
    "lastName": "Voluptas esse alias",
    "dob": "1930-01-28T00:00:00.000Z",
    "ssn": "Qui id necessitatibu",
    "flashMessage": "",
    "active": true,
    "phoneMobile": "+880 1750-690455",
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": "<EMAIL>",
    "title": null,
    "titleSuffix": null,
    "healthInsurance": null,
    "gender": "männlich",
    "addresses": [
      {
        "id": 1759,
        "label": null,
        "name": null,
        "street": "Pariatur Dicta lore",
        "streetNumber": "Illum eum ipsam qui",
        "postalCode": "A",
        "city": "Voluptas eiusmod id",
        "country": "CM",
        "primary": 1
      }
    ],
    "categories": [
      14
    ],
    "customFields": [
      11315,
      11316,
      11317,
      11318,
      11319,
      11320
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1766.png",
    "avatarUrl": null
  },
  "socket": null
}
```

## Converted Webhook Data

```json
{
  "event": "EntityWasCreated",
  "model": "Patient",
  "id": 1766,
  "payload": {
    "id": 1766,
    "createdAt": "2025-07-25T22:44:33.000Z",
    "updatedAt": "2025-07-25T22:44:33.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "Labore possimus con",
    "lastName": "Voluptas esse alias",
    "dob": "1930-01-28T00:00:00.000Z",
    "ssn": "Qui id necessitatibu",
    "flashMessage": "",
    "active": true,
    "phoneMobile": "+880 1750-690455",
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": "<EMAIL>",
    "title": null,
    "titleSuffix": null,
    "healthInsurance": null,
    "gender": "männlich",
    "addresses": [
      {
        "id": 1759,
        "label": null,
        "name": null,
        "street": "Pariatur Dicta lore",
        "streetNumber": "Illum eum ipsam qui",
        "postalCode": "A",
        "city": "Voluptas eiusmod id",
        "country": "CM",
        "primary": 1
      }
    ],
    "categories": [
      14
    ],
    "customFields": [
      11315,
      11316,
      11317,
      11318,
      11319,
      11320
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1766.png",
    "avatarUrl": null
  },
  "timestamp": "2025-07-25T22:44:33.278Z"
}
```

## Event Processing

This event was received from the CC socket server and converted to webhook format for processing by the DermaCare sync system.

### Processing Flow
1. **Socket Event Received**: mobimed:App\Events\EntityWasCreated
2. **Data Extraction**: Extracted model type and payload data
3. **Webhook Conversion**: Converted to webhook format compatible with ccHandler.ts
4. **Webhook Transmission**: Sent to dev server at http://localhost:8787/cc/webhook/

---
*Generated by DermaCare Socket Event Logger*
