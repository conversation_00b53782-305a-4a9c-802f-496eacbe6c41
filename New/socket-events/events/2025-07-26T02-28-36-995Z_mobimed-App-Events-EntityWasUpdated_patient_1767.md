# mobimed:App\Events\EntityWasUpdated

## Event Information

- **Event Type**: mobimed:App\Events\EntityWasUpdated
- **Model**: patient
- **Event ID**: 1767
- **Timestamp**: 2025-07-26T02:28:36.995Z
- **Webhook Event**: EntityWasUpdated
- **Webhook Model**: Patient

## Original Socket Event Data

```json
{
  "type": "patient",
  "payload": {
    "id": 1767,
    "createdAt": "2025-07-26T01:09:12.000Z",
    "updatedAt": "2025-07-26T01:09:12.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "Praesentium sunt ape",
    "lastName": "Minus enim dolore ni",
    "dob": "1993-09-16T00:00:00.000Z",
    "ssn": "Quidem quae magnam a",
    "flashMessage": "",
    "active": true,
    "phoneMobile": "+880 1750-680133",
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": "<EMAIL>",
    "title": null,
    "titleSuffix": null,
    "healthInsurance": null,
    "gender": "männlich",
    "addresses": [
      {
        "id": 1760,
        "label": null,
        "name": null,
        "street": "Labore ad dicta ad a",
        "streetNumber": "Molestias qui adipis",
        "postalCode": "Quis",
        "city": "Minima molestias eos",
        "country": "PF",
        "primary": 1
      }
    ],
    "categories": [
      15
    ],
    "customFields": [
      11321,
      11322,
      11323,
      11324,
      11325,
      11326,
      11327,
      11328,
      11329,
      11330,
      11331,
      11332,
      11333,
      11334,
      11335,
      11336,
      11337
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [
      1112
    ],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1767.png",
    "avatarUrl": null
  },
  "socket": null
}
```

## Converted Webhook Data

```json
{
  "event": "EntityWasUpdated",
  "model": "Patient",
  "id": 1767,
  "payload": {
    "id": 1767,
    "createdAt": "2025-07-26T01:09:12.000Z",
    "updatedAt": "2025-07-26T01:09:12.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "Praesentium sunt ape",
    "lastName": "Minus enim dolore ni",
    "dob": "1993-09-16T00:00:00.000Z",
    "ssn": "Quidem quae magnam a",
    "flashMessage": "",
    "active": true,
    "phoneMobile": "+880 1750-680133",
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": "<EMAIL>",
    "title": null,
    "titleSuffix": null,
    "healthInsurance": null,
    "gender": "männlich",
    "addresses": [
      {
        "id": 1760,
        "label": null,
        "name": null,
        "street": "Labore ad dicta ad a",
        "streetNumber": "Molestias qui adipis",
        "postalCode": "Quis",
        "city": "Minima molestias eos",
        "country": "PF",
        "primary": 1
      }
    ],
    "categories": [
      15
    ],
    "customFields": [
      11321,
      11322,
      11323,
      11324,
      11325,
      11326,
      11327,
      11328,
      11329,
      11330,
      11331,
      11332,
      11333,
      11334,
      11335,
      11336,
      11337
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [
      1112
    ],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1767.png",
    "avatarUrl": null
  },
  "timestamp": "2025-07-26T02:28:36.995Z"
}
```

## Event Processing

This event was received from the CC socket server and converted to webhook format for processing by the DermaCare sync system.

### Processing Flow
1. **Socket Event Received**: mobimed:App\Events\EntityWasUpdated
2. **Data Extraction**: Extracted model type and payload data
3. **Webhook Conversion**: Converted to webhook format compatible with ccHandler.ts
4. **Webhook Transmission**: Sent to dev server at http://localhost:8787/webhooks/cc

---
*Generated by DermaCare Socket Event Logger*
