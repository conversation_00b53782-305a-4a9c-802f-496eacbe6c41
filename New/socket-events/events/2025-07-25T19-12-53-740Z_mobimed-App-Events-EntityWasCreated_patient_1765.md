# mobimed:App\Events\EntityWasCreated

## Event Information

- **Event Type**: mobimed:App\Events\EntityWasCreated
- **Model**: patient
- **Event ID**: 1765
- **Timestamp**: 2025-07-25T19:12:53.741Z
- **Webhook Event**: EntityWasCreated
- **Webhook Model**: Patient

## Original Socket Event Data

```json
{
  "type": "patient",
  "payload": {
    "id": 1765,
    "createdAt": "2025-07-25T19:12:52.000Z",
    "updatedAt": "2025-07-25T19:12:52.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "Sint mollit consequa",
    "lastName": "Voluptatem dolorem q",
    "dob": "1942-10-12T00:00:00.000Z",
    "ssn": "Nisi aperiam dolores",
    "flashMessage": "",
    "active": true,
    "phoneMobile": "",
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": "<EMAIL>",
    "title": null,
    "titleSuffix": null,
    "healthInsurance": null,
    "gender": "weiblich",
    "addresses": [
      {
        "id": 1758,
        "label": null,
        "name": null,
        "street": "Dolores non rerum vo",
        "streetNumber": "Obcaecati exercitati",
        "postalCode": "Magni",
        "city": "Molestiae blanditiis",
        "country": "KH",
        "primary": 1
      }
    ],
    "categories": [],
    "customFields": [
      11310,
      11311,
      11312,
      11313,
      11314
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1765.png",
    "avatarUrl": null
  },
  "socket": null
}
```

## Converted Webhook Data

```json
{
  "event": "EntityWasCreated",
  "model": "Patient",
  "id": 1765,
  "payload": {
    "id": 1765,
    "createdAt": "2025-07-25T19:12:52.000Z",
    "updatedAt": "2025-07-25T19:12:52.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "Sint mollit consequa",
    "lastName": "Voluptatem dolorem q",
    "dob": "1942-10-12T00:00:00.000Z",
    "ssn": "Nisi aperiam dolores",
    "flashMessage": "",
    "active": true,
    "phoneMobile": "",
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": "<EMAIL>",
    "title": null,
    "titleSuffix": null,
    "healthInsurance": null,
    "gender": "weiblich",
    "addresses": [
      {
        "id": 1758,
        "label": null,
        "name": null,
        "street": "Dolores non rerum vo",
        "streetNumber": "Obcaecati exercitati",
        "postalCode": "Magni",
        "city": "Molestiae blanditiis",
        "country": "KH",
        "primary": 1
      }
    ],
    "categories": [],
    "customFields": [
      11310,
      11311,
      11312,
      11313,
      11314
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1765.png",
    "avatarUrl": null
  },
  "timestamp": "2025-07-25T19:12:53.740Z"
}
```

## Event Processing

This event was received from the CC socket server and converted to webhook format for processing by the DermaCare sync system.

### Processing Flow
1. **Socket Event Received**: mobimed:App\Events\EntityWasCreated
2. **Data Extraction**: Extracted model type and payload data
3. **Webhook Conversion**: Converted to webhook format compatible with ccHandler.ts
4. **Webhook Transmission**: Sent to dev server at http://localhost:8787/cc/webhook/

---
*Generated by DermaCare Socket Event Logger*
