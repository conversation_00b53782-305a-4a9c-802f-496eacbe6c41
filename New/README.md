# 🏥 DermaCare Data Sync Service

A high-performance, bi-directional data synchronization service built on Cloudflare Workers for seamless integration between CliniCore (CC) and AutoPatient (AP) medical practice management platforms.

## 🌟 Key Features

- **Bi-directional Sync**: Real-time data synchronization between CliniCore and AutoPatient platforms
- **Webhook Architecture**: HTTP-based event processing with 25-second completion target
- **Cloudflare Workers**: Serverless architecture for global scalability and low latency
- **Type Safety**: Full TypeScript implementation with strict typing and comprehensive JSDoc
- **Performance Optimized**: Advanced caching, serverless database connections, and query optimization
- **Error Resilience**: Comprehensive error handling with exponential backoff retry mechanisms
- **API-First Testing**: Real API integration testing using actual CC and AP endpoints

## 🏗️ Architecture

Built on modern serverless architecture using:
- **Runtime**: Cloudflare Workers with Hono framework
- **Database**: PostgreSQL with Neon serverless driver (no connection pooling)
- **ORM**: Drizzle ORM with TypeScript schema definitions
- **Caching**: Cloudflare KV storage for cross-request data persistence
- **Configuration**: Centralized configuration management in TypeScript

### Data Flow

1. **CC → AP Sync**: CliniCore webhooks trigger patient/appointment sync to AutoPatient
2. **AP → CC Sync**: AutoPatient webhooks trigger contact/appointment sync to CliniCore
3. **Processing**: Service validates, transforms, and routes data between platforms
4. **API Integration**: Direct API calls to target platforms for data operations
5. **Caching**: Intelligent caching prevents duplicate operations and improves performance

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and pnpm package manager
- Cloudflare account with Workers enabled
- PostgreSQL database (Neon recommended for serverless compatibility)
- CliniCore API credentials
- AutoPatient (GoHighLevel) API credentials

### Installation

1. **Clone and Install Dependencies**
   ```bash
   git clone <repository-url>
   cd DermaCare/Try/New
   pnpm install
   ```

2. **Configure Application**
   ```typescript
   // Edit src/utils/configs.ts with your API keys and database URL
   const configs: AppConfigs = {
     databaseUrl: "postgresql://your-neon-database-url",
     ccApiDomain: "https://ccdemo.clinicore.eu/api/v1",
     ccApiKey: "your-clinicore-api-key",
     apApiDomain: "https://services.leadconnectorhq.com",
     apApiKey: "your-autopatient-api-key",
     locationID: "your-location-id",
     apCalendarId: "your-calendar-id",
     // ... other configuration options
   };
   ```

3. **Initialize Database**
   ```bash
   pnpm db:sync
   ```

4. **Start Development Server**
   ```bash
   pnpm dev
   ```

5. **Start with ngrok Tunnel** (for webhook testing)
   ```bash
   pnpm dev:tunnel
   ```

6. **Start with Socket Event Converter** (for development testing)
   ```bash
   # Terminal 1: Start dev server
   pnpm dev

   # Terminal 2: Start socket event converter
   pnpm socket-events
   ```

7. **Start with ngrok Tunnel** (for webhook testing)
   ```bash
   # Terminal 1: Start dev server
   pnpm dev

   # Terminal 2: Start ngrok tunnel
   pnpm ngrok
   ```

Your service will be available at:
- **Local**: `http://localhost:8787`
- **ngrok**: `https://proven-moose-hopefully.ngrok-free.app`

> **Note**: For the best development experience, run each service (dev server, ngrok tunnel, socket event converter) in separate terminal windows. This provides better process isolation and easier debugging.

## 📦 Available Scripts

| Script | Description |
|--------|-------------|
| `pnpm dev` | Start development server on port 8787 |
| `pnpm dev:events` | Show instructions for running dev server + socket events |
| `pnpm dev:tunnel` | Show instructions for running dev server + ngrok tunnel |
| `pnpm dev:full` | Show instructions for running full development environment |
| `pnpm socket-events` | Run socket event converter standalone |
| `pnpm ngrok` | Start ngrok tunnel to proven-moose-hopefully.ngrok-free.app |
| `pnpm deploy` | Deploy to Cloudflare Workers (production) |
| `pnpm deploy:staging` | Deploy to staging environment |
| `pnpm deploy:production` | Deploy to production with minification |
| `pnpm db:sync` | Generate and push database schema |
| `pnpm db:generate` | Generate database migrations |
| `pnpm db:push` | Push schema changes to database |
| `pnpm db:studio` | Open Drizzle Studio for database management |
| `pnpm test` | Run webhook tests locally |
| `pnpm test:ngrok` | Run webhook tests via ngrok tunnel |
| `pnpm test:performance` | Run performance tests |
| `pnpm test:stress` | Run stress tests |
| `pnpm lint` | Check code with Biome linter |
| `pnpm lint:fix` | Fix linting issues automatically |
| `pnpm type-check` | Run TypeScript type checking |
| `pnpm clean` | Clean build artifacts |
| `pnpm setup` | Install dependencies and sync database |

## 🔌 API Endpoints

### Health Check Endpoints

- **GET /** - Basic health check
  ```bash
  curl http://localhost:8787/
  # Response: "DermaCare Data Sync Service - OK"
  ```

- **GET /health** - Detailed health check with metrics
  ```bash
  curl http://localhost:8787/health
  # Returns: performance stats, API status, cache metrics, buffer status
  ```

### Webhook Endpoints

- **POST /webhook** - CliniCore webhook events
  ```bash
  curl -X POST http://localhost:8787/webhook \
    -H "Content-Type: application/json" \
    -d '{"event": "EntityWasCreated", "model": "Patient", "id": 123, "payload": {...}}'
  ```

- **POST /ap/contact** - AutoPatient contact webhooks
  ```bash
  curl -X POST http://localhost:8787/ap/contact \
    -H "Content-Type: application/json" \
    -d '{"event": "ContactCreated", "id": "contact_123", "payload": {...}}'
  ```

- **POST /ap/appointment** - AutoPatient appointment webhooks
  ```bash
  curl -X POST http://localhost:8787/ap/appointment \
    -H "Content-Type: application/json" \
    -d '{"event": "AppointmentCreated", "id": "appointment_123", "payload": {...}}'
  ```

- **POST /sync-services** - Legacy sync services endpoint (placeholder)

## 🔌 Socket Event Converter (Development Tool)

The `dev-event.js` file is a development utility that bridges the gap between the old socket.io-based system and the new webhook-based architecture. It connects to the CC socket server and converts socket events to webhook format for testing.

### Setup Socket Event Converter

1. **Configure CC Socket Credentials**
   ```javascript
   // Edit New/dev-event.js and update the CONFIG object:
   const CONFIG = {
     // ... other config
     ccCredentials: {
       name: 'DermaCare Dev',
       CCSocketUrl: 'wss://your-actual-domain.com/socket.io', // Replace with your actual socket URL
       CCSocketToken: 'your-actual-socket-token', // Replace with your actual socket token
       CCApiUrl: 'https://your-actual-api-domain.com' // Replace with your actual API URL
     }
   };
   ```

2. **Install Dependencies**
   ```bash
   pnpm install
   ```

3. **Run Socket Event Converter**
   ```bash
   # Terminal 1: Start dev server
   pnpm dev

   # Terminal 2: Start socket event converter
   pnpm socket-events
   ```

### Configuration

All configuration is done directly in the `dev-event.js` file by updating the `CONFIG.ccCredentials` object:

| Field | Required | Description |
|-------|----------|-------------|
| `CCSocketUrl` | ✅ | WebSocket URL for CC socket server (e.g., `wss://your-domain.com/socket.io`) |
| `CCSocketToken` | ✅ | Authentication token for socket connection |
| `name` | ❌ | Name identifier for the connection (default: `DermaCare Dev`) |
| `CCApiUrl` | ❌ | API URL for reference |

### Supported Socket Events

The converter listens to all socket events from the original v3Integration system:

- `mobimed:App\\Events\\EntityWasCreated` → `EntityWasCreated`
- `mobimed:App\\Events\\EntityWasUpdated` → `EntityWasUpdated`
- `mobimed:App\\Events\\EntityWasDeleted` → `EntityWasDeleted`
- `mobimed:App\\Events\\AppointmentWasCreated` → `AppointmentWasCreated`

### Event Conversion

Socket events are automatically converted to webhook format:

```javascript
// Socket Event (Input)
{
  type: "patient",
  payload: { id: 123, firstName: "John", ... }
}

// Webhook Event (Output)
{
  event: "EntityWasCreated",
  model: "Patient",
  id: 123,
  payload: { id: 123, firstName: "John", ... },
  timestamp: "2024-01-01T12:00:00.000Z"
}
```

## ⚙️ Configuration Management

All configuration is centralized in `src/utils/configs.ts`. No environment files are used.

### Configuration Structure

```typescript
interface AppConfigs {
  // Database Configuration
  databaseUrl: string;

  // API Configuration
  ccApiDomain: string;
  ccApiKey: string;
  apApiDomain: string;
  apApiKey: string;
  locationID: string;
  apCalendarId: string;

  // Performance Configuration
  performance: {
    totalRequestTimeout: number;
    databaseQueryTimeout: number;
    apiCallTimeout: number;
    memoryLimit: number;
    // ... other performance settings
  };

  // Cache Configuration
  cacheTTLs: {
    patientData: number;
    appointmentData: number;
    customFields: number;
    // ... other cache settings
  };

  // Retry Configuration
  retry: {
    maxAttempts: number;
    baseDelay: number;
    maxDelay: number;
    timeoutMs: number;
  };

  // Error Logging Configuration
  errorLogging: {
    duplicateWindowSeconds: number;
    enableDatabaseLogging: boolean;
    enableConsoleLogging: boolean;
  };
}
```

### Modifying Configuration

To change settings, edit the `configs` object in `src/utils/configs.ts`:

```typescript
const configs: AppConfigs = {
  databaseUrl: "your-database-url",
  ccApiKey: "your-cc-api-key",
  apApiKey: "your-ap-api-key",
  // ... update other values as needed
};
```

## 🧪 Testing

The project includes a comprehensive testing system using real API endpoints.

### Webhook Testing

The `webhook-tester.js` script creates real records in CC and AP systems and tests webhook processing:

```bash
# Test all webhooks locally
pnpm test

# Test via ngrok tunnel
pnpm test:ngrok

# Performance testing
pnpm test:performance

# Stress testing
pnpm test:stress
```

### Test Environments

- **local** - Test against local development server
- **ngrok** - Test via ngrok tunnel (for webhook callbacks)
- **staging** - Test against staging environment
- **production** - Test against production environment

### Test Suites

- **all** - Run all test suites
- **cc-webhooks** - Test CliniCore webhook processing
- **ap-webhooks** - Test AutoPatient webhook processing
- **error-scenarios** - Test error handling
- **edge-cases** - Test edge case scenarios
- **health-check** - Test health endpoints
- **stress** - Stress and performance tests

### Example Test Commands

```bash
# Run all tests locally
node webhook-tester.js local all

# Test CC webhooks via ngrok
node webhook-tester.js ngrok cc-webhooks

# Performance test with cleanup
node webhook-tester.js local all --performance --cleanup
```

## 🚀 Deployment

### Cloudflare Workers Deployment

1. **Configure Wrangler**
   ```bash
   # wrangler.jsonc is already configured
   # Update name and KV namespace ID if needed
   ```

2. **Deploy to Staging**
   ```bash
   pnpm deploy:staging
   ```

3. **Deploy to Production**
   ```bash
   pnpm deploy:production
   ```

### Environment-Specific Configuration

For different environments, update the configuration values in `src/utils/configs.ts`:

```typescript
// For staging
const configs: AppConfigs = {
  databaseUrl: "postgresql://staging-database-url",
  ccApiKey: "staging-cc-api-key",
  apApiKey: "staging-ap-api-key",
  // ... other staging values
};

// For production
const configs: AppConfigs = {
  databaseUrl: "postgresql://production-database-url",
  ccApiKey: "production-cc-api-key",
  apApiKey: "production-ap-api-key",
  // ... other production values
};
```

## 📁 Project Structure

```
New/
├── src/
│   ├── api/                    # API client implementations
│   │   ├── apClient.ts         # AutoPatient API client
│   │   ├── ccClient.ts         # CliniCore API client
│   │   ├── optimizedClient.ts  # Performance-optimized API utilities
│   │   ├── request.ts          # Base API request utilities
│   │   └── index.ts            # API exports
│   ├── database/               # Database layer
│   │   ├── schema.ts           # Drizzle ORM schema definitions
│   │   ├── optimizedQueries.ts # Performance-optimized queries
│   │   ├── migrations/         # Database migrations
│   │   └── index.ts            # Database exports
│   ├── helpers/                # Helper utilities
│   │   ├── customFields.ts     # Custom field mapping logic
│   │   ├── dataTransform.ts    # Data transformation utilities
│   │   └── index.ts            # Helper exports
│   ├── processors/             # Data processors
│   │   ├── patientProcessor.ts # Patient data processing
│   │   ├── appointmentProcessor.ts # Appointment processing
│   │   ├── apContactProcessor.ts # AP contact processing
│   │   ├── apAppointmentProcessor.ts # AP appointment processing
│   │   └── invoicePaymentProcessor.ts # Invoice/payment processing
│   ├── type/                   # TypeScript type definitions
│   │   ├── APTypes.ts          # AutoPatient types
│   │   ├── CCTypes.ts          # CliniCore types
│   │   └── index.ts            # Type exports
│   ├── utils/                  # Utility functions
│   │   ├── configs.ts          # Centralized configuration
│   │   ├── advancedCache.ts    # Cloudflare-compatible caching
│   │   ├── bufferManager.ts    # Request buffer management
│   │   ├── errorLogger.ts      # Error logging utilities
│   │   ├── performanceMonitor.ts # Performance monitoring
│   │   ├── retryMechanism.ts   # Retry logic implementation
│   │   ├── validation.ts       # Data validation utilities
│   │   └── index.ts            # Utility exports
│   ├── webhook/                # Webhook handlers
│   │   ├── handler.ts          # Main webhook handler
│   │   └── apHandler.ts        # AutoPatient webhook handlers
│   └── index.ts                # Main application entry point
├── dev-event.js                # Socket event to webhook converter (development)
├── webhook-tester.js           # Comprehensive webhook testing utility
├── package.json                # Project dependencies and scripts
├── tsconfig.json               # TypeScript configuration
├── wrangler.jsonc              # Cloudflare Workers configuration
├── drizzle.config.ts           # Drizzle ORM configuration
├── biome.json                  # Biome linter configuration
└── README.md                   # This file
```

## 🛠️ Development

### Code Quality

The project uses strict TypeScript and comprehensive linting:

```bash
# Type checking
pnpm type-check

# Linting
pnpm lint

# Auto-fix linting issues
pnpm lint:fix
```

### Database Management

```bash
# Generate migrations
pnpm db:generate

# Push schema changes
pnpm db:push

# Open database studio
pnpm db:studio

# Full sync (generate + push)
pnpm db:sync
```

### Performance Optimization & Monitoring

The system is optimized for **20-second webhook completion** with comprehensive performance enhancements:

#### ⚡ Performance Optimizations

**Timeout Optimizations:**
- API call timeout: 8s → 4s (50% reduction)
- Retry timeout: 20s → 10s (50% reduction)
- Total request timeout: 25s → 20s (20% reduction)
- Retry attempts: 3 → 2 (33% reduction)

**Parallel Processing:**
- Database queries run in parallel where possible
- Independent API calls execute concurrently
- Batch database operations for better performance
- Optimized processor chains with reduced sequential operations

**Conditional Logging:**
- Debug logs disabled by default for performance
- Context logging controlled by configuration flags
- Standardized API performance logging: `[HTTP_METHOD] [STATUS] -> [DURATION]s -> [FULL_URL]`
- Reduced verbose output when not needed

#### 📊 Performance Monitoring

Built-in performance monitoring accessible via the `/health` endpoint:

- Request timing and memory usage
- API client performance metrics with standardized format
- Database query performance tracking
- Cache hit/miss ratios
- Buffer management statistics
- Parallel processing efficiency metrics

#### 🎛️ Logging Configuration

Control logging verbosity via `src/utils/configs.ts`:

```typescript
logging: {
  showLogContext: false, // Disable verbose context for performance
  showDebugLogs: false,  // Disable debug logs for performance
}
```

**Logging Levels:**
- **Performance Logs**: Always shown in format `[GET] [200] -> 1.5s -> https://api.clinicore.com/patients/123`
- **Error Logs**: Always shown for debugging
- **Debug Logs**: Only when `showDebugLogs: true`
- **Context Logs**: Only when `showLogContext: true`

#### 🧪 Performance Testing

Run performance validation tests:

```bash
# Test performance optimizations and logging controls
pnpm test:performance

# Run the custom performance test script
npx tsx test-performance.ts
```

The performance test validates:
- Webhook processing completes within 20 seconds
- Logging controls work correctly
- API performance logging format is standardized
- Parallel processing optimizations function properly

## 🔧 Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify `databaseUrl` in `src/utils/configs.ts`
   - Ensure database is accessible from your network
   - Check Neon database status

2. **API Authentication Errors**
   - Verify API keys in `src/utils/configs.ts`
   - Check API key permissions and expiration
   - Ensure correct API domains are configured

3. **Webhook Testing Issues**
   - Ensure ngrok tunnel is running for webhook callbacks
   - Check firewall settings for local development
   - Verify webhook URLs in external systems

4. **Type Errors**
   - Run `pnpm type-check` to identify issues
   - Ensure all imports use correct path aliases
   - Check for missing type definitions

### Debug Mode

Enable detailed logging by modifying the configuration:

```typescript
// src/utils/configs.ts
const configs: AppConfigs = {
  // ... other config
  errorLogging: {
    enableConsoleLogging: true,
    enableDatabaseLogging: true,
  },
  // ... rest of config
};
```

## 📄 License

This project is proprietary software developed for DermaCare medical practice management integration.

## 🤝 Contributing

1. Follow TypeScript strict mode requirements
2. Add comprehensive JSDoc documentation
3. Include tests for new functionality
4. Update configuration documentation
5. Ensure Cloudflare Workers compatibility

---

**Built with ❤️ for seamless healthcare data integration**