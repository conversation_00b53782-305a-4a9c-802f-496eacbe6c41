/**
 * API Client Index
 *
 * Central export point for all API clients providing easy access to both
 * ClinicalCare (CC) and AutoPatient (AP) API functionality.
 *
 * Usage:
 * ```typescript
 * import { ccClient, apClient } from './apiClient';
 *
 * // Use CC client
 * const patient = await ccClient.patientReq.get(123);
 *
 * // Use AP client
 * const contact = await apClient.contactReq.get('contact-id');
 * ```
 */

// AutoPatient API Client exports
export {
	apAppointmentReq,
	apCustomfield,
	apNoteReq,
	contactReq,
} from "@/apiClient/apClient";
// ClinicalCare API Client exports
export {
	ccAppointmentReq,
	ccCustomfieldReq,
	ccLocationReq,
	ccUserReq,
	invoiceReq,
	patientReq,
	paymentReq,
	resourceReq,
	serviceReq,
} from "@/apiClient/ccClient";

import * as apClientExports from "@/apiClient/apClient";
// Grouped exports for easier usage
import * as ccClientExports from "@/apiClient/ccClient";

/**
 * Complete ClinicalCare API client with all operations
 */
export const ccClient = {
	patientReq: ccClientExports.patientReq,
	ccCustomfieldReq: ccClientExports.ccCustomfieldReq,
	ccUserReq: ccClientExports.ccUserReq,
	serviceReq: ccClientExports.serviceReq,
	resourceReq: ccClientExports.resourceReq,
	ccLocationReq: ccClientExports.ccLocationReq,
	ccAppointmentReq: ccClientExports.ccAppointmentReq,
	invoiceReq: ccClientExports.invoiceReq,
	paymentReq: ccClientExports.paymentReq,
};

/**
 * Complete AutoPatient API client with all operations
 */
export const apClient = {
	contactReq: apClientExports.contactReq,
	apCustomfield: apClientExports.apCustomfield,
	apAppointmentReq: apClientExports.apAppointmentReq,
	apNoteReq: apClientExports.apNoteReq,
};

/**
 * Default export providing both clients
 */
export default {
	cc: ccClient,
	ap: apClient,
};
