#!/bin/bash

code ./

countdown() {
  secs=$1
  shift
  beforeTxt=$1
  afterText=$2
  while [ $secs -gt 0 ]
  do
    printf "\r\033[K$beforeTxt %.d$afterText" $((secs--))
    sleep 1
  done
  echo
}

echo $'\e[32;1m'
echo "Run All Local Server"
echo "------------------------------------------------"

echo "Starting Server..."
sleep 2s
wt --window 0 -p "Windows Powershell" -d . powershell -noExit "node ace serve --watch"
echo "------------------------------------------------"

echo "Starting NGROK..."
sleep 2s
wt --window 0 -p "Windows Powershell" -d . powershell -noExit "ngrok http --domain=real-loosely-moose.ngrok-free.app 3333"
echo "------------------------------------------------"

echo "Opening a blank window..."
sleep 2s
wt --window 0 -p "Windows Powershell" -d . powershell -noExit "echo 'Happy Coding'"


echo "------------------------------------------------"
countdown 5 "Will open the browser in" "s and close this window..."
