{"extends": "adonis-preset-ts/tsconfig.json", "include": ["**/*"], "exclude": ["node_modules", "build"], "compilerOptions": {"outDir": "build", "rootDir": "./", "sourceMap": true, "paths": {"@/*": ["./*"], "App/*": ["./app/*"], "Config/*": ["./config/*"], "Contracts/*": ["./contracts/*"], "Database/*": ["./database/*"]}, "types": ["@adonisjs/core", "@adonisjs/repl", "@japa/preset-adonis/build/adonis-typings", "@adonisjs/lucid", "@rocketseat/adonis-bull", "@adonisjs/session"]}}