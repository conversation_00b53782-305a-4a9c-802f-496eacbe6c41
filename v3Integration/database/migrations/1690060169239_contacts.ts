import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'contacts'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table
        .enum('source', ['ap', 'cc'])
        .nullable()
        .defaultTo('ap')
        .comment('Contact/patient source')
      table.string('ap_id').nullable().unique().defaultTo(null).comment('AP contact ID')
      table.bigInteger('cc_id').nullable().unique().defaultTo(null).comment('CC Patient ID')
      table.string('email').unique().nullable().defaultTo(null).comment('Contact/Patient email')
      table.string('phone').unique().nullable().defaultTo(null).comment('Contact/Patient phone')
      table.json('ap_data').nullable().defaultTo(null).comment('AP Contact data')
      table.json('cc_data').nullable().defaultTo(null).comment('CC Patient data')

      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
