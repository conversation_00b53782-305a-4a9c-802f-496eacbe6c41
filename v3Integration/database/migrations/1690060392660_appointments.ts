import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'appointments'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table
        .integer('cp_id')
        .unsigned()
        .references('contacts.id')
        .comment('Local contact ID for relationship')
      table
        .enum('source', ['ap', 'cc'])
        .nullable()
        .defaultTo('ap')
        .comment('Appointment data source')

      table.bigInteger('cc_id').unique().nullable().defaultTo(null).comment('CC Appointment ID')
      table.string('ap_id').unique().nullable().defaultTo(null).comment('AP Appointment ID')

      table.bigInteger('patient_id').nullable().defaultTo(null).comment('CC Patient ID')
      table.string('contact_id').nullable().defaultTo(null).comment('AP Contact ID')

      table.string('start_at').nullable()
      table.string('end_at').nullable()

      table.json('ap_data').nullable().defaultTo(null).comment('AP Appointment data')
      table.json('cc_data').nullable().defaultTo(null).comment('CC Appointment data')

      table.string('ap_note').nullable().defaultTo(null).comment('AP Appointment note ID')

      table.timestamps(true, true)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
