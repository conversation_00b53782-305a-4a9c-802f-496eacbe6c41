import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'o_auths'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('location').unique()
      table.string('name').comment('Sub account name')
      table.string('timezone').comment('Sub account timezone')
      table.string('calendar_id').nullable().comment('AP Calendar ID')
      table.string('cc_api_url').nullable().comment('CC API URL')
      table.text('cc_api_token').nullable().comment('CC API Token')
      table.string('cc_socket_url').nullable().comment('CC Socket URL')
      table.text('cc_socket_token').nullable().comment('CC Socket Token')
      table.text('ap_static_token').nullable().comment('AP Static Token')
      table.text('ap_access_token').nullable().comment('AP Access Token')
      table.text('ap_refresh_token').nullable().comment('AP Refresh Token')
      table.timestamp('token_expire', { useTz: true }).nullable()
      table.timestamp('created_at', { useTz: true }).nullable().defaultTo(null)
      table.timestamp('updated_at', { useTz: true }).nullable().defaultTo(null)
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
