import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import OAuth from 'App/Models/OAuth'
import { setAPIAuth } from '@/utils/apiAuth'

export default class VerifyLocation {
  public async handle(
    { params, response, session }: HttpContextContract,
    next: () => Promise<void>
  ) {
    if ('location' in params) {
      const auth = await OAuth.findBy('location', params.location)
      if (auth) {
        session.put('auth', auth)
        await setAPIAuth(auth.id)
        return await next()
      } else {
        return response.unauthorized({ error: 'Must have to add auth token.' })
      }
    }
    return await next()
  }
}
