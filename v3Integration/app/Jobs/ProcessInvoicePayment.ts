import { JobContract } from '@ioc:Rocketseat/Bull'
import { setAPIAuth } from '@/utils/apiAuth'
import Contact from '@/app/Models/Contact'
import { cLog } from '@/utils'
import { syncInvoicePayments } from '@/app/helpers/ap'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/

export default class ProcessInvoicePayment implements JobContract {
  public key = 'ProcessInvoicePayment'

  public async handle(job) {
    const { payload, auth: oAuth } = job.data
    await setAPIAuth(oAuth)
    const contact = await Contact.findBy('ccId', payload.patient)
    if (!contact) {
      cLog(`Patient not synced with AP yet. Patient ID: ${payload.patient}`)
      return `Patient not synced with AP yet. Patient ID: ${payload.patient}`
    }
    contact && contact.apId && contact.ccId && (await syncInvoicePayments(contact))
  }
}
