import { JobContract } from '@ioc:Rocketseat/Bull'
import { setAPIAuth } from '@/utils/apiAuth'
import Appointment from '../Models/Appointment'
import { DateTime } from 'luxon'
import { cLog } from '@/utils'
import { syncCCtoAPAppointment } from '@/app/helpers/ap'
import Skip from '../Models/Skip'
import { slackLogger } from '@/utils/slackLogger'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/

export default class ProcessCcAppointmentUpdate implements JobContract {
  public key = 'ProcessCcAppointmentUpdate'

  public async handle(job: { data: { payload: GetCCAppointmentType; auth: number } }) {
    const { payload, auth } = job.data
    await setAPIAuth(auth)
    if (await Skip.hasProcessAppointmentUpdate(payload.id)) {
      cLog(`CC Appointment update loop, Appointment ID: ${payload.id}, Dropping it.`)
      return
    }
    let appointment = await Appointment.findBy('ccId', payload.id)
    if (!appointment) {
      cLog(
        `Appointment isn't exist in AP. CC ID: ${payload.id}, Payload Reeived: ${JSON.stringify(
          payload
        )}`
      )
      slackLogger.error(
        `Appointment isn't exist in AP. CC ID: ${payload.id}, Payload Reeived: ${JSON.stringify(
          payload
        )}`
      )
      return
    } else {
      appointment.startAt = DateTime.fromISO(payload.startsAt, { zone: 'UTC' })
      appointment.endAt = DateTime.fromISO(payload.endsAt, { zone: 'UTC' })
      appointment.ccData = payload
      appointment.patientId = payload.patients[0]
      await appointment.save()
    }
    cLog(
      `Appointment has been queued to update to AP, PatientID: ${appointment.patientId}, Appointment ID: ${appointment.id}`
    )
    await syncCCtoAPAppointment(appointment)
  }
}
