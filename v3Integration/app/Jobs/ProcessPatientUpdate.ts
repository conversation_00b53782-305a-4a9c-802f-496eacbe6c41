import { JobContract } from '@ioc:Rocketseat/Bull'
import { cLog } from '@/utils'
import Contact from 'App/Models/Contact'
import { setAPIAuth } from '@/utils/apiAuth'
import Skip from '../Models/Skip'
import { updateOrCreateContact } from '@/app/helpers/ap'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/

export default class ProcessPatientUpdate implements JobContract {
  public key = 'ProcessPatientUpdate'

  public async handle(job: { data: { payload: any; auth: any } }) {
    const { payload, auth }: { payload: any; auth: any } = job.data
    if (await Skip.hasProcessPatientUpdate(payload.id)) {
      return `We did this update recently, so dropping it. (Updated at ${payload.updatedAt}, PatientId: ${payload.id})`
    }
    await setAPIAuth(auth)
    if (!payload.email && !payload.phoneMobile) {
      cLog(`Email and phone is empty, Dropping update patient request, Patient ID: ${payload.id}`)
      return
    }
    let contact = await Contact.searchCreateOrUpdate({
      ccId: payload.id,
      ccData: payload,
      source: 'cc',
      email: payload.email,
      phone: payload.phoneMobile,
    })
    await updateOrCreateContact(contact)
  }
}
