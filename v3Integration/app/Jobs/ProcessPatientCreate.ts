import { JobContract } from '@ioc:Rocketseat/Bull'
import { setAPIAuth } from '@/utils/apiAuth'
import Skip from '@/app/Models/Skip'
import Contact from '@/app/Models/Contact'
import { cLog } from '@/utils'
import { updateOrCreateContact } from '@/app/helpers/ap'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/

export default class ProcessPatientCreate implements JobContract {
  public key = 'ProcessPatientCreate'

  public async handle(job: { data: { payload: GetCCPatientType; auth: number } }) {
    const { payload, auth }: { payload: GetCCPatientType; auth: number } = job.data
    await setAPIAuth(auth)
    let contact = await Contact.findBy('ccId', payload.id)
    if (contact && contact.apId) {
      if (await Skip.hasProcessPatientCreate(contact.apId)) {
        return `We did this creation recently, so dropping it. (Updated at ${payload.updatedAt}, PatientId: ${payload.id})`
      }
    }
    contact = await Contact.searchCreateOrUpdate({
      ccId: payload.id,
      ccData: payload,
      source: 'cc',
      email: payload.email,
      phone: payload.phoneMobile,
    })
    if (contact.apId) {
      cLog(`Patient already exists in AP, ID: ${contact.apId}`)
      return
    }
    await updateOrCreateContact(contact)
  }
}
