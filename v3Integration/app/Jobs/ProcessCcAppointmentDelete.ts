import { JobContract } from '@ioc:Rocketseat/Bull'
import { setAPIAuth } from '@/utils/apiAuth'
import Appointment from '@/app/Models/Appointment'
import { deleteAppointmentFromAP } from '../helpers/ap'
import { cLog } from '@/utils'
import { apNoteReq } from '@/request'
import { removeHtmlTags } from '@/utils'
import { DateTime } from 'luxon'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/

export default class ProcessCcAppointmentDelete implements JobContract {
  public key = 'ProcessCcAppointmentDelete'

  public async handle(job) {
    const { payload, auth } = job.data
    await setAPIAuth(auth)
    let appointment = await Appointment.findBy('ccId', payload)
    if (appointment) {
      const apRes = await deleteAppointmentFromAP(appointment)
      if (apRes) {
        if (appointment.apNote && appointment.contactId) {
          await apNoteReq.put(
            appointment.contactId,
            appointment.apNote,
            `Appointment has been deleted, Scheduled at ${appointment.startAt.toISO()}`
          )
        }
        cLog(`Appointment has been deleted from AP, Contact Id: ${appointment.contactId}.`)
        if (appointment.apNote && appointment.contactId) {
          await apNoteReq
            .put(
              appointment.contactId,
              appointment.apNote,
              `Appointment Deleted: ${removeHtmlTags(
                appointment.ccData.title ?? 'No title found'
              )}\n\nScheduled at: ${appointment.startAt
                .setZone(auth.timezone)
                .setLocale('de')
                .toLocaleString(DateTime.DATETIME_FULL)}\n\nDeleted At: ${DateTime.now()
                .setZone(auth.timezone)
                .setLocale('de')
                .toLocaleString(DateTime.DATETIME_FULL)}
              }`
            )
            .then(() => {
              cLog(`Appoinment note has been updated for deletetion.`)
            })
        }
        await appointment.delete()
        return
      } else {
        cLog(
          `Unable to delete appointment from AP, Local ID: ${
            appointment.id
          }, ccData: ${JSON.stringify(appointment.ccData)}`
        )
      }
    } else {
      cLog(`Appointment not exist in our system, CC Appointment ID: ${payload}`)
    }
  }
}
