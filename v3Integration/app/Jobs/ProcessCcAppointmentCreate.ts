import { JobContract } from '@ioc:Rocketseat/Bull'
import { setAPIAuth } from '@/utils/apiAuth'
import Appointment from '@/app/Models/Appointment'
import { cLog } from '@/utils'
import { syncCCtoAPAppointment } from '@/app/helpers/ap'
import { DateTime } from 'luxon'
import { slackLogger } from '@/utils/slackLogger'

export default class ProcessCcAppointmentCreate implements JobContract {
  public key = 'ProcessCcAppointmentCreate'

  public async handle(job: { data: { payload: GetCCAppointmentType; auth: number } }) {
    try {
      const { payload, auth } = job.data
      await setAPIAuth(auth)
      let appointment = await Appointment.findBy('ccId', payload.id)
      if (appointment && appointment.apId) {
        cLog(`Appointment already exists in AP, ID: ${appointment.apId}, Dropping this request.`)
        return
      }
      if (!appointment) {
        appointment = await Appointment.updateOrCreate(
          {
            ccId: payload.id,
            source: 'cc',
          },
          {
            ccData: payload,
            startAt: DateTime.fromISO(payload.startsAt, { zone: 'UTC' }),
            endAt: DateTime.fromISO(payload.endsAt, { zone: 'UTC' }),
            patientId: payload.patients[0],
          }
        )
      }
      cLog(
        `Appointment has been queued to create to AP, ccAppointmentId: ${appointment.ccId}, ccPatientId: ${appointment.patientId}`
      )
      await syncCCtoAPAppointment(appointment)
    } catch (error) {
      cLog('Appointment error', { error: error.toString() })
      slackLogger.error(`Appointment error: ${error.toString()}`)
      throw error
    }
  }
}
