import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class O<PERSON>uth extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public location: string

  @column()
  public name: string

  @column({ columnName: 'cc_api_url' })
  public CCApiUrl: string

  @column({ columnName: 'cc_api_token' })
  public CCApiToken: string

  @column({ columnName: 'cc_socket_url' })
  public CCSocketUrl: string

  @column({ columnName: 'cc_socket_token' })
  public CCSocketToken: string

  @column({ columnName: 'ap_static_token' })
  public APStaticToken: string

  @column({ columnName: 'ap_access_token' })
  public APAccessToken: string

  @column({ columnName: 'ap_refresh_token' })
  public APRefreshToken: string

  @column() public calendarId: string
  @column() public timezone: string

  @column.dateTime({ columnName: 'token_expire' })
  public tokenExpire: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
