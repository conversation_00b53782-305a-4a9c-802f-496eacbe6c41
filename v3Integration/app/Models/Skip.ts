import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class Skip extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column() public action: string
  @column({ columnName: 'cc_id' }) public ccId: number
  @column({ columnName: 'ap_id' }) public apId: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  public static async hasProcessPatientUpdate(ccId: number) {
    const has = await this.query()
      .where('action', 'ProcessPatientUpdate')
      .where('ccId', ccId)
      .first()
    if (has) {
      has.delete()
      return true
    }
    return false
  }
  public static async putProcessPatientUpdate(ccId: number) {
    await this.create({
      action: 'ProcessPatientUpdate',
      ccId,
    })
  }

  public static async hasProcessPatientCreate(apId: string) {
    const has = await this.query()
      .where('action', 'putProcessPatientCreate')
      .where('apId', apId)
      .first()
    if (has) {
      has.delete()
      return true
    }
    return false
  }
  public static async putProcessPatientCreate(apId: string) {
    await this.create({
      action: 'putProcessPatientCreate',
      apId,
    })
  }

  public static async hasProcessAppointmentUpdate(ccId: number) {
    const has = await this.query()
      .where('action', 'ProcessAppointmentUpdate')
      .where('ccId', ccId)
      .first()
    if (has) {
      has.delete()
      return true
    }
    return false
  }
  public static async putProcessAppointmentUpdate(ccId: number) {
    await this.create({
      action: 'ProcessAppointmentUpdate',
      ccId,
    })
  }

  public static async hasProcessAppointmentCreate(apId: string) {
    const has = await this.query()
      .where('action', 'ProcessAppointmentCreate')
      .where('apId', apId)
      .first()
    if (has) {
      has.delete()
      return true
    }
    return false
  }
  public static async putProcessAppointmentCreate(apId: string) {
    await this.create({
      action: 'ProcessAppointmentCreate',
      apId,
    })
  }
}
