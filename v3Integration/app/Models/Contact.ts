import { DateTime } from 'luxon'
import { <PERSON><PERSON><PERSON>l, <PERSON><PERSON>any, column, hasMany } from '@ioc:Adonis/Lucid/Orm'
import Appointment from './Appointment'
import { invoiceReq, paymentReq } from '@/request'

export default class Contact extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column() public source: 'ap' | 'cc'

  @column() public apId: string
  @column() public ccId: number

  @column({
    prepare: (value) => (!value || value === '' ? null : value),
  })
  public email: string

  @column({
    prepare: (value) => (!value || value === '' ? null : value),
  })
  public phone: string

  @column({
    prepare: (value: object) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value),
  })
  public apData: GetAPContactType

  @column({
    prepare: (value: object) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value),
  })
  public ccData: GetCCPatientType

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @hasMany(() => Appointment, {
    localKey: 'id',
    foreignKey: 'contact',
  })
  public appointments: HasMany<typeof Appointment>

  public async invoices() {
    if (this.ccData && this.ccData.invoices && this.ccData.invoices.length > 0) {
      return await invoiceReq.get(this.ccData.invoices)
    }
    return null
  }
  public async payments() {
    if (this.ccData && this.ccData.payments && this.ccData.payments.length > 0) {
      return await paymentReq.get(this.ccData.payments)
    }
    return null
  }

  public static async searchCreateOrUpdate(payload: {
    apId?: string
    ccId?: number
    email?: string
    phone?: string
    ccData?: GetCCPatientType
    apData?: GetAPContactType
    source?: 'ap' | 'cc'
  }): Promise<Contact> {
    let contact = Contact.query()
    if (payload.apId) {
      contact = contact.where('apId', payload.apId)
    } else if (payload.ccId) {
      contact = contact.where('ccId', parseInt(payload.ccId + ''))
    } else {
      if (payload.email) {
        contact = contact.where('email', payload.email)
      }
      if (payload.phone) {
        contact = contact.where('phone', payload.phone)
      }
    }
    const found = await contact.first()
    if (found) {
      if (payload.apId) {
        found.apId = payload.apId
      }
      if (payload.ccId) {
        found.ccId = payload.ccId
      }
      if (payload.ccData) {
        found.ccData = payload.ccData
      }
      if (payload.apData) {
        found.apData = payload.apData
      }
      if (payload.phone) {
        found.phone = payload.phone
      }
      if (payload.email) {
        found.email = payload.email
      }
      await found.save()
      return await found.refresh()
    } else {
      return await Contact.create(payload)
    }
  }
}
