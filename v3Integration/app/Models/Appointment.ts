import { DateTime } from 'luxon'
import { BaseModel, <PERSON>One, column, hasOne } from '@ioc:Adonis/Lucid/Orm'
import Contact from './Contact'

export default class Appointment extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  // @column() public cpId: number

  @column() public source: 'ap' | 'cc'

  @column() public ccId: number

  @column() public apId: string

  @column() public patientId: number

  @column() public contactId: string

  @column.dateTime({
    consume: (value: string) => (value ? DateTime.fromISO(value, { zone: 'UTC' }) : null),
    prepare: (value: DateTime) => (value ? value.toISO() : null),
  })
  public startAt: DateTime

  @column.dateTime({
    consume: (value: string) => (value ? DateTime.fromISO(value, { zone: 'UTC' }) : null),
    prepare: (value: DateTime) => (value ? value.toISO() : null),
  })
  public endAt: DateTime

  @column({
    prepare: (value: object) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value),
  })
  public apData: GetAPAppointmentType

  @column({
    prepare: (value: object) => JSON.stringify(value),
    consume: (value: string) => JSON.parse(value),
  })
  public ccData: GetCCAppointmentType

  @column({ columnName: 'ap_note' }) public apNote: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @hasOne(() => Contact, {
    localKey: 'cpId',
    foreignKey: 'id',
  })
  public Contact: HasOne<typeof Contact>
}
