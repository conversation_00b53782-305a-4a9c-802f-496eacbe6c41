import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Contact from '@/app/Models/Contact'
import Appointment from '@/app/Models/Appointment'
import { apAppointmentReq } from '@/request'
import { DateTime } from 'luxon'
import { updateAppointmentToCC } from '@/app/helpers/cc'
import { cLog } from '@/utils'
import { getAPIAuth } from '@/utils/apiAuth'
import ProcessApAppointmentCreatesController from 'App/Controllers/Http/ProcessApAppointmentCreatesController'

export default class ProcessApAppointmentUpdatesController {
  public async Update(ctx: HttpContextContract) {
    const { request, response } = ctx
    const { calendar, contact_id: contactId } = request.body()
    const auth = getAPIAuth()

    if (calendar.last_updated_by_meta.source === 'third_party') {
      return response.json({
        status: 200,
        message: 'Appointment updated by third party. So it might not be processed.',
      })
    }

    let appointment = await Appointment.findBy('apId', calendar.appointmentId)
    if (!appointment) {
      const createInstance = new ProcessApAppointmentCreatesController()
      return await createInstance.Create(ctx)
    }

    let contact: any = null
    if (contactId) {
      contact = await Contact.findBy('apId', contactId)
    } else {
      return response.unprocessableEntity('Contact not found.')
    }

    if (!contact) {
      return response.unprocessableEntity('Contact not found')
    }

    const apAppointment = await apAppointmentReq.get(calendar.appointmentId)
    appointment.apData = apAppointment
    appointment.startAt = DateTime.fromISO(calendar.startTime, { zone: auth.timezone }).setZone(
      'UTC'
    )
    appointment.endAt = DateTime.fromISO(calendar.endTime, { zone: auth.timezone }).setZone('UTC')
    await appointment.save()
    cLog(`Updating appointment to CC`)
    const res = await updateAppointmentToCC(await appointment.refresh())
    if (res) {
      return response.json({
        status: 200,
        message: 'Appointment updated successfully to CC.',
        data: {
          id: res.ccId,
        },
      })
    }
    return response.json({
      status: 400,
      message: 'Unable to update the appointment to CC.',
    })
  }
}
