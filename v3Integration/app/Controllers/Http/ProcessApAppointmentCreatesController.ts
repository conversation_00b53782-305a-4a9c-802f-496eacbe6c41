import Appointment from '@/app/Models/Appointment'
import Contact from '@/app/Models/Contact'
import { apAppointmentReq, contactReq } from '@/request'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { DateTime } from 'luxon'
import { createAppointmentToCC } from '@/app/helpers/cc'
import { getAPIAuth } from '@/utils/apiAuth'

export default class ProcessApAppointmentCreatesController {
  public async Create(ctx: HttpContextContract) {
    const { request, response } = ctx
    const { calendar, contact_id: contactId } = request.body()
    const auth = getAPIAuth()
    console.log('request.body()', request.body())
    if (calendar && calendar.created_by_meta.source === 'third_party') {
      return response.json({
        status: 200,
        message: 'Appointment created by third party.',
      })
    }
    if (!calendar) {
      console.log('Calendar not found')
      return null
    }
    let contact: any = null
    if (!contactId) {
      return response.unprocessableEntity('Contact not found.')
    }
    const apContact = await contactReq.get(contactId)
    contact = await Contact.searchCreateOrUpdate({
      apId: contactId,
      email: request.body().email,
      phone: request.body().phone,
      source: 'ap',
      apData: apContact,
    })
    let appointment = await Appointment.findBy('apId', calendar.appointmentId)
    if (appointment) {
      return response.json({
        status: 200,
        message: `Appointment already exist here. CC ID: ${appointment.ccId}`,
      })
    }

    const apAppointment = await apAppointmentReq.get(calendar.appointmentId)
    appointment = await Appointment.create({
      apId: calendar.appointmentId,
      contactId: contact.apId,
      apData: apAppointment,
      source: 'ap',
      startAt: DateTime.fromISO(calendar.startTime, { zone: auth.timezone }).setZone('UTC'),
      endAt: DateTime.fromISO(calendar.endTime, { zone: auth.timezone }).setZone('UTC'),
    })
    const res = await createAppointmentToCC(appointment)
    return response.json({
      status: 200,
      message: 'Appointment created successfully to CC.',
      data: {
        id: res?.ccId,
      },
    })
  }
}
