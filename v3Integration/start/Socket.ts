/*
|--------------------------------------------------------------------------
| Preloaded File
|--------------------------------------------------------------------------
|
| Any code written inside this file will be executed during the application
| boot.
|
*/
import io from 'socket.io-client'
import OAuth from 'App/Models/OAuth'
import { cLog } from '@/utils'
import { slackLogger } from '@/utils/slackLogger'
import Bull from '@ioc:Rocketseat/Bull'

const processSocket = async () => {
  cLog(`Trying to init Socket`)
  try {
    const auths = await OAuth.all()
    if (auths.length === 0) {
      throw new Error(`No authentication found`)
    }
    auths.map(async (auth) => {
      if (!auth.CCSocketUrl || !auth.CCSocketUrl) {
        cLog(`CC Socket url and token not found`, auth)
        return
      }
      cLog(`Trying to connect with ${auth.name} socket server.`)
      const socket = io.connect(auth.CCSocketUrl, {
        query: 'access_token=' + auth.CCSocketToken,
        transport: ['websocket'],
      })

      socket.on('connect', () => console.log('Listening socket event for ' + auth.CCApiUrl))

      socket.on('error', (e: any) => {
        slackLogger.error("We're unable to connect your CC account." + auth.name)
        console.log('Unable to listen socket event for ' + auth.name + ' -> Error: ', { e })
      })

      socket.on('mobimed:App\\Events\\EntityWasCreated', async (data: SocketEventType) => {
        switch (data.type) {
          case 'patient':
            Bull.add('ProcessPatientCreate', { payload: data.payload, auth: auth.id })
            break
          case 'invoice':
          case 'payment':
            // case 'inventory-movement':
            Bull.add('ProcessInvoicePayment', { payload: data.payload, auth: auth.id })
            break
          case 'service':
            cLog(`We don't process service event`, data)
            break
          default:
            console.log('mobimed:App\\Events\\EntityWasCreated', data.type)
            console.table(data.payload)
            break
        }
      })

      socket.on('mobimed:App\\Events\\AppointmentWasCreated', async (data: SocketEventType) => {
        if (data.type && data.type === 'appointment') {
          Bull.add('ProcessCcAppointmentCreate', { payload: data.payload, auth: auth.id })
        }
      })

      socket.on('mobimed:App\\Events\\EntityWasUpdated', async (data: SocketEventType) => {
        switch (data.type) {
          case 'patient':
            Bull.add('ProcessPatientUpdate', { payload: data.payload, auth: auth.id })
            break
          case 'appointment':
            Bull.add('ProcessCcAppointmentUpdate', { payload: data.payload, auth: auth.id })
            break
          case 'service':
            cLog(`We don't process service event`, data)
            break
          default:
            console.log('mobimed:App\\Events\\EntityWasUpdated', data.type)
            console.table(data.payload)
            break
        }
      })

      socket.on('mobimed:App\\Events\\EntityWasDeleted', async (data: SocketEventType) => {
        switch (data.type) {
          case 'appointment':
            Bull.add('ProcessCcAppointmentDelete', { payload: data.id, auth: auth.id })
            break
          default:
            console.log('mobimed:App\\Events\\EntityWasDeleted', data.type)
            console.table(data.payload)
            break
        }
      })

      //End map
    })
  } catch (error) {
    cLog(`Socket Error`, error)
  }
}

;(async () => await processSocket())()
