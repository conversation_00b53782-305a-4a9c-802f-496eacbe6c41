import OAuth from '@/app/Models/OAuth'
import { cLog } from '@/utils'
import { getAPIAuth, setAPIAuth } from '@/utils/apiAuth'
import { slackLogger } from '@/utils/slackLogger'
import axios, { AxiosError } from 'axios'
import { DateTime } from 'luxon'
let pendingRequest = false

/**
 * AP Request v2
 */
export const apRequestV2 = async (options: {
  url: string
  method: string
  headers?: { [key: string]: string }
  data?: any
  params?: any
}) => {
  const auth: OAuth = getAPIAuth()
  if (!auth) {
    throw new Error('Auth not set yet')
  }
  if (!auth.APAccessToken) {
    console.log('auth.APAccessToken', auth)
    throw new Error('AP token not found.')
  }

  if (auth.tokenExpire < DateTime.now().minus({ minutes: 5 })) {
    await refreshAPToken(auth)
  }

  if (!pendingRequest) {
    const v2 = axios.create({
      baseURL: 'https://services.leadconnectorhq.com',
      headers: {
        'Authorization': 'Bearer ' + auth.APAccessToken,
        'Content-Type': 'application/json',
        'Accept-Encoding': 'application/json',
        'Accept': 'application/json',
        'Version': '2021-04-15',
      },
    })
    return await v2
      .request(options)
      .then((r) => {
        return r.data
      })
      .catch(async (error) => {
        if (error.response && error.response.status === 401) {
          await refreshAPToken(auth)
          return apRequestV2(options)
        }
        cLog('Request error: ' + error.response?.data.message ?? error.message, {
          payload: options,
        })
        throw new Error('Request error: ' + error.response?.data.message ?? error.message)
      })
  }
}

/**
 * AP Request version 1
 */
export const apRequestV1 = async (options: {
  url: string
  method: string
  headers?: { [key: string]: string }
  data?: any
  params?: any
}) => {
  const auth = getAPIAuth()
  if (!auth) {
    throw new Error('Auth not set yet')
  }
  if (!auth.APStaticToken) {
    throw new Error('AP Static token not found.')
  }
  const v1 = axios.create({
    baseURL: 'https://rest.gohighlevel.com/v1/',
    maxBodyLength: Infinity,
    headers: {
      'Authorization': 'Bearer ' + auth.APStaticToken,
      'Content-Type': 'application/json',
      'Accept-Encoding': 'application/json',
      'Accept': 'application/json',
    },
  })
  return await v1
    .request(options)
    .then((r) => r.data)
    .catch(async (error) => {
      throw error.response.data ?? error
    })
}
export const ccRequest = async (options: {
  url: string
  method: string
  headers?: { [key: string]: string }
  data?: any
  params?: any
}) => {
  const auth = getAPIAuth()
  if (!auth) {
    throw new Error('Auth not set yet')
  }
  if (!auth.CCApiUrl || !auth.CCApiToken) {
    throw new Error('CC API credentials not found.')
  }

  const ccR = axios.create({
    baseURL: auth.CCApiUrl,
    headers: {
      common: {
        'Authorization': 'Bearer ' + auth.CCApiToken,
        'Accept-Encoding': 'application/json',
        'Content-Type': 'application/json',
      },
    },
  })
  return ccR
    .request(options)
    .then((r) => r.data)
    .catch((e) => {
      throw e.response?.data ?? e
    })
}

export const refreshAPToken = async (auth: OAuth) => {
  const params = {
    client_id: process.env.AP_CLIENT_ID,
    client_secret: process.env.AP_CLIENT_SECRET,
    grant_type: 'refresh_token',
    refresh_token: auth.APRefreshToken,
  }

  const reqOption = {
    method: 'POST',
    url: `https://api.msgsndr.com/oauth/token`,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: params,
    params,
  }
  pendingRequest = true
  return axios
    .request(reqOption)
    .then(async (res) => {
      if (res.data.access_token) {
        auth.APAccessToken = res.data.access_token
        auth.APRefreshToken = res.data.refresh_token
        auth.tokenExpire = DateTime.now().plus({ seconds: res.data.expires_in })
        await auth.save()
        await setAPIAuth(auth.id)
        pendingRequest = false
        return auth
      } else {
        slackLogger.error('Unable to refresh the AP token for ' + auth.name)
        throw new Error(`Unable to refresh the AP access token ${auth.name}.`)
      }
    })
    .catch((error: AxiosError<{ statusCode?: number; message?: string; error?: string }>) => {
      throw new Error(error.response?.data?.message ?? error.message)
    })
}
