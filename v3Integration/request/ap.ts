import { apRequestV2 } from './request'
import { getAPIAuth } from '../utils/apiAuth'
import { removeNullEmptyProperties } from '@/utils/index'

export const contactReq = {
  get: (id: string): Promise<GetAPContactType> =>
    apRequestV2({ url: '/contacts/' + id, method: 'get' }).then((r: any) => r.contact),
  create: (data: PostAPContactType): Promise<GetAPContactType> => {
    const auth = getAPIAuth()
    return apRequestV2({
      url: '/contacts/',
      method: 'post',
      data: { ...removeNullEmptyProperties(data), locationId: auth.location },
    }).then((r: any) => r.contact)
  },
  upsert: (data: PostAPContactType): Promise<GetAPContactType> => {
    const auth = getAPIAuth()
    return apRequestV2({
      url: '/contacts/upsert/',
      method: 'post',
      data: { ...removeNullEmptyProperties(data), locationId: auth.location },
    }).then((r: any) => r.contact)
  },
  update: (id: string, data: PostAPContactType): Promise<GetAPContactType> =>
    apRequestV2({
      url: '/contacts/' + id,
      method: 'put',
      data: removeNullEmptyProperties(data),
    }).then((r: any) => r.contact),
  delete: (id: string) => apRequestV2({ url: `/contacts/${id}/`, method: 'delete' }),
  appointments: (contactId: string) =>
    apRequestV2({ url: `/contacts/${contactId}/appointments/`, method: 'get' }),
  all: async (params?: {
    limit?: number
    query?: string
    startAfter?: number
    startAfterId?: string
  }): Promise<GetAPContactType[]> => {
    const auth = getAPIAuth()
    return apRequestV2({
      url: `/contacts/`,
      method: 'get',
      params: { ...params, locationId: auth.location },
    }).then((r: any) => r.contacts)
  },
}

export const apCustomfield = {
  get: async (id: string): Promise<APGetCustomFieldType> => {
    const auth = getAPIAuth()
    return apRequestV2({
      url: `/locations/${auth.location}/customFields/${id}/`,
      method: 'get',
    }).then((r: any) => r.customField)
  },
  all: async (): Promise<APGetCustomFieldType[]> => {
    const auth = getAPIAuth()
    return apRequestV2({ url: `/locations/${auth.location}/customFields/`, method: 'get' }).then(
      (r: any) => r.customFields
    )
  },
  create: async (data: APPostCustomfieldType): Promise<APGetCustomFieldType> => {
    const auth = getAPIAuth()
    return apRequestV2({
      url: `/locations/${auth.location}/customFields/`,
      method: 'post',
      data,
    }).then((r: any) => r.customField)
  },
  update: async (id: string, data: APPostCustomfieldType): Promise<APGetCustomFieldType> => {
    const auth = getAPIAuth()
    return apRequestV2({
      url: `/locations/${auth.location}/customFields/${id}/`,
      method: 'put',
      data,
    }).then((r: any) => r.customField)
  },
  delete: async (id: string): Promise<boolean> => {
    const auth = getAPIAuth()
    return apRequestV2({
      url: `/locations/${auth.location}/customFields/${id}/`,
      method: 'delete',
    }).then((r: any) => r.succeded)
  },
}

export const apAppointmentReq = {
  get: async (apId: string): Promise<GetAPAppointmentType> => {
    return apRequestV2({
      url: `/calendars/events/appointments/${apId}`,
      method: 'GET',
    }).then((r: any) => r.appointment)
  },
  post: async (payload: PostAPAppointmentType): Promise<GetAPAppointmentType> => {
    const auth = getAPIAuth()
    return apRequestV2({
      url: '/calendars/events/appointments',
      method: 'POST',
      data: { calendarId: auth.calendarId, ...payload, locationId: auth.location },
    })
  },
  postBlockSlot: async (payload: PostAPAppointmentType): Promise<GetAPAppointmentType> => {
    const auth = getAPIAuth()
    return apRequestV2({
      url: '/calendars/events/block-slots',
      method: 'POST',
      data: { calendarId: auth.calendarId, ...payload, locationId: auth.location },
    })
  },
  put: async (apId: string, payload: PutAPAppointmentType) => {
    const auth = getAPIAuth()
    return apRequestV2({
      url: `/calendars/events/appointments/${apId}`,
      method: 'PUT',
      data: { calendarId: auth.calendarId, ...payload, locationId: auth.location },
    })
  },
  delete: async (apId: string): Promise<boolean> => {
    return apRequestV2({
      url: `/calendars/events/${apId}`,
      method: 'DELETE',
    }).then((r: any) => r.succeeded)
  },
}

export const apNoteReq = {
  post: async (contactId: string, payload: string): Promise<GetAPNoteType> => {
    return await apRequestV2({
      url: `/contacts/${contactId}/notes/`,
      method: 'POST',
      data: {
        body: payload,
      },
    }).then((r: any) => r.note)
  },
  put: async (contactId: string, noteId: string, payload: string): Promise<GetAPNoteType> => {
    return await apRequestV2({
      url: `/contacts/${contactId}/notes/${noteId}/`,
      method: 'PUT',
      data: {
        body: payload,
      },
    }).then((r: any) => r.note)
  },
  delete: async (contactId: string, noteId: string): Promise<boolean> => {
    return await apRequestV2({
      url: `/contacts/${contactId}/notes/${noteId}/`,
      method: 'DELETE',
    }).then((r: any) => r.succeded)
  },
}
