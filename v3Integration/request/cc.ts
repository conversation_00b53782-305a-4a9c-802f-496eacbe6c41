import { ccRequest } from './request'
import { removeNullEmptyProperties } from '@/utils/index'

export const patientReq = {
  create: async (data: PostCCPatientType): Promise<GetCCPatientType> => {
    const r = await ccRequest({
      url: `/patients`,
      method: 'POST',
      data: {
        patient: removeNullEmptyProperties(data),
      },
    })
    return r.patient
  },
  update: async (id: number, data: PostCCPatientType): Promise<GetCCPatientType> => {
    const r = await ccRequest({
      url: `/patients/${id}`,
      method: 'PUT',
      data: {
        patient: removeNullEmptyProperties(data),
      },
    })
    return r.patient
  },
  get: async (id: number): Promise<GetCCPatientType> => {
    const r = await ccRequest({
      url: `/patients/${id}`,
      method: 'GET',
    })
    return r.patient
  },
  search: async (emailOrPhoneOr: string): Promise<GetCCPatientType> => {
    const r = await ccRequest({
      url: `/patients?search=${emailOrPhoneOr}`,
      method: 'GET',
    })
    return (r.patients && r.patients.length) > 0 ? r.patients[0] : null
  },
  all: async (
    params: {
      page?: number
      perPage?: number
      active?: boolean
      sort?: string
    } = {
      page: 1,
      perPage: 20,
      active: true,
      sort: '-createdAt',
    }
  ) => {
    const r = await ccRequest({
      url:
        '/patients?' +
        `active${params.active}&page[number]=${params.page}&page[size]=${params.perPage}&sort=${params.sort}`,
      method: 'GET',
    })
    return r.patients
  },
  customFields: async (ids: number[]): Promise<GetCCPatientCustomField[]> =>
    ccRequest({ url: '/patientCustomFields?' + idsToQueryString(ids), method: 'get' }).then(
      (r: any) => r.patientCustomFields
    ),
}

//convert ids to query string
const idsToQueryString = (ids: number[]) => {
  return ids
    .map((id) => `ids[]=${id.toString().trim()}`)
    .join('&')
    .trim()
}

export const ccCustomfieldReq = {
  get: (id: number): Promise<GetCCCustomField> =>
    ccRequest({ url: `/customFields/${id}`, method: 'get' }).then((r: any) => r.customField),
  all: (): Promise<GetCCCustomField[]> =>
    ccRequest({ url: '/customFields/', method: 'get' }).then((r: any) => r.customFields),
}

export const ccUserReq = {
  get: async (id: number): Promise<GetCCUserType> => {
    const r = await ccRequest({
      url: `/users/${id}`,
      method: 'GET',
    })
    return r.user
  },
  all: async (): Promise<GetCCUserType[]> =>
    await ccRequest({ url: `/users`, method: `get` }).then((r) => r.users),
}

export const serviceReq = {
  all: (): Promise<GetCCServiceType[]> =>
    ccRequest({ url: '/services', method: 'get' }).then((r: any) => r.services),
  get: (id: number): Promise<GetCCServiceType> =>
    ccRequest({ url: `/services/${id}`, method: 'get' }).then((r: any) => r.service),
}
export const resourceReq = {
  all: (): Promise<GetCCResourceType[]> =>
    ccRequest({ url: '/resources', method: 'get' }).then((r: any) => r.resources),
  get: (id: number): Promise<GetCCResourceType> =>
    ccRequest({ url: `/resources/${id}`, method: 'get' }).then((r: any) => r.resource),
}
export const ccLocationReq = {
  all: (): Promise<GetCCLocationType[]> =>
    ccRequest({ url: '/locations', method: 'get' }).then((r: any) => r.locations),
  get: (id: number): Promise<GetCCLocationType> =>
    ccRequest({ url: `/locations/${id}`, method: 'get' }).then((r: any) => r.location),
}

export const ccAppointmentReq = {
  get: async (id: number): Promise<GetCCAppointmentType> => {
    return ccRequest({
      url: `/appointments/${id}`,
      method: 'get',
    }).then((r: any) => r.appointment)
  },
  post: async (payload: PostCCAppointmentType) => {
    return ccRequest({
      url: `/appointments`,
      method: 'POST',
      data: { appointment: payload },
    }).then((r: any) => r.appointment)
  },
  put: async (id: number, payload: PutCCAppointmentType) => {
    return ccRequest({
      url: `/appointments/${id}`,
      method: 'PUT',
      data: { appointment: payload },
    }).then((r: any) => r.appointment)
  },
  category: {
    get: async (id: number): Promise<GetCCAppointmentCategoryType> => {
      return ccRequest({
        url: `/appointmentCategories/${id}`,
        method: 'get',
      }).then((r: any) => r.appointmentCategory)
    },
    all: async (): Promise<GetCCAppointmentCategoryType[]> => {
      return ccRequest({
        url: `/appointmentCategories`,
        method: 'get',
      }).then((r: any) => r.appointmentCategories)
    },
  },
}

export const invoiceReq = {
  get: (ids: number[]): Promise<GetInvoiceType[]> =>
    ccRequest({ url: `/invoices?${idsToQueryString(ids)}`, method: 'get' }).then(
      (r: any) => r.invoices
    ),
}

export const paymentReq = {
  get: (ids: number[]): Promise<GetPaymentType[]> =>
    ccRequest({ url: `/payments?${idsToQueryString(ids)}`, method: 'get' }).then(
      (r: any) => r.payments
    ),
}
