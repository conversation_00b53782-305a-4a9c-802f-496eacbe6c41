#!/bin/bash
#On the first run we'll need the server password
# Change these variables to match your setup
REMOTE_USER="root" #SFTP/SSH username
REMOTE_SERVER="45.76.86.184" #SFTP/SSH server
LOCAL_DIR="$(realpath ./build)" #Local directory
REMOTE_DIR="/var/www/v3/" #Remote directory

echo

# Check if the local directory exists
if [ ! -d "$LOCAL_DIR" ]; then
    echo -e "\033[1m\e[31mLocal directory '$LOCAL_DIR' does not exist. Aborting upload process.\e[0m\033[0m"
    exit 1
fi

echo -e "\033[1m\e[31mMake sure you have your server password and set the information correctly within this file. \n(if you're running this first time.)\e[0m\033[0m"
echo

setup_ssh_key() {
    ssh-keygen -t rsa -N "" -f "$HOME/.ssh/id_rsa" > /dev/null 2>&1
    ssh-copy-id "$REMOTE_USER@$REMOTE_SERVER" > /dev/null 2>&1
    echo Added the rsa key to the server. > "$HOME/.ssh/$REMOTE_SERVER.txt" > /dev/null 2>&1
    echo "Starting upload process..."
}

transfer_rsa_id() {
    ssh-copy-id "$REMOTE_USER@$REMOTE_SERVER"  > /dev/null 2>&1
    echo Added the rsa key to the server. > "$HOME/.ssh/$REMOTE_SERVER.txt" > /dev/null 2>&1
}

prompt_for_ssh_key() {
    read -p "Do you want to set up an SSH key pair for passwordless authentication with the remote server? (y/n): " answer
    if [[ "$answer" =~ ^[Yy]$ ]]; then
        setup_ssh_key
    else
        echo "SSH key setup skipped. You may be prompted for the password during the file transfer."
    fi
}

# Check if SSH key pair already exists
if [ -f "$HOME/.ssh/id_rsa" ] && [ -f "$HOME/.ssh/id_rsa.pub" ]; then
    if [ ! -f "$HOME/.ssh/$REMOTE_SERVER.txt" ]; then
        transfer_rsa_id
    else
        echo "Starting upload process..."
    fi
else
    echo "We don't have SSH key pair, Creating SSH key pair..."
    echo
    prompt_for_ssh_key
fi
echo
echo -e "\033[1m\e[31mChecking remote dir is exist or not, if not, will create it.\e[0m\033[0m"

ssh "$REMOTE_USER@$REMOTE_SERVER" "mkdir -p $REMOTE_DIR"
echo

transfer_files_with_progress() {
    local total_files=$(find "$LOCAL_DIR" -type f | wc -l)
    local current_file=1
    local start_time=$(date +%s)
    echo -e "\033[1m\e[32mPreparing to upload $total_files files to upload, From \033[1m\e[31m'$LOCAL_DIR'\e[0m\033[0m \033[1m\e[32mto\e[0m\033[0m \033[1m\e[31m'$REMOTE_DIR'\e[0m\033[0m\e[0m\033[0m."
    echo
    find "$LOCAL_DIR" -type f -print0 | while IFS= read -r -d '' file; do
        local file_start_time=$(date +%s)
        echo -ne "\033[1mUploading files -> [\033[0m"
        for ((i=0; i<40; i++)); do
            if [ "$i" -lt $((current_file * 40 / total_files)) ]; then
                echo -ne "\033[1m\e[32m=\e[0m\033[0m"
            else
                echo -ne "\033[1m\e[31m-\e[0m\033[0m"
            fi
        done
        local elapsed_time=$(( $(date +%s) - start_time ))
        local avg_time_per_file=$(( elapsed_time / current_file ))
        local remaining_files=$(( total_files - current_file ))
        local eta=$(( avg_time_per_file * remaining_files ))

        echo -ne "\033[1m] \e[33m$(($current_file * 100 / total_files))% [$current_file/$total_files]\e[0m\033[0m \033[1m\e[31mET: $(date -u -d @$elapsed_time +'%H:%M:%S')\e[0m\033[0m - \033[1m\e[32mETA: $(date -u -d @$eta +'%H:%M:%S')\e[0m\033[0m          \r"

        scp -r "$file" "$REMOTE_USER@$REMOTE_SERVER:$REMOTE_DIR" > /dev/null 2>&1
        ((current_file++))
    done

    echo
}
transfer_files_with_progress

echo
echo "All file and directory has been transferred successfully."
echo

# Function to prompt for PM2 process reload
prompt_for_pm2_reload() {
    read -p "Do you want to reload a PM2 process? (y/n): " reload_answer
    if [[ "$reload_answer" =~ ^[Yy]$ ]]; then
        echo "Listing all PM2 processes..."
        ssh "$REMOTE_USER@$REMOTE_SERVER" "pm2 list"
        read -p "Enter the PID of the process you want to reload: " pid
        ssh "$REMOTE_USER@$REMOTE_SERVER" "pm2 reload $pid"
        echo "PM2 process with PID $pid has been reloaded."
    else
        echo "PM2 process reload skipped."
    fi
}

prompt_for_pm2_reload

sleep 5
