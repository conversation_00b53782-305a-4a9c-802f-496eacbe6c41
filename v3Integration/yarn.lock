# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@aashutoshrathi/word-wrap@npm:^1.2.3":
  version: 1.2.6
  resolution: "@aashutoshrathi/word-wrap@npm:1.2.6"
  checksum: 10c0/53c2b231a61a46792b39a0d43bc4f4f776bb4542aa57ee04930676802e5501282c2fc8aac14e4cd1f1120ff8b52616b6ff5ab539ad30aa2277d726444b71619f
  languageName: node
  linkType: hard

"@adonisjs/ace@npm:^11.3.1":
  version: 11.3.1
  resolution: "@adonisjs/ace@npm:11.3.1"
  dependencies:
    "@poppinss/cliui": "npm:^3.0.2"
    "@poppinss/prompts": "npm:^2.0.2"
    "@poppinss/utils": "npm:^4.0.4"
    fs-extra: "npm:^10.1.0"
    getopts: "npm:^2.3.0"
    leven: "npm:^3.1.0"
    mustache: "npm:^4.2.0"
    slash: "npm:^3.0.0"
    term-size: "npm:^2.2.1"
  peerDependencies:
    "@adonisjs/application": ^5.0.0
  checksum: 10c0/f98642826083ba8232500cf16b54a2ed48fcc6ec5a68c2a0358f466abbf69ad5db020fadfbc95608ece42a1820ca78dedab7e36826e47bb2b72ad795e7cbc8d2
  languageName: node
  linkType: hard

"@adonisjs/application@npm:^5.2.5, @adonisjs/application@npm:^5.3.0":
  version: 5.3.0
  resolution: "@adonisjs/application@npm:5.3.0"
  dependencies:
    "@adonisjs/config": "npm:^3.0.9"
    "@adonisjs/env": "npm:^3.0.9"
    "@adonisjs/fold": "npm:^8.2.0"
    "@adonisjs/logger": "npm:^4.1.5"
    "@adonisjs/profiler": "npm:^6.0.9"
    "@poppinss/utils": "npm:^5.0.0"
    semver: "npm:^7.3.8"
  checksum: 10c0/4cb44c2b1ddb01c8cef342932619bed39b9219c94b6d95aa5a5d8bbfc80d08c792dec734c4113c98402ff51c90a91cb65e0d786d82a8ae70ccb0ff84f3b0a645
  languageName: node
  linkType: hard

"@adonisjs/assembler@npm:^5.9.5":
  version: 5.9.5
  resolution: "@adonisjs/assembler@npm:5.9.5"
  dependencies:
    "@adonisjs/application": "npm:^5.2.5"
    "@adonisjs/env": "npm:^3.0.9"
    "@adonisjs/ioc-transformer": "npm:^2.3.4"
    "@adonisjs/require-ts": "npm:^2.0.13"
    "@adonisjs/sink": "npm:^5.4.2"
    "@poppinss/chokidar-ts": "npm:^3.3.5"
    "@poppinss/cliui": "npm:^3.0.5"
    "@poppinss/utils": "npm:^5.0.0"
    cpy: "npm:^8.1.2"
    emittery: "npm:^0.13.1"
    execa: "npm:^5.1.1"
    fs-extra: "npm:^10.1.0"
    get-port: "npm:^5.1.1"
    glob-parent: "npm:^6.0.2"
    has-yarn: "npm:^2.1.0"
    picomatch: "npm:^2.3.1"
    slash: "npm:^3.0.0"
  peerDependencies:
    "@adonisjs/core": ^5.1.0
  checksum: 10c0/a327f4a146d617a697e543e4e2db231ab7e05db306d9be3ee71f5010df68021ef0d9efa8ba8fbfadb39ab703b619be1ca625390c87d24f67c72026d76ffbea36
  languageName: node
  linkType: hard

"@adonisjs/bodyparser@npm:^8.1.7":
  version: 8.1.9
  resolution: "@adonisjs/bodyparser@npm:8.1.9"
  dependencies:
    "@poppinss/co-body": "npm:^1.1.3"
    "@poppinss/multiparty": "npm:^2.0.1"
    "@poppinss/utils": "npm:^5.0.0"
    bytes: "npm:^3.1.2"
    file-type: "npm:^16.5.4"
    fs-extra: "npm:^10.1.0"
    media-typer: "npm:^1.1.0"
    slash: "npm:^3.0.0"
  peerDependencies:
    "@adonisjs/application": ^5.0.0
    "@adonisjs/drive": ^2.0.0
    "@adonisjs/http-server": ^5.0.0
  checksum: 10c0/04796076d10597aba4b8ba30d35f1e6f1e36c26952b7da91a88a91f8eb3cbdc905bd56856d5e39d88a792f8e944eb0aa571bdde330ab95131b9f31fec9b447c1
  languageName: node
  linkType: hard

"@adonisjs/config@npm:^3.0.9":
  version: 3.0.9
  resolution: "@adonisjs/config@npm:3.0.9"
  dependencies:
    "@poppinss/utils": "npm:^5.0.0"
  checksum: 10c0/a4cfab6ab26e6a7e6d40a0ec1b132f9b779648fc73facc703fdf9c75c4d4c17b00ba748fc949e8150c96c486b458a0875337f943664b5ace7bf1cdf0a8924a94
  languageName: node
  linkType: hard

"@adonisjs/core@npm:^5.8.0":
  version: 5.9.0
  resolution: "@adonisjs/core@npm:5.9.0"
  dependencies:
    "@adonisjs/ace": "npm:^11.3.1"
    "@adonisjs/application": "npm:^5.3.0"
    "@adonisjs/bodyparser": "npm:^8.1.7"
    "@adonisjs/drive": "npm:^2.3.0"
    "@adonisjs/encryption": "npm:^4.0.8"
    "@adonisjs/events": "npm:^7.2.1"
    "@adonisjs/hash": "npm:^7.2.2"
    "@adonisjs/http-server": "npm:^5.12.0"
    "@adonisjs/validator": "npm:^12.4.1"
    "@poppinss/cliui": "npm:^3.0.5"
    "@poppinss/manager": "npm:^5.0.2"
    "@poppinss/utils": "npm:^5.0.0"
    fs-extra: "npm:^10.1.0"
    macroable: "npm:^7.0.2"
    memfs: "npm:^3.4.12"
    serve-static: "npm:^1.15.0"
    stringify-attributes: "npm:^2.0.0"
  checksum: 10c0/4483ec3db9c746a36e9d5575866bf58401b1bf13ce5f3756c1905ad81cab9ab24e462602f0b5d9959c4175ef753efc0cd8cd263638ef85a1f188397ecce72c98
  languageName: node
  linkType: hard

"@adonisjs/drive@npm:^2.3.0":
  version: 2.3.0
  resolution: "@adonisjs/drive@npm:2.3.0"
  dependencies:
    "@poppinss/manager": "npm:^5.0.2"
    "@poppinss/utils": "npm:^5.0.0"
    "@types/fs-extra": "npm:^9.0.13"
    etag: "npm:^1.8.1"
    fs-extra: "npm:^10.1.0"
    memfs: "npm:^3.4.7"
  peerDependencies:
    "@adonisjs/application": ^5.0.0
    "@adonisjs/http-server": ^5.0.0
  checksum: 10c0/50acf118b47951d2d8fd5ca035b4116569f8d288bd3e61c566e8b01bb03ed802edf53001662689eac20939577ac8e6154e1e9369c1c0797dc07ad5fc951b117b
  languageName: node
  linkType: hard

"@adonisjs/encryption@npm:^4.0.8":
  version: 4.0.8
  resolution: "@adonisjs/encryption@npm:4.0.8"
  dependencies:
    "@poppinss/utils": "npm:^4.0.3"
  peerDependencies:
    "@adonisjs/application": ^5.0.0
  checksum: 10c0/7a7b6a481e86ebca17b67b344dc8aeade5fa6db4c42c867ef86b0c539d35ddbead5c7a1dbb4a8d66efd0b939c3653e32e480bf059d97b6ebed2149308aa6fb9a
  languageName: node
  linkType: hard

"@adonisjs/env@npm:^3.0.9":
  version: 3.0.9
  resolution: "@adonisjs/env@npm:3.0.9"
  dependencies:
    "@poppinss/utils": "npm:^4.0.2"
    dotenv: "npm:^16.0.0"
    validator: "npm:^13.7.0"
  checksum: 10c0/722753e0967ecfac9224b064d07a8599f534289b3bd4a102f7f4b97e71c649d71aba36bc7bbcdf613d1834dfe6ca85bac3deb8301d623cb97ac7b87e7eb080e2
  languageName: node
  linkType: hard

"@adonisjs/events@npm:^7.2.1":
  version: 7.2.1
  resolution: "@adonisjs/events@npm:7.2.1"
  dependencies:
    emittery: "npm:^0.10.0"
  peerDependencies:
    "@adonisjs/application": ^5.0.0
  checksum: 10c0/40a6f5523c593e2de223073022356eaf6fdb7495b7032e2a24a8b961930a65623794a9221b6c0633320872b0a82ede44e83259e5ccc4b0a0f934cc1d38fd226e
  languageName: node
  linkType: hard

"@adonisjs/fold@npm:^8.2.0":
  version: 8.2.0
  resolution: "@adonisjs/fold@npm:8.2.0"
  dependencies:
    "@poppinss/utils": "npm:^4.0.4"
  checksum: 10c0/ce0da12956a6f51d96a610148589bb7b522f3c0a21bfaf050b67fdbcc769fb5dd6b5dd169b83795b96622c6856961de6d7a462ae09a2da9cc78ed81c95ece6b3
  languageName: node
  linkType: hard

"@adonisjs/hash@npm:^7.2.2":
  version: 7.2.2
  resolution: "@adonisjs/hash@npm:7.2.2"
  dependencies:
    "@phc/format": "npm:^1.0.0"
    "@poppinss/manager": "npm:^5.0.2"
    "@poppinss/utils": "npm:^5.0.0"
  peerDependencies:
    "@adonisjs/application": ^5.0.0
  checksum: 10c0/119061e22f4d7e3841a5ff2cfad1dbb3c4a96bba9edb90e1a485b735d1db2c149bcb5fbce0e39d97bbb850b3b52e9a4b3ac31288b84104617f7b76f5c0944119
  languageName: node
  linkType: hard

"@adonisjs/http-server@npm:^5.12.0":
  version: 5.12.0
  resolution: "@adonisjs/http-server@npm:5.12.0"
  dependencies:
    "@poppinss/matchit": "npm:^3.1.2"
    "@poppinss/utils": "npm:^5.0.0"
    accepts: "npm:^1.3.8"
    co-compose: "npm:^7.0.2"
    content-disposition: "npm:^0.5.4"
    cookie: "npm:^0.5.0"
    destroy: "npm:^1.2.0"
    encodeurl: "npm:^1.0.2"
    etag: "npm:^1.8.1"
    fresh: "npm:^0.5.2"
    haye: "npm:^3.0.0"
    macroable: "npm:^7.0.2"
    mime-types: "npm:^2.1.35"
    ms: "npm:^2.1.3"
    on-finished: "npm:^2.4.1"
    pluralize: "npm:^8.0.0"
    proxy-addr: "npm:^2.0.7"
    qs: "npm:^6.11.0"
    tmp-cache: "npm:^1.1.0"
    type-is: "npm:^1.6.18"
    vary: "npm:^1.1.2"
  peerDependencies:
    "@adonisjs/application": ^5.0.0
    "@adonisjs/encryption": ^4.0.0
  checksum: 10c0/d3c754d55b8f0f7233c1f8f1fe8910cc98052ac171a31f884ba0fb53038f25ff1fd7f67cbff6065d2aa5ca2454ff3c806d8016e8aad5b711594f759880a06f23
  languageName: node
  linkType: hard

"@adonisjs/ioc-transformer@npm:^2.3.4":
  version: 2.3.4
  resolution: "@adonisjs/ioc-transformer@npm:2.3.4"
  checksum: 10c0/1c2b3c05cc85ae79f65e95b8ac40b1ac5f0179cf0e94737131966848017031614996928b57ef13db1910d989e0fa50739188f0bffd8070989296f629e8b4b715
  languageName: node
  linkType: hard

"@adonisjs/logger@npm:^4.1.5":
  version: 4.1.5
  resolution: "@adonisjs/logger@npm:4.1.5"
  dependencies:
    "@poppinss/utils": "npm:^5.0.0"
    "@types/pino": "npm:^6.3.12"
    abstract-logging: "npm:^2.0.1"
    pino: "npm:^6.14.0"
  checksum: 10c0/baaa8b45f6cfdcb1e7f4e8ce85ef669f437f646b5f633f003e3a6ed5a28aead4a435d5c46dfaf83f497c72ce7e0f11bab04cc260080aa8d57d2ae027421411b1
  languageName: node
  linkType: hard

"@adonisjs/lucid@npm:^18.4.0":
  version: 18.4.0
  resolution: "@adonisjs/lucid@npm:18.4.0"
  dependencies:
    "@faker-js/faker": "npm:^8.0.1"
    "@poppinss/hooks": "npm:^5.0.3"
    "@poppinss/utils": "npm:^5.0.0"
    fast-deep-equal: "npm:^3.1.3"
    igniculus: "npm:^1.5.0"
    knex: "npm:^2.4.2"
    knex-dynamic-connection: "npm:^3.0.1"
    luxon: "npm:^3.3.0"
    macroable: "npm:^7.0.2"
    pretty-hrtime: "npm:^1.0.3"
    qs: "npm:^6.11.2"
    slash: "npm:^3.0.0"
    tarn: "npm:^3.0.2"
  peerDependencies:
    "@adonisjs/core": ^5.1.0
  checksum: 10c0/2250fa17b5a7a6806274482093875bb83d2e2f8c5f49c4c8db9386ef707a0c5f029fc42b026cb5745a45fd988988aa03f3ed12085ad58620b7b340237bff4493
  languageName: node
  linkType: hard

"@adonisjs/profiler@npm:^6.0.9":
  version: 6.0.9
  resolution: "@adonisjs/profiler@npm:6.0.9"
  dependencies:
    "@poppinss/utils": "npm:^4.0.3"
    jest-worker: "npm:^27.5.1"
  peerDependencies:
    "@adonisjs/logger": ^4.0.0
  checksum: 10c0/300384e5e983fd9db7ab244cdfb1f7987f36eba45a6471903da2dcea4f444f627aa06e94d5d6c7a2bd39313cfeebe037e1f82f876c60749348eda68348fef349
  languageName: node
  linkType: hard

"@adonisjs/repl@npm:^3.1.0":
  version: 3.1.11
  resolution: "@adonisjs/repl@npm:3.1.11"
  dependencies:
    "@poppinss/colors": "npm:^3.0.2"
    node-repl-await: "npm:^0.1.2"
    parse-imports: "npm:0.0.5"
    string-width: "npm:^4.2.2"
  peerDependencies:
    "@adonisjs/core": ^5.1.0
  checksum: 10c0/7beff94fbeb5181c33ca2ecef71e714e870a5c6b0e2c9e1bb5ac9c0268aa1f23501f240b77de6862e3db7a0ffbecf5f011ac76e89f22561c8f2a87eb40c149cd
  languageName: node
  linkType: hard

"@adonisjs/require-ts@npm:^2.0.13":
  version: 2.0.13
  resolution: "@adonisjs/require-ts@npm:2.0.13"
  dependencies:
    "@poppinss/utils": "npm:^5.0.0"
    debug: "npm:^4.3.4"
    find-cache-dir: "npm:^3.3.2"
    fs-extra: "npm:^10.1.0"
    normalize-path: "npm:^3.0.0"
    pirates: "npm:^4.0.5"
    rev-hash: "npm:^3.0.0"
    source-map-support: "npm:^0.5.21"
  checksum: 10c0/9837726cdffdb71256103afc38bfad76a9d066646f658c076c6421eb1350dd8edd6dcfeb142cf16294867a3e1dff412c9529c655acb31408016cdb4086fc56ef
  languageName: node
  linkType: hard

"@adonisjs/session@npm:^6.4.0":
  version: 6.4.0
  resolution: "@adonisjs/session@npm:6.4.0"
  dependencies:
    "@poppinss/utils": "npm:^4.0.4"
    fs-extra: "npm:^10.1.0"
  peerDependencies:
    "@adonisjs/core": ^5.8.0
  checksum: 10c0/a3698fba14acc27aa3127a2ed2ec11739810c70966be218647d0d32ecd963e2235576b14773ec07786554afc761aab3a1456c966d8b197b56519ad830460e126
  languageName: node
  linkType: hard

"@adonisjs/sink@npm:^5.4.2":
  version: 5.4.3
  resolution: "@adonisjs/sink@npm:5.4.3"
  dependencies:
    "@poppinss/cliui": "npm:^3.0.5"
    "@poppinss/prompts": "npm:^2.0.2"
    "@poppinss/utils": "npm:^5.0.0"
    cp-file: "npm:^9.1.0"
    fs-extra: "npm:^10.1.0"
    marked: "npm:^4.2.12"
    marked-terminal: "npm:^5.1.1"
    mrm-core: "npm:7.1.13"
    mustache: "npm:^4.2.0"
    open: "npm:^8.4.2"
  peerDependencies:
    "@adonisjs/application": ^5.0.0
  checksum: 10c0/f306ca484aa2d07d8a148dcf845b132409524839c5f24ecff1bf9437783fa55703b236793b11d22e71860b2ce8daa6280cfee3b7173efa86596902a7bec2bbbb
  languageName: node
  linkType: hard

"@adonisjs/validator@npm:^12.4.1":
  version: 12.4.2
  resolution: "@adonisjs/validator@npm:12.4.2"
  dependencies:
    "@poppinss/utils": "npm:^5.0.0"
    "@types/luxon": "npm:^3.0.1"
    "@types/validator": "npm:^13.7.10"
    luxon: "npm:^3.0.3"
    normalize-url: "npm:^6.1.0"
    tmp-cache: "npm:^1.1.0"
    validator: "npm:^13.7.0"
  peerDependencies:
    "@adonisjs/application": ^5.0.0
    "@adonisjs/bodyparser": ^8.0.0
    "@adonisjs/http-server": ^5.0.0
  checksum: 10c0/0a6cf7b1e369717b1d553f137f2d9e62007fddbd217c8b162243bd01dcaa69ed904f0f43795acb2068071f4b2e1ae59bd685cccb96d28f4362a6aaee596ca23a
  languageName: node
  linkType: hard

"@apidevtools/json-schema-ref-parser@npm:^9.0.6":
  version: 9.1.2
  resolution: "@apidevtools/json-schema-ref-parser@npm:9.1.2"
  dependencies:
    "@jsdevtools/ono": "npm:^7.1.3"
    "@types/json-schema": "npm:^7.0.6"
    call-me-maybe: "npm:^1.0.1"
    js-yaml: "npm:^4.1.0"
  checksum: 10c0/ebf952eb2e00bf0919f024e72897e047fd5012f0a9e47ac361873f6de0a733b9334513cdbc73205a6b43ac4a652b8c87f55e489c39b2d60bd0bc1cb2b411e218
  languageName: node
  linkType: hard

"@apidevtools/openapi-schemas@npm:^2.0.4":
  version: 2.1.0
  resolution: "@apidevtools/openapi-schemas@npm:2.1.0"
  checksum: 10c0/f4aa0f9df32e474d166c84ef91bceb18fa1c4f44b5593879529154ef340846811ea57dc2921560f157f692262827d28d988dd6e19fb21f00320e9961964176b4
  languageName: node
  linkType: hard

"@apidevtools/swagger-methods@npm:^3.0.2":
  version: 3.0.2
  resolution: "@apidevtools/swagger-methods@npm:3.0.2"
  checksum: 10c0/8c390e8e50c0be7787ba0ba4c3758488bde7c66c2d995209b4b48c1f8bc988faf393cbb24a4bd1cd2d42ce5167c26538e8adea5c85eb922761b927e4dab9fa1c
  languageName: node
  linkType: hard

"@apidevtools/swagger-parser@npm:10.0.3":
  version: 10.0.3
  resolution: "@apidevtools/swagger-parser@npm:10.0.3"
  dependencies:
    "@apidevtools/json-schema-ref-parser": "npm:^9.0.6"
    "@apidevtools/openapi-schemas": "npm:^2.0.4"
    "@apidevtools/swagger-methods": "npm:^3.0.2"
    "@jsdevtools/ono": "npm:^7.1.3"
    call-me-maybe: "npm:^1.0.1"
    z-schema: "npm:^5.0.1"
  peerDependencies:
    openapi-types: ">=7"
  checksum: 10c0/3b43f719c2d647ac8dcf30f132834d413ce21cbf7a8d9c3b35ec91149dd25d608c8fd892358fcd61a8edd8c5140a7fb13676f948e2d87067d081a47b8c7107e9
  languageName: node
  linkType: hard

"@arr/every@npm:^1.0.0":
  version: 1.0.1
  resolution: "@arr/every@npm:1.0.1"
  checksum: 10c0/f9769bc97eca37d1c0dedfda4c17506c0e1269d95c15a7478a49c4d84d43512a3f350b71c95e045cf4113d3398ebd6d298f2eea09196a1ed61c311bc4e6964e7
  languageName: node
  linkType: hard

"@colors/colors@npm:1.5.0":
  version: 1.5.0
  resolution: "@colors/colors@npm:1.5.0"
  checksum: 10c0/eb42729851adca56d19a08e48d5a1e95efd2a32c55ae0323de8119052be0510d4b7a1611f2abcbf28c044a6c11e6b7d38f99fccdad7429300c37a8ea5fb95b44
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0":
  version: 4.4.0
  resolution: "@eslint-community/eslint-utils@npm:4.4.0"
  dependencies:
    eslint-visitor-keys: "npm:^3.3.0"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10c0/7e559c4ce59cd3a06b1b5a517b593912e680a7f981ae7affab0d01d709e99cd5647019be8fafa38c350305bc32f1f7d42c7073edde2ab536c745e365f37b607e
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.4.0":
  version: 4.6.1
  resolution: "@eslint-community/regexpp@npm:4.6.1"
  checksum: 10c0/4ab30d948881ad01311a24866afb30cf7481c594ac6aecebf7fbea2f29bd42d403d5676b69790df3d333620d863df29358a0282f10726f3506e5ef52471204be
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^2.1.0":
  version: 2.1.0
  resolution: "@eslint/eslintrc@npm:2.1.0"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^9.6.0"
    globals: "npm:^13.19.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 10c0/6ffbc3e7867b377754492539af0e2f5b55645a2c67279a70508fe09080bc76d49ba64b579e59a2a04014f84d0768301736fbcdd94c7b3ad4f0e648c32bf21e43
  languageName: node
  linkType: hard

"@eslint/js@npm:8.44.0":
  version: 8.44.0
  resolution: "@eslint/js@npm:8.44.0"
  checksum: 10c0/ce7b966f8804228e4d5725d44d3c8fb7fc427176f077401323a02e082f628d207133a25704330e610ebe3254fdf1acb186f779d1242fd145a758fdcc4486a660
  languageName: node
  linkType: hard

"@faker-js/faker@npm:^8.0.1":
  version: 8.0.2
  resolution: "@faker-js/faker@npm:8.0.2"
  checksum: 10c0/a7851d4abfcd93c8c9d40ca7f1a7ad2218b9dcac1b11afe9dcf0fe38f5c1d91610e56bcedf5193a5b178dc10e052baab35977853342e84ec12b1fb27b425b356
  languageName: node
  linkType: hard

"@humanwhocodes/config-array@npm:^0.11.10":
  version: 0.11.10
  resolution: "@humanwhocodes/config-array@npm:0.11.10"
  dependencies:
    "@humanwhocodes/object-schema": "npm:^1.2.1"
    debug: "npm:^4.1.1"
    minimatch: "npm:^3.0.5"
  checksum: 10c0/9e307a49a5baa28beb243d2c14c145f288fccd6885f4c92a9055707057ec40980242256b2a07c976cfa6c75f7081da111a40a9844d1ca8daeff2302f8b640e76
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 10c0/909b69c3b86d482c26b3359db16e46a32e0fb30bd306a3c176b8313b9e7313dba0f37f519de6aa8b0a1921349e505f259d19475e123182416a506d7f87e7f529
  languageName: node
  linkType: hard

"@humanwhocodes/object-schema@npm:^1.2.1":
  version: 1.2.1
  resolution: "@humanwhocodes/object-schema@npm:1.2.1"
  checksum: 10c0/c3c35fdb70c04a569278351c75553e293ae339684ed75895edc79facc7276e351115786946658d78133130c0cca80e57e2203bc07f8fa7fe7980300e8deef7db
  languageName: node
  linkType: hard

"@ioredis/commands@npm:^1.1.1":
  version: 1.2.0
  resolution: "@ioredis/commands@npm:1.2.0"
  checksum: 10c0/a5d3c29dd84d8a28b7c67a441ac1715cbd7337a7b88649c0f17c345d89aa218578d2b360760017c48149ef8a70f44b051af9ac0921a0622c2b479614c4f65b36
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@japa/api-client@npm:^1.4.2":
  version: 1.4.4
  resolution: "@japa/api-client@npm:1.4.4"
  dependencies:
    "@poppinss/hooks": "npm:^6.0.2-0"
    "@types/superagent": "npm:^4.1.16"
    cookie: "npm:^0.5.0"
    macroable: "npm:^7.0.2"
    set-cookie-parser: "npm:^2.5.1"
    superagent: "npm:^8.0.9"
  peerDependencies:
    "@japa/runner": ^2.2.3
  checksum: 10c0/1aed2de55bf174dc756dc1ad4fe476be3929e2c10a8e044e902af1eb8b9540d78b66ec6759080077bd67f49d6f6cd59688f00916a5444497dd98093a9a78affc
  languageName: node
  linkType: hard

"@japa/assert@npm:^1.3.6":
  version: 1.4.1
  resolution: "@japa/assert@npm:1.4.1"
  dependencies:
    "@types/chai": "npm:^4.3.4"
    api-contract-validator: "npm:^2.2.8"
    chai: "npm:^4.3.7"
    macroable: "npm:^7.0.2"
  peerDependencies:
    "@japa/runner": ^2.1.1
  checksum: 10c0/711ad9ed96373710c8fedf92105008504e868ae998f7bcde30d0de6b3662c5ed9738243c420240c9b9c1931d9dc34ea795a1045264e577e824573e2529d8bdad
  languageName: node
  linkType: hard

"@japa/base-reporter@npm:^1.1.1":
  version: 1.1.2
  resolution: "@japa/base-reporter@npm:1.1.2"
  dependencies:
    "@japa/errors-printer": "npm:^2.1.0"
    "@poppinss/cliui": "npm:^3.0.5"
    ms: "npm:^2.1.3"
  checksum: 10c0/dcb7986f2571f54684757d8901a77a13f71fae341b1f3edb60dd49e87670e91a297c77051934896952a6a9bb8af50c57be93d836927d921f687c1d36c8e03109
  languageName: node
  linkType: hard

"@japa/core@npm:^7.3.2":
  version: 7.3.3
  resolution: "@japa/core@npm:7.3.3"
  dependencies:
    "@poppinss/hooks": "npm:^6.0.2-0"
    async-retry: "npm:^1.3.3"
    emittery: "npm:^0.13.1"
    macroable: "npm:^7.0.2"
    time-span: "npm:^4.0.0"
  checksum: 10c0/e40638c6f6c3ff8587082280bb0567c3b4d62ecef4ba32fa3d079633b3ab8eec817598790b8348aaf8b4027034222c149920299606dc4771f23403e29da926b2
  languageName: node
  linkType: hard

"@japa/errors-printer@npm:^2.1.0":
  version: 2.1.0
  resolution: "@japa/errors-printer@npm:2.1.0"
  dependencies:
    "@poppinss/cliui": "npm:^3.0.5"
    jest-diff: "npm:^29.4.1"
    youch: "npm:^3.2.3"
    youch-terminal: "npm:^2.2.0"
  checksum: 10c0/5a96e39825c5fbe1bb68ca2d785503b540d695df42a36e7ae3b445b128a994c2bbee8f62dcc33772e9a7dc5574140d6a15664698e82712dd303fd9edb2715aef
  languageName: node
  linkType: hard

"@japa/preset-adonis@npm:^1.2.0":
  version: 1.2.0
  resolution: "@japa/preset-adonis@npm:1.2.0"
  dependencies:
    "@japa/api-client": "npm:^1.4.2"
    "@japa/assert": "npm:^1.3.6"
    "@japa/run-failed-tests": "npm:^1.1.0"
    "@japa/spec-reporter": "npm:^1.3.0"
  peerDependencies:
    "@adonisjs/core": ^5.0.0
    "@japa/runner": ^2.0.0
  checksum: 10c0/e8dd160da41d0906dbfaabbded6be842eecc134e1ffdde82fb0a00225f2970bb33e61e14ab6b3dcc895dd1c5d85bd938ae1c2faa28b1ebaec569bb3944d2b204
  languageName: node
  linkType: hard

"@japa/run-failed-tests@npm:^1.1.0":
  version: 1.1.1
  resolution: "@japa/run-failed-tests@npm:1.1.1"
  dependencies:
    "@poppinss/cliui": "npm:^3.0.5"
    find-cache-dir: "npm:^3.3.2"
    fs-extra: "npm:^11.1.0"
  peerDependencies:
    "@japa/runner": ^2.2.3
  checksum: 10c0/22db0c0c9d22c683b87275fce8ac3bf2bb9b6d2ef5d52fe3a3bc9b8361a3f1a32c8c504295b96b0256460d131b3b754e7c545828801fb1b1e5a6ee2679e8e683
  languageName: node
  linkType: hard

"@japa/runner@npm:^2.5.1":
  version: 2.5.1
  resolution: "@japa/runner@npm:2.5.1"
  dependencies:
    "@japa/core": "npm:^7.3.2"
    "@japa/errors-printer": "npm:^2.1.0"
    "@poppinss/cliui": "npm:^3.0.5"
    "@poppinss/hooks": "npm:^6.0.2-0"
    fast-glob: "npm:^3.2.12"
    getopts: "npm:^2.3.0"
    inclusion: "npm:^1.0.1"
  checksum: 10c0/3fd8245f45f9875a66043276f49a7340ba064e4266dfd325daca3d510b62c4570f1329e4085fe3fcb2e78676f9114c6b66a32f1fc2d20c62f80bd6808e60698d
  languageName: node
  linkType: hard

"@japa/spec-reporter@npm:^1.3.0":
  version: 1.3.3
  resolution: "@japa/spec-reporter@npm:1.3.3"
  dependencies:
    "@japa/base-reporter": "npm:^1.1.1"
    "@japa/errors-printer": "npm:^2.1.0"
    "@poppinss/cliui": "npm:^3.0.5"
    ms: "npm:^2.1.3"
  checksum: 10c0/3d29b50ddd3744c7c392c21e65f2c9d3cb4060743dc2d7aaff2b97f72bbbacfaccae88d00233ff179ab901cd425b068e2f1cf6ceab9ecca40f29b0d0e6f3a588
  languageName: node
  linkType: hard

"@jest/schemas@npm:^29.6.0":
  version: 29.6.0
  resolution: "@jest/schemas@npm:29.6.0"
  dependencies:
    "@sinclair/typebox": "npm:^0.27.8"
  checksum: 10c0/8671b1fb59c4296204d335190e8451e1983d9f2db6dbbd38f838c6c273fd222fc11e4e0df04adfb6169d36acfb9693d525db136653ec04e6884180f45a131d8f
  languageName: node
  linkType: hard

"@jest/types@npm:^25.5.0":
  version: 25.5.0
  resolution: "@jest/types@npm:25.5.0"
  dependencies:
    "@types/istanbul-lib-coverage": "npm:^2.0.0"
    "@types/istanbul-reports": "npm:^1.1.1"
    "@types/yargs": "npm:^15.0.0"
    chalk: "npm:^3.0.0"
  checksum: 10c0/f47c6e98c99d3fd562f2be6c339f41d3c7092e9587b8524fe71411f9c8b8e71f50475278a10e534f56c729ccd3e3b55e3aa20e4b0a2c5c47ded1ba53e0aef286
  languageName: node
  linkType: hard

"@jsdevtools/ono@npm:^7.1.3":
  version: 7.1.3
  resolution: "@jsdevtools/ono@npm:7.1.3"
  checksum: 10c0/a9f7e3e8e3bc315a34959934a5e2f874c423cf4eae64377d3fc9de0400ed9f36cb5fd5ebce3300d2e8f4085f557c4a8b591427a583729a87841fda46e6c216b9
  languageName: node
  linkType: hard

"@mrmlnc/readdir-enhanced@npm:^2.2.1":
  version: 2.2.1
  resolution: "@mrmlnc/readdir-enhanced@npm:2.2.1"
  dependencies:
    call-me-maybe: "npm:^1.0.1"
    glob-to-regexp: "npm:^0.3.0"
  checksum: 10c0/01840f3c85e9a7cd0ed5e038cc00e7518809b9edda950598e22b1c9804832e39a75707aaa6eb0b023e72182a85e00041c7a01483e425b16257bd3d5e4c788d86
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-darwin-arm64@npm:3.0.2":
  version: 3.0.2
  resolution: "@msgpackr-extract/msgpackr-extract-darwin-arm64@npm:3.0.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-darwin-x64@npm:3.0.2":
  version: 3.0.2
  resolution: "@msgpackr-extract/msgpackr-extract-darwin-x64@npm:3.0.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-linux-arm64@npm:3.0.2":
  version: 3.0.2
  resolution: "@msgpackr-extract/msgpackr-extract-linux-arm64@npm:3.0.2"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-linux-arm@npm:3.0.2":
  version: 3.0.2
  resolution: "@msgpackr-extract/msgpackr-extract-linux-arm@npm:3.0.2"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-linux-x64@npm:3.0.2":
  version: 3.0.2
  resolution: "@msgpackr-extract/msgpackr-extract-linux-x64@npm:3.0.2"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-win32-x64@npm:3.0.2":
  version: 3.0.2
  resolution: "@msgpackr-extract/msgpackr-extract-win32-x64@npm:3.0.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10c0/732c3b6d1b1e967440e65f284bd06e5821fedf10a1bea9ed2bb75956ea1f30e08c44d3def9d6a230666574edbaf136f8cfd319c14fd1f87c66e6a44449afb2eb
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10c0/88dafe5e3e29a388b07264680dc996c17f4bda48d163a9d4f5c1112979f0ce8ec72aa7116122c350b4e7976bc5566dc3ddb579be1ceaacc727872eb4ed93926d
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:^1.1.2":
  version: 1.1.3
  resolution: "@nodelib/fs.stat@npm:1.1.3"
  checksum: 10c0/dc28ccae626e817a61b1544285b0f86c4e94a4a23db777c2949f78866ec57b1e1ccd5554bc3ed8e965df0646b1019e184315d32e98428c15eef7409974b17598
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3, @nodelib/fs.walk@npm:^1.2.8":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10c0/db9de047c3bb9b51f9335a7bb46f4fcfb6829fb628318c12115fbaf7d369bfce71c15b103d1fc3b464812d936220ee9bc1c8f762d032c9f6be9acc99249095b1
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^3.1.0":
  version: 3.1.0
  resolution: "@npmcli/fs@npm:3.1.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/162b4a0b8705cd6f5c2470b851d1dc6cd228c86d2170e1769d738c1fbb69a87160901411c3c035331e9e99db72f1f1099a8b734bf1637cc32b9a5be1660e4e1e
  languageName: node
  linkType: hard

"@phc/format@npm:^1.0.0":
  version: 1.0.0
  resolution: "@phc/format@npm:1.0.0"
  checksum: 10c0/56cec2687ac3767298f77b2202c0d6f282878c1375948b01da16e03475dc293628847d33150469a793d55095700dce65ac1760ada271deeab15bd0c6d1ceb306
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@pkgr/utils@npm:^2.3.1":
  version: 2.4.2
  resolution: "@pkgr/utils@npm:2.4.2"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    fast-glob: "npm:^3.3.0"
    is-glob: "npm:^4.0.3"
    open: "npm:^9.1.0"
    picocolors: "npm:^1.0.0"
    tslib: "npm:^2.6.0"
  checksum: 10c0/7c3e68f6405a1d4c51f418d8d580e71d7bade2683d5db07e8413d8e57f7e389047eda44a2341f77a1b3085895fca7676a9d45e8812a58312524f8c4c65d501be
  languageName: node
  linkType: hard

"@poppinss/chokidar-ts@npm:^3.3.5":
  version: 3.3.5
  resolution: "@poppinss/chokidar-ts@npm:3.3.5"
  dependencies:
    builtin-modules: "npm:^3.2.0"
    chokidar: "npm:^3.5.3"
    debug: "npm:^4.3.4"
    emittery: "npm:^0.10.2"
    fs-extra: "npm:^10.0.1"
    mem: "npm:^8.1.1"
    picomatch: "npm:^2.3.1"
  checksum: 10c0/1c982198ed901faf24c32defed02922886fcd5d8fbe9321a87c1d81d8942fac070f4e65220971a58f7135c1a507cf9f88d96639cec444181e3df1cd82fc9544c
  languageName: node
  linkType: hard

"@poppinss/cliui@npm:^3.0.2, @poppinss/cliui@npm:^3.0.5":
  version: 3.0.5
  resolution: "@poppinss/cliui@npm:3.0.5"
  dependencies:
    "@poppinss/colors": "npm:^3.0.3"
    cli-boxes: "npm:^3.0.0"
    cli-table3: "npm:^0.6.3"
    color-support: "npm:^1.1.3"
    log-update: "npm:^4.0.0"
    pretty-hrtime: "npm:^1.0.3"
    string-width: "npm:^4.2.2"
  checksum: 10c0/355e1cb1faff26aab46108d1af7802f19994da55cb750904afb3c9430b8ae570a23bd7d973e1ede0cf9c203210e2f3ac5067c17356f9f90f5baa43cc5e97a17a
  languageName: node
  linkType: hard

"@poppinss/co-body@npm:^1.1.3":
  version: 1.2.0
  resolution: "@poppinss/co-body@npm:1.2.0"
  dependencies:
    "@poppinss/utils": "npm:^5.0.0"
    inflation: "npm:^2.0.0"
    qs: "npm:^6.11.0"
    raw-body: "npm:^2.5.1"
    type-is: "npm:^1.6.18"
  checksum: 10c0/2c33f989680d424734529ca02a5093e80fb8ed64e27b4ff2e302b9a27dc534f82e4b84a786e8a71390e820cca2328030db87eeeda11e74553a282531de034ba4
  languageName: node
  linkType: hard

"@poppinss/colors@npm:^3.0.2, @poppinss/colors@npm:^3.0.3":
  version: 3.0.3
  resolution: "@poppinss/colors@npm:3.0.3"
  dependencies:
    color-support: "npm:^1.1.3"
    kleur: "npm:^4.1.5"
  checksum: 10c0/58b5fdf1b607d97351190ad2ef46b24bb644138d739a3a17edfc01ef9d8fec48a4948072cbec2ef2836962d3f5ba5d7d9ae2a6872dc01b04217c923917f2144a
  languageName: node
  linkType: hard

"@poppinss/file-generator@npm:^1.0.2":
  version: 1.0.2
  resolution: "@poppinss/file-generator@npm:1.0.2"
  dependencies:
    bytes: "npm:^3.1.2"
  checksum: 10c0/87c1593bb3daad9bd2e9988b0e81f3561052e6d536b51d5bf41c7f9eb12dc85dc3471cea18d4fc8628ebe5fff4213a814f9806f8df6793395032cdcae5cb2095
  languageName: node
  linkType: hard

"@poppinss/hooks@npm:^5.0.3":
  version: 5.0.3
  resolution: "@poppinss/hooks@npm:5.0.3"
  peerDependencies:
    "@adonisjs/application": ">=4.0.0"
  peerDependenciesMeta:
    "@adonisjs/application":
      optional: true
  checksum: 10c0/70f4c62d4543f5405b86c8e5c13a7021fcfeac1e586ac4239947ca0df760e7852a2b6472d46a2224d5ff4da2c5aaaa99fb182d4a78128f9f52411ed7a64d2b07
  languageName: node
  linkType: hard

"@poppinss/hooks@npm:^6.0.2-0":
  version: 6.0.2-0
  resolution: "@poppinss/hooks@npm:6.0.2-0"
  checksum: 10c0/e5d8b6cbc9e8788b71676d8b6df3c85704b0ba53693e4fa0de3626a4378c244ff921df04c39f9d9912f4783ef5e4ac7901722927f152aa4781682d6dc1d1c033
  languageName: node
  linkType: hard

"@poppinss/manager@npm:^5.0.2":
  version: 5.0.2
  resolution: "@poppinss/manager@npm:5.0.2"
  checksum: 10c0/ce180883c3b35136b32d126f767ab22b8ddd9d11f3e645943a7c759f2797b7cbeb2257ce426b6a7092d9b686bdde10505205f776ceac418d472e696cba57a6ce
  languageName: node
  linkType: hard

"@poppinss/matchit@npm:^3.1.2":
  version: 3.1.2
  resolution: "@poppinss/matchit@npm:3.1.2"
  dependencies:
    "@arr/every": "npm:^1.0.0"
  checksum: 10c0/051d35d3846f9ae6457bcf2afeedde499f188da63e380a6c01497a2358dd8eff1730eacbc8ec86cb6299d60b3fbeef9a26fdeac97aab6e984537367c27126582
  languageName: node
  linkType: hard

"@poppinss/multiparty@npm:^2.0.1":
  version: 2.0.1
  resolution: "@poppinss/multiparty@npm:2.0.1"
  dependencies:
    http-errors: "npm:^2.0.0"
    safe-buffer: "npm:5.2.1"
    uid-safe: "npm:2.1.5"
  checksum: 10c0/fe78171a7d368834c91757bba77b835335c64193df27c121df65a10a1629f8908d94384fa0933190107bd32adb1faa764477abee170b8105747dda0de43bd012
  languageName: node
  linkType: hard

"@poppinss/prompts@npm:^2.0.2":
  version: 2.0.2
  resolution: "@poppinss/prompts@npm:2.0.2"
  dependencies:
    "@poppinss/colors": "npm:^3.0.2"
    enquirer: "npm:^2.3.6"
  checksum: 10c0/f408d24ec8fd9813b710b65f3cc9ff5981c23213b29fa5d6c11782a3baa1b87baa75e49d62f34dda2f53bff3664f47ad110d9ff832e6f3229316091d7526e62c
  languageName: node
  linkType: hard

"@poppinss/utils@npm:^4.0.2, @poppinss/utils@npm:^4.0.3, @poppinss/utils@npm:^4.0.4":
  version: 4.0.4
  resolution: "@poppinss/utils@npm:4.0.4"
  dependencies:
    "@poppinss/file-generator": "npm:^1.0.2"
    "@types/bytes": "npm:^3.1.1"
    "@types/he": "npm:^1.1.2"
    bytes: "npm:^3.1.2"
    change-case: "npm:^4.1.2"
    cuid: "npm:^2.1.8"
    flattie: "npm:^1.1.0"
    fs-readdir-recursive: "npm:^1.1.0"
    he: "npm:^1.2.0"
    kind-of: "npm:^6.0.3"
    lodash: "npm:^4.17.21"
    ms: "npm:^2.1.3"
    pluralize: "npm:^8.0.0"
    require-all: "npm:^3.0.0"
    resolve-from: "npm:^5.0.0"
    slugify: "npm:^1.6.5"
    truncatise: "npm:0.0.8"
  checksum: 10c0/88914b9ee46c1cbc02396ddb46e871d9f86257f517ec0bfcaad25cca2e24faa901d4d2487d072d77a059fc944d7dfb51e35703ee4f1021d20a435d9c962c2953
  languageName: node
  linkType: hard

"@poppinss/utils@npm:^5.0.0":
  version: 5.0.0
  resolution: "@poppinss/utils@npm:5.0.0"
  dependencies:
    "@poppinss/file-generator": "npm:^1.0.2"
    "@types/bytes": "npm:^3.1.1"
    "@types/he": "npm:^1.1.2"
    bytes: "npm:^3.1.2"
    change-case: "npm:^4.1.2"
    cuid: "npm:^2.1.8"
    flattie: "npm:^1.1.0"
    fs-readdir-recursive: "npm:^1.1.0"
    he: "npm:^1.2.0"
    kind-of: "npm:^6.0.3"
    lodash: "npm:^4.17.21"
    ms: "npm:^2.1.3"
    pluralize: "npm:^8.0.0"
    require-all: "npm:^3.0.0"
    resolve-from: "npm:^5.0.0"
    slugify: "npm:^1.6.5"
    truncatise: "npm:0.0.8"
  checksum: 10c0/bd4a8f6688409a346bc68344140c17d1029f28775d6a1f7a983e27bd4b6728aca5ddf25eb229801856d59e2bb77e2d6a4825d0a12eab926fee2f1d8bd616ff11
  languageName: node
  linkType: hard

"@rocketseat/adonis-bull@npm:^1.0.4":
  version: 1.0.4
  resolution: "@rocketseat/adonis-bull@npm:1.0.4"
  dependencies:
    bull-board: "npm:^2.0.3"
    bullmq: "npm:^1.24.5"
  peerDependencies:
    "@adonisjs/core": ^5.0.4-preview-rc
  checksum: 10c0/33fb2bfd6ac8b282c65ed9c12d1ddc11986bf02233e5c2d063a908c198138459bc2dc568f4c825395511e20c345dd37db48759bb8ee288d3d75374d8679494a4
  languageName: node
  linkType: hard

"@sinclair/typebox@npm:^0.27.8":
  version: 0.27.8
  resolution: "@sinclair/typebox@npm:0.27.8"
  checksum: 10c0/ef6351ae073c45c2ac89494dbb3e1f87cc60a93ce4cde797b782812b6f97da0d620ae81973f104b43c9b7eaa789ad20ba4f6a1359f1cc62f63729a55a7d22d4e
  languageName: node
  linkType: hard

"@tokenizer/token@npm:^0.3.0":
  version: 0.3.0
  resolution: "@tokenizer/token@npm:0.3.0"
  checksum: 10c0/7ab9a822d4b5ff3f5bca7f7d14d46bdd8432528e028db4a52be7fbf90c7f495cc1af1324691dda2813c6af8dc4b8eb29de3107d4508165f9aa5b53e7d501f155
  languageName: node
  linkType: hard

"@tootallnate/once@npm:2":
  version: 2.0.0
  resolution: "@tootallnate/once@npm:2.0.0"
  checksum: 10c0/073bfa548026b1ebaf1659eb8961e526be22fa77139b10d60e712f46d2f0f05f4e6c8bec62a087d41088ee9e29faa7f54838568e475ab2f776171003c3920858
  languageName: node
  linkType: hard

"@types/body-parser@npm:*":
  version: 1.19.2
  resolution: "@types/body-parser@npm:1.19.2"
  dependencies:
    "@types/connect": "npm:*"
    "@types/node": "npm:*"
  checksum: 10c0/c2dd533e1d4af958d656bdba7f376df68437d8dfb7e4522c88b6f3e6f827549e4be5bf0be68a5f1878accf5752ea37fba7e8a4b6dda53d0d122d77e27b69c750
  languageName: node
  linkType: hard

"@types/bytes@npm:^3.1.1":
  version: 3.1.1
  resolution: "@types/bytes@npm:3.1.1"
  checksum: 10c0/187eb93148daf71aab6c7e1fa451e1c0072028bea631d39fc8255ad3b2a36b460f4675fa082f026b80afa176bf2dd9eef701b7f63ddc9c47433f5b6a20e94697
  languageName: node
  linkType: hard

"@types/chai@npm:^4.3.4":
  version: 4.3.5
  resolution: "@types/chai@npm:4.3.5"
  checksum: 10c0/816b3081e067c6e332be313e2e9a518b117c9eac51201cac749de0d6fcf7cb3238d0def37690b6539b3a928bd87b2f5f777914248447889ebc6f630a0d00e0e5
  languageName: node
  linkType: hard

"@types/connect@npm:*":
  version: 3.4.35
  resolution: "@types/connect@npm:3.4.35"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/f11a1ccfed540723dddd7cb496543ad40a2f663f22ff825e9b220f0bae86db8b1ced2184ee41d3fb358b019ad6519e39481b06386db91ebb859003ad1d54fe6a
  languageName: node
  linkType: hard

"@types/cookiejar@npm:*":
  version: 2.1.2
  resolution: "@types/cookiejar@npm:2.1.2"
  checksum: 10c0/f663f2476ad0aed8ccab03056bbc18b62ed059642077eaec7ab497f56c78149558bfbc0f34345a85872e019352dc28f3c12872af971dc455da3c598ff3966cda
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:^4.17.20, @types/express-serve-static-core@npm:^4.17.33":
  version: 4.17.35
  resolution: "@types/express-serve-static-core@npm:4.17.35"
  dependencies:
    "@types/node": "npm:*"
    "@types/qs": "npm:*"
    "@types/range-parser": "npm:*"
    "@types/send": "npm:*"
  checksum: 10c0/08db6ffff07b5d53d852bb0a078ea5ee6dc3eb581d8c8fdf0d65f48c641db2830658074c797844e618b0933ce4ca2ddd08191f9d79b12eb2ec3d66f8551716ec
  languageName: node
  linkType: hard

"@types/express@npm:^4.17.12":
  version: 4.17.17
  resolution: "@types/express@npm:4.17.17"
  dependencies:
    "@types/body-parser": "npm:*"
    "@types/express-serve-static-core": "npm:^4.17.33"
    "@types/qs": "npm:*"
    "@types/serve-static": "npm:*"
  checksum: 10c0/5802a0a28f7473744dd6a118479440d8c5c801c973d34fb6f31b5ee645a41fee936193978a8e905d55deefda9b675d19924167bf11a31339874c3161a3fc2922
  languageName: node
  linkType: hard

"@types/fs-extra@npm:^9.0.13":
  version: 9.0.13
  resolution: "@types/fs-extra@npm:9.0.13"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/576d4e9d382393316ed815c593f7f5c157408ec5e184521d077fcb15d514b5a985245f153ef52142b9b976cb9bd8f801850d51238153ebd0dc9e96b7a7548588
  languageName: node
  linkType: hard

"@types/glob@npm:^7.1.1":
  version: 7.2.0
  resolution: "@types/glob@npm:7.2.0"
  dependencies:
    "@types/minimatch": "npm:*"
    "@types/node": "npm:*"
  checksum: 10c0/a8eb5d5cb5c48fc58c7ca3ff1e1ddf771ee07ca5043da6e4871e6757b4472e2e73b4cfef2644c38983174a4bc728c73f8da02845c28a1212f98cabd293ecae98
  languageName: node
  linkType: hard

"@types/he@npm:^1.1.2":
  version: 1.2.0
  resolution: "@types/he@npm:1.2.0"
  checksum: 10c0/3ead3ea6705cafa9b34e35ca2ff45564d9f11d2072c99db42447f6cec397ea9326fbd831fc8a71051c7f7596b3ee4fdc6e244a4d23a0d7e61040766ecb201152
  languageName: node
  linkType: hard

"@types/http-errors@npm:*":
  version: 2.0.1
  resolution: "@types/http-errors@npm:2.0.1"
  checksum: 10c0/3bbc8c84fb02b381737e2eec563b434121384b1aef4e070edec4479a1bc74f27373edc09162680cd3ea1035ef8e5ab6d606bd7c99e3855c424045fb74376cb66
  languageName: node
  linkType: hard

"@types/istanbul-lib-coverage@npm:*, @types/istanbul-lib-coverage@npm:^2.0.0":
  version: 2.0.4
  resolution: "@types/istanbul-lib-coverage@npm:2.0.4"
  checksum: 10c0/af5f6b64e788331ed3f7b2e2613cb6ca659c58b8500be94bbda8c995ad3da9216c006f1cfe6f66b321c39392b1bda18b16e63cef090a77d24a00b4bd5ba3b018
  languageName: node
  linkType: hard

"@types/istanbul-lib-report@npm:*":
  version: 3.0.0
  resolution: "@types/istanbul-lib-report@npm:3.0.0"
  dependencies:
    "@types/istanbul-lib-coverage": "npm:*"
  checksum: 10c0/7ced458631276a28082ee40645224c3cdd8b861961039ff811d841069171c987ec7e50bc221845ec0d04df0022b2f457a21fb2f816dab2fbe64d59377b32031f
  languageName: node
  linkType: hard

"@types/istanbul-reports@npm:^1.1.1":
  version: 1.1.2
  resolution: "@types/istanbul-reports@npm:1.1.2"
  dependencies:
    "@types/istanbul-lib-coverage": "npm:*"
    "@types/istanbul-lib-report": "npm:*"
  checksum: 10c0/80b76715f4ac74a4ddfc82d7942b2faaefbe9fdce8e7dfdfa497b3fb60a3e707b632c6e70e1565cfe30045eaebaf7aad0d6c3d102652d1da8fdb0bf095924eb3
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.6, @types/json-schema@npm:^7.0.9":
  version: 7.0.12
  resolution: "@types/json-schema@npm:7.0.12"
  checksum: 10c0/2c39946ae321fe42d085c61a85872a81bbee70f9b2054ad344e8811dfc478fdbaf1ebf5f2989bb87c895ba2dfc3b1dcba85db11e467bbcdc023708814207791c
  languageName: node
  linkType: hard

"@types/luxon@npm:^3.0.1":
  version: 3.3.1
  resolution: "@types/luxon@npm:3.3.1"
  checksum: 10c0/eac87a3d8ee7a1ccf8e0261d5edefce03716a9ca9b9a35dbd064e6682398b277571775b88229b7475d8bacf48fc8b42e6376026b5a2a4cb51f6fe6423f1bd7c9
  languageName: node
  linkType: hard

"@types/mime@npm:*":
  version: 3.0.1
  resolution: "@types/mime@npm:3.0.1"
  checksum: 10c0/c4c0fc89042822a3b5ffd6ef0da7006513454ee8376ffa492372d17d2925a4e4b1b194c977b718c711df38b33eb9d06deb5dbf9f851bcfb7e5e65f06b2a87f97
  languageName: node
  linkType: hard

"@types/mime@npm:^1":
  version: 1.3.2
  resolution: "@types/mime@npm:1.3.2"
  checksum: 10c0/61d144e5170c6cdf6de334ec0ee4bb499b1a0fb0233834a9e8cec6d289b0e3042bedf35cbc1c995d71a247635770dae3f13a9ddae69098bb54b933429bc08d35
  languageName: node
  linkType: hard

"@types/minimatch@npm:*":
  version: 5.1.2
  resolution: "@types/minimatch@npm:5.1.2"
  checksum: 10c0/83cf1c11748891b714e129de0585af4c55dd4c2cafb1f1d5233d79246e5e1e19d1b5ad9e8db449667b3ffa2b6c80125c429dbee1054e9efb45758dbc4e118562
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 20.4.4
  resolution: "@types/node@npm:20.4.4"
  checksum: 10c0/93748f53c7f34acde229436717408a0b8f88540647a4bacb863ee46a1545d53a056fa9b486721067b5a7bf0d5123fb1c13519e977b34aef1ddef9857a1c1e5fc
  languageName: node
  linkType: hard

"@types/pino-pretty@npm:*":
  version: 5.0.0
  resolution: "@types/pino-pretty@npm:5.0.0"
  dependencies:
    pino-pretty: "npm:*"
  checksum: 10c0/c37ad948adf9cc3665fb150707e44f5230f15e2d691d9d1c3f876ec59cbd637313b797c1580d538e1292b5c385f0e59a73127c9ac5ba953ec2081bde43c2b0f5
  languageName: node
  linkType: hard

"@types/pino-std-serializers@npm:*":
  version: 4.0.0
  resolution: "@types/pino-std-serializers@npm:4.0.0"
  dependencies:
    pino-std-serializers: "npm:*"
  checksum: 10c0/729c6be8d84973474aa21533892a6bb9cc18c17c2f2e43ff31ec416dd25cfe583eff4c4ca80fe50898359da23b2fbff4caacadacc6e3b15fb4aa2a3fbf77deac
  languageName: node
  linkType: hard

"@types/pino@npm:^6.3.12":
  version: 6.3.12
  resolution: "@types/pino@npm:6.3.12"
  dependencies:
    "@types/node": "npm:*"
    "@types/pino-pretty": "npm:*"
    "@types/pino-std-serializers": "npm:*"
    sonic-boom: "npm:^2.1.0"
  checksum: 10c0/906a2a30f9f49eda0c84548e3376c1566339855ff25357e77bc357b68d43d8bc1500b95a0028d420a6cfc36efa0229401a6b94552b22398e457dd1c3627d6ccf
  languageName: node
  linkType: hard

"@types/proxy-addr@npm:^2.0.0":
  version: 2.0.0
  resolution: "@types/proxy-addr@npm:2.0.0"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/09f868bbb3f7af0ad58b0949e30635a585064be890c78a8b19e769c6a2741698620bec2c09a71b7099abb8bb438be38966f87d137bb09d0f292ae8373115f0d0
  languageName: node
  linkType: hard

"@types/qs@npm:*":
  version: 6.9.7
  resolution: "@types/qs@npm:6.9.7"
  checksum: 10c0/157eb05f4c75790b0ebdcf7b0547ff117feabc8cda03c3cac3d3ea82bb19a1912e76a411df3eb0bdd01026a9770f07bc0e7e3fbe39ebb31c1be4564c16be35f1
  languageName: node
  linkType: hard

"@types/range-parser@npm:*":
  version: 1.2.4
  resolution: "@types/range-parser@npm:1.2.4"
  checksum: 10c0/8e3c3cda88675efd9145241bcb454449715b7d015a7fb80d018dcb3d441fa1938b302242cc0dfa6b02c5d014dd8bc082ae90091e62b1e816cae3ec36c2a7dbcb
  languageName: node
  linkType: hard

"@types/semver@npm:^7.3.12":
  version: 7.5.0
  resolution: "@types/semver@npm:7.5.0"
  checksum: 10c0/ca4ba4642b5972b6e88e73c5bc02bbaceb8d76bce71748d86e3e95042d4e5a44603113a1dcd2cb9b73ad6f91f6e4ab73185eb41bbfc9c73b11f0ed3db3b7443a
  languageName: node
  linkType: hard

"@types/send@npm:*":
  version: 0.17.1
  resolution: "@types/send@npm:0.17.1"
  dependencies:
    "@types/mime": "npm:^1"
    "@types/node": "npm:*"
  checksum: 10c0/1aad6bfafdaa3a3cadad1b441843dfd166821c0e93513daabe979de85b552a1298cfb6f07d40f80b5ecf14a3194dc148deb138605039841f1dadc7132c73e634
  languageName: node
  linkType: hard

"@types/serve-static@npm:*":
  version: 1.15.2
  resolution: "@types/serve-static@npm:1.15.2"
  dependencies:
    "@types/http-errors": "npm:*"
    "@types/mime": "npm:*"
    "@types/node": "npm:*"
  checksum: 10c0/5e7b3e17b376f8910d5c9a0b1def38d7841c8939713940098f1b80a330d5caa9cfe9b632c122252cd70165052439e18fafa46635dc55b1d6058343901eec22eb
  languageName: node
  linkType: hard

"@types/source-map-support@npm:^0.5.6":
  version: 0.5.6
  resolution: "@types/source-map-support@npm:0.5.6"
  dependencies:
    source-map: "npm:^0.6.0"
  checksum: 10c0/6ab742829d3828663c244e2364c01be7cf8a13b644b3c52e9b14d8328d0eb858cf8c45d7353ed4f5a5a71d0d16d5fdc4a49adc0a7102622f26bfb3f3ca0f8165
  languageName: node
  linkType: hard

"@types/superagent@npm:^4.1.16":
  version: 4.1.18
  resolution: "@types/superagent@npm:4.1.18"
  dependencies:
    "@types/cookiejar": "npm:*"
    "@types/node": "npm:*"
  checksum: 10c0/0ac764df988559e61d5557608c13d3dd00d0d1990e5a8aa390334328b2c61dc6c407ef796bed83d4dd3829c15088f581d4efaa664f21e78849b58cc2713534d9
  languageName: node
  linkType: hard

"@types/validator@npm:^13.7.10":
  version: 13.7.17
  resolution: "@types/validator@npm:13.7.17"
  checksum: 10c0/46ec9b8bc11d91a575aead891a9c1d2f23bc28369449cb62415d663262d1d0e96debe75ceac626102a17a0a7a899330626ec36c8d4e84d337c705daed314fed5
  languageName: node
  linkType: hard

"@types/yargs-parser@npm:*":
  version: 21.0.0
  resolution: "@types/yargs-parser@npm:21.0.0"
  checksum: 10c0/cb89f3bb2e8002f1479a65a934e825be4cc18c50b350bbc656405d41cf90b8a299b105e7da497d7eb1aa460472a07d1e5a389f3af0862f1d1252279cfcdd017c
  languageName: node
  linkType: hard

"@types/yargs@npm:^15.0.0":
  version: 15.0.15
  resolution: "@types/yargs@npm:15.0.15"
  dependencies:
    "@types/yargs-parser": "npm:*"
  checksum: 10c0/b52519ba68a8d90996b54143ff74fcd8ac1722a1ef4a50ed8c3dbc1f7a76d14210f0262f8b91eabcdab202ff4babdd92ce7332ab1cdd6af4eae7c9fc81c83797
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^5.37.0":
  version: 5.62.0
  resolution: "@typescript-eslint/eslint-plugin@npm:5.62.0"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.4.0"
    "@typescript-eslint/scope-manager": "npm:5.62.0"
    "@typescript-eslint/type-utils": "npm:5.62.0"
    "@typescript-eslint/utils": "npm:5.62.0"
    debug: "npm:^4.3.4"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^5.2.0"
    natural-compare-lite: "npm:^1.4.0"
    semver: "npm:^7.3.7"
    tsutils: "npm:^3.21.0"
  peerDependencies:
    "@typescript-eslint/parser": ^5.0.0
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/3f40cb6bab5a2833c3544e4621b9fdacd8ea53420cadc1c63fac3b89cdf5c62be1e6b7bcf56976dede5db4c43830de298ced3db60b5494a3b961ca1b4bff9f2a
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^5.37.0":
  version: 5.62.0
  resolution: "@typescript-eslint/parser@npm:5.62.0"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:5.62.0"
    "@typescript-eslint/types": "npm:5.62.0"
    "@typescript-eslint/typescript-estree": "npm:5.62.0"
    debug: "npm:^4.3.4"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/315194b3bf39beb9bd16c190956c46beec64b8371e18d6bb72002108b250983eb1e186a01d34b77eb4045f4941acbb243b16155fbb46881105f65e37dc9e24d4
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/scope-manager@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": "npm:5.62.0"
    "@typescript-eslint/visitor-keys": "npm:5.62.0"
  checksum: 10c0/861253235576c1c5c1772d23cdce1418c2da2618a479a7de4f6114a12a7ca853011a1e530525d0931c355a8fd237b9cd828fac560f85f9623e24054fd024726f
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/type-utils@npm:5.62.0"
  dependencies:
    "@typescript-eslint/typescript-estree": "npm:5.62.0"
    "@typescript-eslint/utils": "npm:5.62.0"
    debug: "npm:^4.3.4"
    tsutils: "npm:^3.21.0"
  peerDependencies:
    eslint: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/93112e34026069a48f0484b98caca1c89d9707842afe14e08e7390af51cdde87378df29d213d3bbd10a7cfe6f91b228031b56218515ce077bdb62ddea9d9f474
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/types@npm:5.62.0"
  checksum: 10c0/7febd3a7f0701c0b927e094f02e82d8ee2cada2b186fcb938bc2b94ff6fbad88237afc304cbaf33e82797078bbbb1baf91475f6400912f8b64c89be79bfa4ddf
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/typescript-estree@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": "npm:5.62.0"
    "@typescript-eslint/visitor-keys": "npm:5.62.0"
    debug: "npm:^4.3.4"
    globby: "npm:^11.1.0"
    is-glob: "npm:^4.0.3"
    semver: "npm:^7.3.7"
    tsutils: "npm:^3.21.0"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10c0/d7984a3e9d56897b2481940ec803cb8e7ead03df8d9cfd9797350be82ff765dfcf3cfec04e7355e1779e948da8f02bc5e11719d07a596eb1cb995c48a95e38cf
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/utils@npm:5.62.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@types/json-schema": "npm:^7.0.9"
    "@types/semver": "npm:^7.3.12"
    "@typescript-eslint/scope-manager": "npm:5.62.0"
    "@typescript-eslint/types": "npm:5.62.0"
    "@typescript-eslint/typescript-estree": "npm:5.62.0"
    eslint-scope: "npm:^5.1.1"
    semver: "npm:^7.3.7"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10c0/f09b7d9952e4a205eb1ced31d7684dd55cee40bf8c2d78e923aa8a255318d97279825733902742c09d8690f37a50243f4c4d383ab16bd7aefaf9c4b438f785e1
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/visitor-keys@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": "npm:5.62.0"
    eslint-visitor-keys: "npm:^3.3.0"
  checksum: 10c0/7c3b8e4148e9b94d9b7162a596a1260d7a3efc4e65199693b8025c71c4652b8042501c0bc9f57654c1e2943c26da98c0f77884a746c6ae81389fcb0b513d995d
  languageName: node
  linkType: hard

"@youngkiu/pino-slack-webhook@npm:^0.1.2":
  version: 0.1.2
  resolution: "@youngkiu/pino-slack-webhook@npm:0.1.2"
  dependencies:
    axios: "npm:^1.4.0"
    pino-abstract-transport: "npm:^1.0.0"
  checksum: 10c0/c8ab31cd1f4d34f4db1f060aa4ac3981959b29bb46e8b6475763f9d4960456afbbfd86172fba86068354ea30b21de4c71bbd379e6c937bd753d4aa6a5ea9835f
  languageName: node
  linkType: hard

"abbrev@npm:^1.0.0":
  version: 1.1.1
  resolution: "abbrev@npm:1.1.1"
  checksum: 10c0/3f762677702acb24f65e813070e306c61fafe25d4b2583f9dfc935131f774863f3addd5741572ed576bd69cabe473c5af18e1e108b829cb7b6b4747884f726e6
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: "npm:^5.0.0"
  checksum: 10c0/90ccc50f010250152509a344eb2e71977fbf8db0ab8f1061197e3275ddf6c61a41a6edfd7b9409c664513131dd96e962065415325ef23efa5db931b382d24ca5
  languageName: node
  linkType: hard

"abstract-logging@npm:^2.0.1":
  version: 2.0.1
  resolution: "abstract-logging@npm:2.0.1"
  checksum: 10c0/304879d9babcf6772260e5ddde632e6428e1f42f7a7a116d4689e97ad813a20e0ec2dd1e0a122f3617557f40091b9ca85735de4b48c17a2041268cb47b3f8ef1
  languageName: node
  linkType: hard

"accepts@npm:^1.3.8, accepts@npm:~1.3.7":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: "npm:~2.1.34"
    negotiator: "npm:0.6.3"
  checksum: 10c0/3a35c5f5586cfb9a21163ca47a5f77ac34fa8ceb5d17d2fa2c0d81f41cbd7f8c6fa52c77e2c039acc0f4d09e71abdc51144246900f6bef5e3c4b333f77d89362
  languageName: node
  linkType: hard

"acorn-class-fields@npm:^1.0.0":
  version: 1.0.0
  resolution: "acorn-class-fields@npm:1.0.0"
  dependencies:
    acorn-private-class-elements: "npm:^1.0.0"
  peerDependencies:
    acorn: ^6 || ^7 || ^8
  checksum: 10c0/061dc73b95801f844181cd81dfbb306856fc6e9b6d1f2230ade46ae5f84f303aba45682739c641579a474f702570f1d9a5ccaa09d97ebf24a53d55f6b7d32f11
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10c0/4c54868fbef3b8d58927d5e33f0a4de35f59012fe7b12cf9dfbb345fb8f46607709e1c4431be869a23fb63c151033d84c4198fa9f79385cec34fcb1dd53974c1
  languageName: node
  linkType: hard

"acorn-private-class-elements@npm:^1.0.0":
  version: 1.0.0
  resolution: "acorn-private-class-elements@npm:1.0.0"
  peerDependencies:
    acorn: ^6.1.0 || ^7 || ^8
  checksum: 10c0/26632c7d65957a1f3ee00aaec600901b9c69f0de132f4131f876ca230e38cca002dbf2c2bf83563445f1a5d187c4fd25ed9d35b11297e4834e81c8b5d49e36c6
  languageName: node
  linkType: hard

"acorn-private-methods@npm:^1.0.0":
  version: 1.0.0
  resolution: "acorn-private-methods@npm:1.0.0"
  dependencies:
    acorn-private-class-elements: "npm:^1.0.0"
  peerDependencies:
    acorn: ^6 || ^7 || ^8
  checksum: 10c0/f10625959532558613bedade9e0ead635e9eaab092d9fa97770b52e4c06e765a983c348cbb20b6d0c85294e7fcb8995634ee527085d50178f67d2ee95711018f
  languageName: node
  linkType: hard

"acorn-static-class-features@npm:^1.0.0":
  version: 1.0.0
  resolution: "acorn-static-class-features@npm:1.0.0"
  dependencies:
    acorn-private-class-elements: "npm:^1.0.0"
  peerDependencies:
    acorn: ^6.1.0 || ^7 || ^8
  checksum: 10c0/633e0ef550708df9f78b677c47c0f56cc61b354e8a7435f0d8261d43f6ac1dcef5be9dcbe15411146b0654ed52bf2def2748142b6ae1fd155c0fbe1c92191aea
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.0.2":
  version: 8.2.0
  resolution: "acorn-walk@npm:8.2.0"
  checksum: 10c0/dbe92f5b2452c93e960c5594e666dd1fae141b965ff2cb4a1e1d0381e3e4db4274c5ce4ffa3d681a86ca2a8d4e29d5efc0670a08e23fd2800051ea387df56ca2
  languageName: node
  linkType: hard

"acorn@npm:^8.0.5, acorn@npm:^8.9.0":
  version: 8.10.0
  resolution: "acorn@npm:8.10.0"
  bin:
    acorn: bin/acorn
  checksum: 10c0/deaeebfbea6e40f6c0e1070e9b0e16e76ba484de54cbd735914d1d41d19169a450de8630b7a3a0c4e271a3b0c0b075a3427ad1a40d8a69f8747c0e8cb02ee3e2
  languageName: node
  linkType: hard

"adonis-preset-ts@npm:^2.1.0":
  version: 2.1.0
  resolution: "adonis-preset-ts@npm:2.1.0"
  checksum: 10c0/8724ba2f23295b45af7d856ef8672fa468ca64ad3026a770d4451c0a363ad5515ad92b85ff80291852d36f53b2cfdf915ec79d54564ab5af80e8cc23769f80bb
  languageName: node
  linkType: hard

"after@npm:0.8.2":
  version: 0.8.2
  resolution: "after@npm:0.8.2"
  checksum: 10c0/b7ac8c7936eadfcb7cd6714150f04977745f3555db0d2183fd219d1f69a0d2dba95c1db16f372b45aef6670bda74c674a49a74100310080676b7ec308842a9c3
  languageName: node
  linkType: hard

"agent-base@npm:6, agent-base@npm:^6.0.2":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: "npm:4"
  checksum: 10c0/dc4f757e40b5f3e3d674bc9beb4f1048f4ee83af189bae39be99f57bf1f48dde166a8b0a5342a84b5944ee8e6ed1e5a9d801858f4ad44764e84957122fe46261
  languageName: node
  linkType: hard

"agentkeepalive@npm:^4.2.1":
  version: 4.3.0
  resolution: "agentkeepalive@npm:4.3.0"
  dependencies:
    debug: "npm:^4.1.0"
    depd: "npm:^2.0.0"
    humanize-ms: "npm:^1.2.1"
  checksum: 10c0/61cbdab12d45e82e9ae515b0aa8d09617b66f72409e541a646dd7be4b7260d335d7f56a38079ad305bf0ffb8405592a459faf1294111289107f48352a20c2799
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: "npm:^2.0.0"
    indent-string: "npm:^4.0.0"
  checksum: 10c0/a42f67faa79e3e6687a4923050e7c9807db3848a037076f791d10e092677d65c1d2d863b7848560699f40fc0502c19f40963fb1cd1fb3d338a7423df8e45e039
  languageName: node
  linkType: hard

"ajv@npm:^6.10.0, ajv@npm:^6.12.4, ajv@npm:^6.12.6, ajv@npm:^6.5.2":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10c0/41e23642cbe545889245b9d2a45854ebba51cda6c778ebced9649420d9205f2efb39cb43dbc41e358409223b1ea43303ae4839db682c848b891e4811da1a5a71
  languageName: node
  linkType: hard

"ansi-colors@npm:^4.1.1":
  version: 4.1.3
  resolution: "ansi-colors@npm:4.1.3"
  checksum: 10c0/ec87a2f59902f74e61eada7f6e6fe20094a628dab765cfdbd03c3477599368768cffccdb5d3bb19a1b6c99126783a143b1fee31aab729b31ffe5836c7e5e28b9
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.3.0":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: "npm:^0.21.3"
  checksum: 10c0/da917be01871525a3dfcf925ae2977bc59e8c513d4423368645634bf5d4ceba5401574eb705c1e92b79f7292af5a656f78c5725a4b0e1cec97c4b413705c1d50
  languageName: node
  linkType: hard

"ansi-escapes@npm:^6.2.0":
  version: 6.2.0
  resolution: "ansi-escapes@npm:6.2.0"
  dependencies:
    type-fest: "npm:^3.0.0"
  checksum: 10c0/3eec75deedd8b10192c5f98e4cd9715cc3ff268d33fc463c24b7d22446668bfcd4ad1803993ea89c0f51f88b5a3399572bacb7c8cb1a067fc86e189c5f3b0c7e
  languageName: node
  linkType: hard

"ansi-regex@npm:^2.0.0":
  version: 2.1.1
  resolution: "ansi-regex@npm:2.1.1"
  checksum: 10c0/78cebaf50bce2cb96341a7230adf28d804611da3ce6bf338efa7b72f06cc6ff648e29f80cd95e582617ba58d5fdbec38abfeed3500a98bce8381a9daec7c548b
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.0, ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.0.1
  resolution: "ansi-regex@npm:6.0.1"
  checksum: 10c0/cbe16dbd2c6b2735d1df7976a7070dd277326434f0212f43abf6d87674095d247968209babdaad31bb00882fa68807256ba9be340eec2f1004de14ca75f52a08
  languageName: node
  linkType: hard

"ansi-styles@npm:^2.2.1":
  version: 2.2.1
  resolution: "ansi-styles@npm:2.2.1"
  checksum: 10c0/7c68aed4f1857389e7a12f85537ea5b40d832656babbf511cc7ecd9efc52889b9c3e5653a71a6aade783c3c5e0aa223ad4ff8e83c27ac8a666514e6c79068cab
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^5.0.0":
  version: 5.2.0
  resolution: "ansi-styles@npm:5.2.0"
  checksum: 10c0/9c4ca80eb3c2fb7b33841c210d2f20807f40865d27008d7c3f707b7f95cab7d67462a565e2388ac3285b71cb3d9bb2173de8da37c57692a362885ec34d6e27df
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"ansicolors@npm:~0.3.2":
  version: 0.3.2
  resolution: "ansicolors@npm:0.3.2"
  checksum: 10c0/e202182895e959c5357db6c60791b2abaade99fcc02221da11a581b26a7f83dc084392bc74e4d3875c22f37b3c9ef48842e896e3bfed394ec278194b8003e0ac
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10c0/57b06ae984bc32a0d22592c87384cd88fe4511b1dd7581497831c56d41939c8a001b28e7b853e1450f2bf61992dfcaa8ae2d0d161a0a90c4fb631ef07098fbac
  languageName: node
  linkType: hard

"api-contract-validator@npm:^2.2.8":
  version: 2.2.8
  resolution: "api-contract-validator@npm:2.2.8"
  dependencies:
    api-schema-builder: "npm:^2.0.10"
    chalk: "npm:^3.0.0"
    columnify: "npm:^1.5.4"
    jest-diff: "npm:^25.5.0"
    jest-matcher-utils: "npm:^25.5.0"
    lodash.flatten: "npm:^4.4.0"
    lodash.get: "npm:^4.4.2"
    lodash.set: "npm:^4.3.2"
    uri-js: "npm:^4.4.1"
  checksum: 10c0/c41adab0ea50367a2a3f6b533e57828468d5eee35a6f5520de8ab4a6f23c04ab045be197402a59a978e253120474304b6e879bcc6398eb176c3b4a6fd9d58e67
  languageName: node
  linkType: hard

"api-schema-builder@npm:^2.0.10":
  version: 2.0.11
  resolution: "api-schema-builder@npm:2.0.11"
  dependencies:
    ajv: "npm:^6.12.6"
    clone-deep: "npm:^4.0.1"
    decimal.js: "npm:^10.3.1"
    js-yaml: "npm:^3.14.1"
    json-schema-deref-sync: "npm:^0.14.0"
    lodash.get: "npm:^4.4.2"
    openapi-schema-validator: "npm:^3.0.3"
    swagger-parser: "npm:^10.0.3"
  checksum: 10c0/1e28f15ccc5c1dc8fcce7e810140cf51a4dcc431652951fb6fb26999cddcf4782a1a19a65c62ba9417382880d8b1d01409bb59c559b63ea0eb5390ffaea9d4ac
  languageName: node
  linkType: hard

"aproba@npm:^1.0.3 || ^2.0.0":
  version: 2.0.0
  resolution: "aproba@npm:2.0.0"
  checksum: 10c0/d06e26384a8f6245d8c8896e138c0388824e259a329e0c9f196b4fa533c82502a6fd449586e3604950a0c42921832a458bb3aa0aa9f0ba449cfd4f50fd0d09b5
  languageName: node
  linkType: hard

"are-we-there-yet@npm:^3.0.0":
  version: 3.0.1
  resolution: "are-we-there-yet@npm:3.0.1"
  dependencies:
    delegates: "npm:^1.0.0"
    readable-stream: "npm:^3.6.0"
  checksum: 10c0/8373f289ba42e4b5ec713bb585acdac14b5702c75f2a458dc985b9e4fa5762bc5b46b40a21b72418a3ed0cfb5e35bdc317ef1ae132f3035f633d581dd03168c3
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: "npm:~1.0.2"
  checksum: 10c0/b2972c5c23c63df66bca144dbc65d180efa74f25f8fd9b7d9a0a6c88ae839db32df3d54770dcb6460cf840d232b60695d1a6b1053f599d84e73f7437087712de
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10c0/c5640c2d89045371c7cedd6a70212a04e360fd34d6edeae32f6952c63949e3525ea77dbec0289d8213a99bbaeab5abfa860b5c12cf88a2e6cf8106e90dd27a7e
  languageName: node
  linkType: hard

"arr-diff@npm:^4.0.0":
  version: 4.0.0
  resolution: "arr-diff@npm:4.0.0"
  checksum: 10c0/67b80067137f70c89953b95f5c6279ad379c3ee39f7143578e13bd51580a40066ee2a55da066e22d498dce10f68c2d70056d7823f972fab99dfbf4c78d0bc0f7
  languageName: node
  linkType: hard

"arr-flatten@npm:^1.1.0":
  version: 1.1.0
  resolution: "arr-flatten@npm:1.1.0"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"arr-union@npm:^3.1.0":
  version: 3.1.0
  resolution: "arr-union@npm:3.1.0"
  checksum: 10c0/7d5aa05894e54aa93c77c5726c1dd5d8e8d3afe4f77983c0aa8a14a8a5cbe8b18f0cf4ecaa4ac8c908ef5f744d2cbbdaa83fd6e96724d15fea56cfa7f5efdd51
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-union@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-union@npm:1.0.2"
  dependencies:
    array-uniq: "npm:^1.0.1"
  checksum: 10c0/18686767c0cfdae8dc4acf5ac119b0f0eacad82b7fcc0aa62cc41f93c5ad406d494b6a6e53d85e52e8f0349b67a4fec815feeb537e95c02510d747bc9a4157c7
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 10c0/429897e68110374f39b771ec47a7161fc6a8fc33e196857c0a396dc75df0b5f65e4d046674db764330b6bb66b39ef48dd7c53b6a2ee75cfb0681e0c1a7033962
  languageName: node
  linkType: hard

"array-uniq@npm:^1.0.1":
  version: 1.0.3
  resolution: "array-uniq@npm:1.0.3"
  checksum: 10c0/3acbaf9e6d5faeb1010e2db04ab171b8d265889e46c61762e502979bdc5e55656013726e9a61507de3c82d329a0dc1e8072630a3454b4f2b881cb19ba7fd8aa6
  languageName: node
  linkType: hard

"array-unique@npm:^0.3.2":
  version: 0.3.2
  resolution: "array-unique@npm:0.3.2"
  checksum: 10c0/dbf4462cdba8a4b85577be07705210b3d35be4b765822a3f52962d907186617638ce15e0603a4fefdcf82f4cbbc9d433f8cbbd6855148a68872fa041b6474121
  languageName: node
  linkType: hard

"arraybuffer.slice@npm:0.0.6":
  version: 0.0.6
  resolution: "arraybuffer.slice@npm:0.0.6"
  checksum: 10c0/d4069325ac432bda6cae5a50acd2c4d7515211e814467c04d6bb95747c11d7c2dcd79606a144bd6d72601ff8e4bac619f2da683eefb0a3f988c7a4986433b32f
  languageName: node
  linkType: hard

"arrify@npm:^2.0.1":
  version: 2.0.1
  resolution: "arrify@npm:2.0.1"
  checksum: 10c0/3fb30b5e7c37abea1907a60b28a554d2f0fc088757ca9bf5b684786e583fdf14360721eb12575c1ce6f995282eab936712d3c4389122682eafab0e0b57f78dbb
  languageName: node
  linkType: hard

"as-table@npm:^1.0.36":
  version: 1.0.55
  resolution: "as-table@npm:1.0.55"
  dependencies:
    printable-characters: "npm:^1.0.42"
  checksum: 10c0/8c5693a84621fe53c62fcad6b779dc55c5caf4d43b8e67077964baea4a337769ef53f590d7395c806805b4ef1a391b614ba9acdee19b2ca4309ddedaf13894e6
  languageName: node
  linkType: hard

"asap@npm:^2.0.0":
  version: 2.0.6
  resolution: "asap@npm:2.0.6"
  checksum: 10c0/c6d5e39fe1f15e4b87677460bd66b66050cd14c772269cee6688824c1410a08ab20254bb6784f9afb75af9144a9f9a7692d49547f4d19d715aeb7c0318f3136d
  languageName: node
  linkType: hard

"assertion-error@npm:^1.1.0":
  version: 1.1.0
  resolution: "assertion-error@npm:1.1.0"
  checksum: 10c0/25456b2aa333250f01143968e02e4884a34588a8538fbbf65c91a637f1dbfb8069249133cd2f4e530f10f624d206a664e7df30207830b659e9f5298b00a4099b
  languageName: node
  linkType: hard

"assign-symbols@npm:^1.0.0":
  version: 1.0.0
  resolution: "assign-symbols@npm:1.0.0"
  checksum: 10c0/29a654b8a6da6889a190d0d0efef4b1bfb5948fa06cbc245054aef05139f889f2f7c75b989917e3fde853fc4093b88048e4de8578a73a76f113d41bfd66e5775
  languageName: node
  linkType: hard

"astral-regex@npm:^2.0.0":
  version: 2.0.0
  resolution: "astral-regex@npm:2.0.0"
  checksum: 10c0/f63d439cc383db1b9c5c6080d1e240bd14dae745f15d11ec5da863e182bbeca70df6c8191cffef5deba0b566ef98834610a68be79ac6379c95eeb26e1b310e25
  languageName: node
  linkType: hard

"async-retry@npm:^1.3.3":
  version: 1.3.3
  resolution: "async-retry@npm:1.3.3"
  dependencies:
    retry: "npm:0.13.1"
  checksum: 10c0/cabced4fb46f8737b95cc88dc9c0ff42656c62dc83ce0650864e891b6c155a063af08d62c446269b51256f6fbcb69a6563b80e76d0ea4a5117b0c0377b6b19d8
  languageName: node
  linkType: hard

"async@npm:^3.2.3":
  version: 3.2.4
  resolution: "async@npm:3.2.4"
  checksum: 10c0/b5d02fed64717edf49e35b2b156debd9cf524934ea670108fa5528e7615ed66a5e0bf6c65f832c9483b63aa7f0bffe3e588ebe8d58a539b833798d324516e1c9
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10c0/d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"atob@npm:^2.1.2":
  version: 2.1.2
  resolution: "atob@npm:2.1.2"
  bin:
    atob: bin/atob.js
  checksum: 10c0/ada635b519dc0c576bb0b3ca63a73b50eefacf390abb3f062558342a8d68f2db91d0c8db54ce81b0d89de3b0f000de71f3ae7d761fd7d8cc624278fe443d6c7e
  languageName: node
  linkType: hard

"atomic-sleep@npm:^1.0.0":
  version: 1.0.0
  resolution: "atomic-sleep@npm:1.0.0"
  checksum: 10c0/e329a6665512736a9bbb073e1761b4ec102f7926cce35037753146a9db9c8104f5044c1662e4a863576ce544fb8be27cd2be6bc8c1a40147d03f31eb1cfb6e8a
  languageName: node
  linkType: hard

"axios@npm:^1.4.0":
  version: 1.4.0
  resolution: "axios@npm:1.4.0"
  dependencies:
    follow-redirects: "npm:^1.15.0"
    form-data: "npm:^4.0.0"
    proxy-from-env: "npm:^1.1.0"
  checksum: 10c0/a925a07590b0ec1d4daf28cd27890f930daab980371558deb3b883af174b881da09e5ba2cb8393a648fda5859e39934982d0b8b092fe89fc84cb6c80a70a1910
  languageName: node
  linkType: hard

"babel-code-frame@npm:^6.26.0":
  version: 6.26.0
  resolution: "babel-code-frame@npm:6.26.0"
  dependencies:
    chalk: "npm:^1.1.3"
    esutils: "npm:^2.0.2"
    js-tokens: "npm:^3.0.2"
  checksum: 10c0/7fecc128e87578cf1b96e78d2b25e0b260e202bdbbfcefa2eac23b7f8b7b2f7bc9276a14599cde14403cc798cc2a38e428e2cab50b77658ab49228b09ae92473
  languageName: node
  linkType: hard

"backo2@npm:1.0.2":
  version: 1.0.2
  resolution: "backo2@npm:1.0.2"
  checksum: 10c0/a9e825a6a38a6d1c4a94476eabc13d6127dfaafb0967baf104affbb67806ae26abbb58dab8d572d2cd21ef06634ff57c3ad48dff14b904e18de1474cc2f22bf3
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"base64-arraybuffer@npm:0.1.5":
  version: 0.1.5
  resolution: "base64-arraybuffer@npm:0.1.5"
  checksum: 10c0/90afdff8ecae0ea96709f8d65037585bcabddfb222bc8b46408b74b982a8322f36fe1f97468d84e6e18e01ac165ee1c6570bde6c8f9b4f64a3e9374885237a76
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10c0/f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"base@npm:^0.11.1":
  version: 0.11.2
  resolution: "base@npm:0.11.2"
  dependencies:
    cache-base: "npm:^1.0.1"
    class-utils: "npm:^0.3.5"
    component-emitter: "npm:^1.2.1"
    define-property: "npm:^1.0.0"
    isobject: "npm:^3.0.1"
    mixin-deep: "npm:^1.2.0"
    pascalcase: "npm:^0.1.1"
  checksum: 10c0/30a2c0675eb52136b05ef496feb41574d9f0bb2d6d677761da579c00a841523fccf07f1dbabec2337b5f5750f428683b8ca60d89e56a1052c4ae1c0cd05de64d
  languageName: node
  linkType: hard

"better-assert@npm:~1.0.0":
  version: 1.0.2
  resolution: "better-assert@npm:1.0.2"
  dependencies:
    callsite: "npm:1.0.0"
  checksum: 10c0/9bba805b3472e05b2d2d2bf2b33d53c9d066c60ea0f015a587c1ad450a409f2018df2d61596ad27d8aaeed5ff786c567bbaf648649e122dbea7d9ca0b41c32fe
  languageName: node
  linkType: hard

"big-integer@npm:^1.6.44":
  version: 1.6.51
  resolution: "big-integer@npm:1.6.51"
  checksum: 10c0/c8139662d57f8833a44802f4b65be911679c569535ea73c5cfd3c1c8994eaead1b84b6f63e1db63833e4d4cacb6b6a9e5522178113dfdc8e4c81ed8436f1e8cc
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.2.0
  resolution: "binary-extensions@npm:2.2.0"
  checksum: 10c0/d73d8b897238a2d3ffa5f59c0241870043aa7471335e89ea5e1ff48edb7c2d0bb471517a3e4c5c3f4c043615caa2717b5f80a5e61e07503d51dc85cb848e665d
  languageName: node
  linkType: hard

"blob@npm:0.0.4":
  version: 0.0.4
  resolution: "blob@npm:0.0.4"
  checksum: 10c0/a0f35dba3e10591e0d95de5c4a9a360ee3cb58d952e7995d2482fae4f2633e16652515dcc6023ee7a4eb90cda98ec61441541d308316c1939608ed4444514b8c
  languageName: node
  linkType: hard

"body-parser@npm:1.19.0":
  version: 1.19.0
  resolution: "body-parser@npm:1.19.0"
  dependencies:
    bytes: "npm:3.1.0"
    content-type: "npm:~1.0.4"
    debug: "npm:2.6.9"
    depd: "npm:~1.1.2"
    http-errors: "npm:1.7.2"
    iconv-lite: "npm:0.4.24"
    on-finished: "npm:~2.3.0"
    qs: "npm:6.7.0"
    raw-body: "npm:2.4.0"
    type-is: "npm:~1.6.17"
  checksum: 10c0/df97c94a16495db166dba4c7812a43ba800ea252a76a1de80be944e2b884b808897febb920880c30089ac01f74f9118ca589402294c0ea5e2075488e4f91dc09
  languageName: node
  linkType: hard

"bplist-parser@npm:^0.2.0":
  version: 0.2.0
  resolution: "bplist-parser@npm:0.2.0"
  dependencies:
    big-integer: "npm:^1.6.44"
  checksum: 10c0/ce79c69e0f6efe506281e7c84e3712f7d12978991675b6e3a58a295b16f13ca81aa9b845c335614a545e0af728c8311b6aa3142af76ba1cb616af9bbac5c4a9f
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10c0/695a56cd058096a7cb71fb09d9d6a7070113c7be516699ed361317aca2ec169f618e28b8af352e02ab4233fb54eb0168460a40dc320bab0034b36ab59aaad668
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/b358f2fe060e2d7a87aa015979ecea07f3c37d4018f8d6deb5bd4c229ad3a0384fe6029bb76cd8be63c81e516ee52d1a0673edbe2023d53a5191732ae3c3e49f
  languageName: node
  linkType: hard

"braces@npm:^2.3.1":
  version: 2.3.2
  resolution: "braces@npm:2.3.2"
  dependencies:
    arr-flatten: "npm:^1.1.0"
    array-unique: "npm:^0.3.2"
    extend-shallow: "npm:^2.0.1"
    fill-range: "npm:^4.0.0"
    isobject: "npm:^3.0.1"
    repeat-element: "npm:^1.1.2"
    snapdragon: "npm:^0.8.1"
    snapdragon-node: "npm:^2.0.1"
    split-string: "npm:^3.0.2"
    to-regex: "npm:^3.0.1"
  checksum: 10c0/72b27ea3ea2718f061c29e70fd6e17606e37c65f5801abddcf0b0052db1de7d60f3bf92cfc220ab57b44bd0083a5f69f9d03b3461d2816cfe9f9398207acc728
  languageName: node
  linkType: hard

"braces@npm:^3.0.2, braces@npm:~3.0.2":
  version: 3.0.2
  resolution: "braces@npm:3.0.2"
  dependencies:
    fill-range: "npm:^7.0.1"
  checksum: 10c0/321b4d675791479293264019156ca322163f02dc06e3c4cab33bb15cd43d80b51efef69b0930cfde3acd63d126ebca24cd0544fa6f261e093a0fb41ab9dda381
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 10c0/124fff9d66d691a86d3b062eff4663fe437a9d9ee4b47b1b9e97f5a5d14f6d5399345db80f796827be7c95e70a8e765dd404b7c3ff3b3324f98e9b0c8826cc34
  languageName: node
  linkType: hard

"buffer@npm:^6.0.3":
  version: 6.0.3
  resolution: "buffer@npm:6.0.3"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.2.1"
  checksum: 10c0/2a905fbbcde73cc5d8bd18d1caa23715d5f83a5935867c2329f0ac06104204ba7947be098fe1317fbd8830e26090ff8e764f08cd14fefc977bb248c3487bcbd0
  languageName: node
  linkType: hard

"builtin-modules@npm:^3.2.0":
  version: 3.3.0
  resolution: "builtin-modules@npm:3.3.0"
  checksum: 10c0/2cb3448b4f7306dc853632a4fcddc95e8d4e4b9868c139400027b71938fc6806d4ff44007deffb362ac85724bd40c2c6452fb6a0aa4531650eeddb98d8e5ee8a
  languageName: node
  linkType: hard

"builtins@npm:^1.0.3":
  version: 1.0.3
  resolution: "builtins@npm:1.0.3"
  checksum: 10c0/493afcc1db0a56d174cc85bebe5ca69144f6fdd0007d6cbe6b2434185314c79d83cb867e492b56aa5cf421b4b8a8135bf96ba4c3ce71994cf3da154d1ea59747
  languageName: node
  linkType: hard

"bull-board@npm:^2.0.3":
  version: 2.1.3
  resolution: "bull-board@npm:2.1.3"
  dependencies:
    "@types/express": "npm:^4.17.12"
    "@types/express-serve-static-core": "npm:^4.17.20"
    ejs: "npm:3.1.6"
    express: "npm:4.17.1"
    redis-info: "npm:^3.0.8"
  checksum: 10c0/d530a5bd1302d06f3b8bf37db6a9107b5d34109b058cfec06af8ccc8ef2cb75a30db7ef802b8ab5ee619d24d30ebba9dbcf0ecccc9847e8e7eb3651a4b8c0d36
  languageName: node
  linkType: hard

"bullmq@npm:^1.24.5":
  version: 1.91.1
  resolution: "bullmq@npm:1.91.1"
  dependencies:
    cron-parser: "npm:^4.6.0"
    get-port: "npm:6.1.2"
    glob: "npm:^8.0.3"
    ioredis: "npm:^5.2.2"
    lodash: "npm:^4.17.21"
    msgpackr: "npm:^1.6.2"
    semver: "npm:^7.3.7"
    tslib: "npm:^2.0.0"
    uuid: "npm:^9.0.0"
  checksum: 10c0/a499f49cca6cd131c21ef6b31b1f857493b4da2f162ff9ef6c5e8d8ead01f59265be130d8829063e2ac1407d18e92d3fcd06ec6135c7bc9d2f65fd0cd440699f
  languageName: node
  linkType: hard

"bundle-name@npm:^3.0.0":
  version: 3.0.0
  resolution: "bundle-name@npm:3.0.0"
  dependencies:
    run-applescript: "npm:^5.0.0"
  checksum: 10c0/57bc7f8b025d83961b04db2f1eff6a87f2363c2891f3542a4b82471ff8ebb5d484af48e9784fcdb28ef1d48bb01f03d891966dc3ef58758e46ea32d750ce40f8
  languageName: node
  linkType: hard

"bytes@npm:3.1.0":
  version: 3.1.0
  resolution: "bytes@npm:3.1.0"
  checksum: 10c0/7034f475b006b9a8a37c7ecaa0947d0be181feb6d3d5231984e4c14e01c587a47e0fe85f66c630689fa6a046cfa498b6891f5af8022357e52db09365f1dfb625
  languageName: node
  linkType: hard

"bytes@npm:3.1.2, bytes@npm:^3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 10c0/76d1c43cbd602794ad8ad2ae94095cddeb1de78c5dddaa7005c51af10b0176c69971a6d88e805a90c2b6550d76636e43c40d8427a808b8645ede885de4a0358e
  languageName: node
  linkType: hard

"cacache@npm:^17.0.0":
  version: 17.1.3
  resolution: "cacache@npm:17.1.3"
  dependencies:
    "@npmcli/fs": "npm:^3.1.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^7.7.1"
    minipass: "npm:^5.0.0"
    minipass-collect: "npm:^1.0.2"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^4.0.0"
    ssri: "npm:^10.0.0"
    tar: "npm:^6.1.11"
    unique-filename: "npm:^3.0.0"
  checksum: 10c0/fcb0843c8e152b0e1440328508a2c0d6435c431198155e31daa591b348a1739b089ce2a72a4528690ed10a2bf086c180ee4980e2116457131b4c8a6e65e10976
  languageName: node
  linkType: hard

"cache-base@npm:^1.0.1":
  version: 1.0.1
  resolution: "cache-base@npm:1.0.1"
  dependencies:
    collection-visit: "npm:^1.0.0"
    component-emitter: "npm:^1.2.1"
    get-value: "npm:^2.0.6"
    has-value: "npm:^1.0.0"
    isobject: "npm:^3.0.1"
    set-value: "npm:^2.0.0"
    to-object-path: "npm:^0.3.0"
    union-value: "npm:^1.0.0"
    unset-value: "npm:^1.0.0"
  checksum: 10c0/a7142e25c73f767fa520957dcd179b900b86eac63b8cfeaa3b2a35e18c9ca5968aa4e2d2bed7a3e7efd10f13be404344cfab3a4156217e71f9bdb95940bb9c8c
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.0":
  version: 1.0.2
  resolution: "call-bind@npm:1.0.2"
  dependencies:
    function-bind: "npm:^1.1.1"
    get-intrinsic: "npm:^1.0.2"
  checksum: 10c0/74ba3f31e715456e22e451d8d098779b861eba3c7cac0d9b510049aced70d75c231ba05071f97e1812c98e34e2bee734c0c6126653e0088c2d9819ca047f4073
  languageName: node
  linkType: hard

"call-me-maybe@npm:^1.0.1":
  version: 1.0.2
  resolution: "call-me-maybe@npm:1.0.2"
  checksum: 10c0/8eff5dbb61141ebb236ed71b4e9549e488bcb5451c48c11e5667d5c75b0532303788a1101e6978cafa2d0c8c1a727805599c2741e3e0982855c9f1d78cd06c9f
  languageName: node
  linkType: hard

"callsite@npm:1.0.0":
  version: 1.0.0
  resolution: "callsite@npm:1.0.0"
  checksum: 10c0/8b23d5ed879984b66fe3da381994d6c4b741e561226abc48b40c99c4896f7125db395ea4aa989071a7eb0712c3f83bc32fb1e798fdf54967acdf4af176e48572
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0, callsites@npm:^3.1.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10c0/fff92277400eb06c3079f9e74f3af120db9f8ea03bad0e84d9aede54bbe2d44a56cccb5f6cf12211f93f52306df87077ecec5b712794c5a9b5dac6d615a3f301
  languageName: node
  linkType: hard

"camel-case@npm:^4.1.2":
  version: 4.1.2
  resolution: "camel-case@npm:4.1.2"
  dependencies:
    pascal-case: "npm:^3.1.2"
    tslib: "npm:^2.0.3"
  checksum: 10c0/bf9eefaee1f20edbed2e9a442a226793bc72336e2b99e5e48c6b7252b6f70b080fc46d8246ab91939e2af91c36cdd422e0af35161e58dd089590f302f8f64c8a
  languageName: node
  linkType: hard

"capital-case@npm:^1.0.4":
  version: 1.0.4
  resolution: "capital-case@npm:1.0.4"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
    upper-case-first: "npm:^2.0.2"
  checksum: 10c0/6a034af73401f6e55d91ea35c190bbf8bda21714d4ea8bb8f1799311d123410a80f0875db4e3236dc3f97d74231ff4bf1c8783f2be13d7733c7d990c57387281
  languageName: node
  linkType: hard

"cardinal@npm:^2.1.1":
  version: 2.1.1
  resolution: "cardinal@npm:2.1.1"
  dependencies:
    ansicolors: "npm:~0.3.2"
    redeyed: "npm:~2.1.0"
  bin:
    cdl: ./bin/cdl.js
  checksum: 10c0/0051d0e64c0e1dff480c1aace4c018c48ecca44030533257af3f023107ccdeb061925603af6d73710f0345b0ae0eb57e5241d181d9b5fdb595d45c5418161675
  languageName: node
  linkType: hard

"chai@npm:^4.3.7":
  version: 4.3.7
  resolution: "chai@npm:4.3.7"
  dependencies:
    assertion-error: "npm:^1.1.0"
    check-error: "npm:^1.0.2"
    deep-eql: "npm:^4.1.2"
    get-func-name: "npm:^2.0.0"
    loupe: "npm:^2.3.1"
    pathval: "npm:^1.1.1"
    type-detect: "npm:^4.0.5"
  checksum: 10c0/a11c6b74ce2d5587c3db1f1e5bf32073876319d4c65ba4e574ca9b56ec93ebbc80765e1fa4af354553afbf7ed245fb54c45d69d350a7b850c4aaf9f1e01f950f
  languageName: node
  linkType: hard

"chalk@npm:^1.1.3":
  version: 1.1.3
  resolution: "chalk@npm:1.1.3"
  dependencies:
    ansi-styles: "npm:^2.2.1"
    escape-string-regexp: "npm:^1.0.2"
    has-ansi: "npm:^2.0.0"
    strip-ansi: "npm:^3.0.0"
    supports-color: "npm:^2.0.0"
  checksum: 10c0/28c3e399ec286bb3a7111fd4225ebedb0d7b813aef38a37bca7c498d032459c265ef43404201d5fbb8d888d29090899c95335b4c0cda13e8b126ff15c541cef8
  languageName: node
  linkType: hard

"chalk@npm:^3.0.0":
  version: 3.0.0
  resolution: "chalk@npm:3.0.0"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/ee650b0a065b3d7a6fda258e75d3a86fc8e4effa55871da730a9e42ccb035bf5fd203525e5a1ef45ec2582ecc4f65b47eb11357c526b84dd29a14fb162c414d2
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.0.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"chalk@npm:^5.2.0":
  version: 5.3.0
  resolution: "chalk@npm:5.3.0"
  checksum: 10c0/8297d436b2c0f95801103ff2ef67268d362021b8210daf8ddbe349695333eb3610a71122172ff3b0272f1ef2cf7cc2c41fdaa4715f52e49ffe04c56340feed09
  languageName: node
  linkType: hard

"change-case@npm:^4.1.2":
  version: 4.1.2
  resolution: "change-case@npm:4.1.2"
  dependencies:
    camel-case: "npm:^4.1.2"
    capital-case: "npm:^1.0.4"
    constant-case: "npm:^3.0.4"
    dot-case: "npm:^3.0.4"
    header-case: "npm:^2.0.4"
    no-case: "npm:^3.0.4"
    param-case: "npm:^3.0.4"
    pascal-case: "npm:^3.1.2"
    path-case: "npm:^3.0.4"
    sentence-case: "npm:^3.0.4"
    snake-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10c0/95a6e48563cd393241ce18470c7310a8a050304a64b63addac487560ab039ce42b099673d1d293cc10652324d92060de11b5d918179fe3b5af2ee521fb03ca58
  languageName: node
  linkType: hard

"charenc@npm:~0.0.1":
  version: 0.0.2
  resolution: "charenc@npm:0.0.2"
  checksum: 10c0/a45ec39363a16799d0f9365c8dd0c78e711415113c6f14787a22462ef451f5013efae8a28f1c058f81fc01f2a6a16955f7a5fd0cd56247ce94a45349c89877d8
  languageName: node
  linkType: hard

"check-error@npm:^1.0.2":
  version: 1.0.2
  resolution: "check-error@npm:1.0.2"
  checksum: 10c0/c58ac4d6a92203209a61d025568198c073f101691eb6247f999266e1d1e3ab3af2bbe0a41af5008c1f1b95446ec7831e6ba91f03816177f2da852f316ad7921d
  languageName: node
  linkType: hard

"chokidar@npm:^3.5.3":
  version: 3.5.3
  resolution: "chokidar@npm:3.5.3"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/1076953093e0707c882a92c66c0f56ba6187831aa51bb4de878c1fec59ae611a3bf02898f190efec8e77a086b8df61c2b2a3ea324642a0558bdf8ee6c5dc9ca1
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: 10c0/594754e1303672171cc04e50f6c398ae16128eb134a88f801bf5354fd96f205320f23536a045d9abd8b51024a149696e51231565891d4efdab8846021ecf88e6
  languageName: node
  linkType: hard

"class-utils@npm:^0.3.5":
  version: 0.3.6
  resolution: "class-utils@npm:0.3.6"
  dependencies:
    arr-union: "npm:^3.1.0"
    define-property: "npm:^0.2.5"
    isobject: "npm:^3.0.0"
    static-extend: "npm:^0.1.1"
  checksum: 10c0/d44f4afc7a3e48dba4c2d3fada5f781a1adeeff371b875c3b578bc33815c6c29d5d06483c2abfd43a32d35b104b27b67bfa39c2e8a422fa858068bd756cfbd42
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 10c0/1f90262d5f6230a17e27d0c190b09d47ebe7efdd76a03b5a1127863f7b3c9aec4c3e6c8bb3a7bbf81d553d56a1fd35728f5a8ef4c63f867ac8d690109742a8c1
  languageName: node
  linkType: hard

"cli-boxes@npm:^3.0.0":
  version: 3.0.0
  resolution: "cli-boxes@npm:3.0.0"
  checksum: 10c0/4db3e8fbfaf1aac4fb3a6cbe5a2d3fa048bee741a45371b906439b9ffc821c6e626b0f108bdcd3ddf126a4a319409aedcf39a0730573ff050fdd7b6731e99fb9
  languageName: node
  linkType: hard

"cli-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-cursor@npm:3.1.0"
  dependencies:
    restore-cursor: "npm:^3.1.0"
  checksum: 10c0/92a2f98ff9037d09be3dfe1f0d749664797fb674bf388375a2207a1203b69d41847abf16434203e0089212479e47a358b13a0222ab9fccfe8e2644a7ccebd111
  languageName: node
  linkType: hard

"cli-table3@npm:^0.6.3":
  version: 0.6.3
  resolution: "cli-table3@npm:0.6.3"
  dependencies:
    "@colors/colors": "npm:1.5.0"
    string-width: "npm:^4.2.0"
  dependenciesMeta:
    "@colors/colors":
      optional: true
  checksum: 10c0/39e580cb346c2eaf1bd8f4ff055ae644e902b8303c164a1b8894c0dc95941f92e001db51f49649011be987e708d9fa3183ccc2289a4d376a057769664048cc0c
  languageName: node
  linkType: hard

"clone-deep@npm:^4.0.1":
  version: 4.0.1
  resolution: "clone-deep@npm:4.0.1"
  dependencies:
    is-plain-object: "npm:^2.0.4"
    kind-of: "npm:^6.0.2"
    shallow-clone: "npm:^3.0.0"
  checksum: 10c0/637753615aa24adf0f2d505947a1bb75e63964309034a1cf56ba4b1f30af155201edd38d26ffe26911adaae267a3c138b344a4947d39f5fc1b6d6108125aa758
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: 10c0/2176952b3649293473999a95d7bebfc9dc96410f6cbd3d2595cf12fd401f63a4bf41a7adbfd3ab2ff09ed60cb9870c58c6acdd18b87767366fabfc163700f13b
  languageName: node
  linkType: hard

"clone@npm:^2.1.2":
  version: 2.1.2
  resolution: "clone@npm:2.1.2"
  checksum: 10c0/ed0601cd0b1606bc7d82ee7175b97e68d1dd9b91fd1250a3617b38d34a095f8ee0431d40a1a611122dcccb4f93295b4fdb94942aa763392b5fe44effa50c2d5e
  languageName: node
  linkType: hard

"cluster-key-slot@npm:^1.1.0":
  version: 1.1.2
  resolution: "cluster-key-slot@npm:1.1.2"
  checksum: 10c0/d7d39ca28a8786e9e801eeb8c770e3c3236a566625d7299a47bb71113fb2298ce1039596acb82590e598c52dbc9b1f088c8f587803e697cb58e1867a95ff94d3
  languageName: node
  linkType: hard

"co-compose@npm:^7.0.2":
  version: 7.0.3
  resolution: "co-compose@npm:7.0.3"
  checksum: 10c0/ec673f03c82b50effa26d07f9faa26e96e256188f5ec02574a1b04036d91f8bad1d57bb30926853f834d6a5e88748270d7e50854d2716a8fd131465221937d91
  languageName: node
  linkType: hard

"collection-visit@npm:^1.0.0":
  version: 1.0.0
  resolution: "collection-visit@npm:1.0.0"
  dependencies:
    map-visit: "npm:^1.0.0"
    object-visit: "npm:^1.0.0"
  checksum: 10c0/add72a8d1c37cb90e53b1aaa2c31bf1989bfb733f0b02ce82c9fa6828c7a14358dba2e4f8e698c02f69e424aeccae1ffb39acdeaf872ade2f41369e84a2fcf8a
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"color-support@npm:^1.1.3":
  version: 1.1.3
  resolution: "color-support@npm:1.1.3"
  bin:
    color-support: bin.js
  checksum: 10c0/8ffeaa270a784dc382f62d9be0a98581db43e11eee301af14734a6d089bd456478b1a8b3e7db7ca7dc5b18a75f828f775c44074020b51c05fc00e6d0992b1cc6
  languageName: node
  linkType: hard

"colorette@npm:2.0.19":
  version: 2.0.19
  resolution: "colorette@npm:2.0.19"
  checksum: 10c0/2bcc9134095750fece6e88167011499b964b78bf0ea953469130ddb1dba3c8fe6c03debb0ae181e710e2be10900d117460f980483a7df4ba4a1bac3b182ecb64
  languageName: node
  linkType: hard

"colorette@npm:^2.0.7":
  version: 2.0.20
  resolution: "colorette@npm:2.0.20"
  checksum: 10c0/e94116ff33b0ff56f3b83b9ace895e5bf87c2a7a47b3401b8c3f3226e050d5ef76cf4072fb3325f9dc24d1698f9b730baf4e05eeaf861d74a1883073f4c98a40
  languageName: node
  linkType: hard

"columnify@npm:^1.5.4":
  version: 1.6.0
  resolution: "columnify@npm:1.6.0"
  dependencies:
    strip-ansi: "npm:^6.0.1"
    wcwidth: "npm:^1.0.0"
  checksum: 10c0/25b90b59129331bbb8b0c838f8df69924349b83e8eab9549f431062a20a39094b8d744bb83265be38fd5d03140ce4bfbd85837c293f618925e83157ae9535f1d
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10c0/0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"commander@npm:^10.0.0":
  version: 10.0.1
  resolution: "commander@npm:10.0.1"
  checksum: 10c0/53f33d8927758a911094adadda4b2cbac111a5b377d8706700587650fd8f45b0bbe336de4b5c3fe47fd61f420a3d9bd452b6e0e6e5600a7e74d7bf0174f6efe3
  languageName: node
  linkType: hard

"commander@npm:^2.19.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: 10c0/74c781a5248c2402a0a3e966a0a2bba3c054aad144f5c023364be83265e796b20565aa9feff624132ff629aa64e16999fa40a743c10c12f7c61e96a794b99288
  languageName: node
  linkType: hard

"comment-json@npm:^2.2.0":
  version: 2.4.2
  resolution: "comment-json@npm:2.4.2"
  dependencies:
    core-util-is: "npm:^1.0.2"
    esprima: "npm:^4.0.1"
    has-own-prop: "npm:^2.0.0"
    repeat-string: "npm:^1.6.1"
  checksum: 10c0/409aafaab6a8ed7d22220540fd8577a2943f22e8a78ae4745c8e4de070b72d0ad67dd62b4e950bcda838125d7bb76f6d874a4a577a5a5e8af8847bf411c800a1
  languageName: node
  linkType: hard

"commondir@npm:^1.0.1":
  version: 1.0.1
  resolution: "commondir@npm:1.0.1"
  checksum: 10c0/33a124960e471c25ee19280c9ce31ccc19574b566dc514fe4f4ca4c34fa8b0b57cf437671f5de380e11353ea9426213fca17687dd2ef03134fea2dbc53809fd6
  languageName: node
  linkType: hard

"component-bind@npm:1.0.0":
  version: 1.0.0
  resolution: "component-bind@npm:1.0.0"
  checksum: 10c0/839485d60fdf21e1303cd739c76f5adac046f7b3d5d7b666ad1259ccff562c7e7afc8922b40e4a5c4665789a7fb6645e527214c80a922093f392d89685fda488
  languageName: node
  linkType: hard

"component-emitter@npm:1.1.2":
  version: 1.1.2
  resolution: "component-emitter@npm:1.1.2"
  checksum: 10c0/5a8e551c4554b1b35f4eed6f2ceda4fdf157633906907f371f895ac617b0bd314d1eb2f35b4ad3e881f7a5687c437eef76e6040b4adcc5a1fd126ec2062ee1f3
  languageName: node
  linkType: hard

"component-emitter@npm:1.2.1":
  version: 1.2.1
  resolution: "component-emitter@npm:1.2.1"
  checksum: 10c0/6c27bd7bba028776464cee6c1686c8e02cb9a576a11df93f1fc211ae3eb2de234ae90952d0b7fb3acc9c92c8baa389fa7389681b2e8689d2ca463e94f3ad30b2
  languageName: node
  linkType: hard

"component-emitter@npm:^1.2.1, component-emitter@npm:^1.3.0":
  version: 1.3.0
  resolution: "component-emitter@npm:1.3.0"
  checksum: 10c0/68774a0a3754fb6c0ba53c2e88886dfbd0c773931066abb1d7fd1b0c893b2a838d8f088ab4dca1f18cc1a4fc2e6932019eba3ded2d931b5ba2241ce40e93a24f
  languageName: node
  linkType: hard

"component-inherit@npm:0.0.3":
  version: 0.0.3
  resolution: "component-inherit@npm:0.0.3"
  checksum: 10c0/f9f7b0555aa4976e227b28e1cfc1a775941f053fb1c33be81ece31afd759807ff0f35a437fb61a08eb1039a3444a970f29e2808d0621df4eed9ff510849fba36
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10c0/c996b1cfdf95b6c90fee4dae37e332c8b6eb7d106430c17d538034c0ad9a1630cb194d2ab37293b1bdd4d779494beee7786d586a50bd9376fd6f7bcc2bd4c98f
  languageName: node
  linkType: hard

"console-control-strings@npm:^1.1.0":
  version: 1.1.0
  resolution: "console-control-strings@npm:1.1.0"
  checksum: 10c0/7ab51d30b52d461412cd467721bb82afe695da78fff8f29fe6f6b9cbaac9a2328e27a22a966014df9532100f6dd85370460be8130b9c677891ba36d96a343f50
  languageName: node
  linkType: hard

"constant-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "constant-case@npm:3.0.4"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
    upper-case: "npm:^2.0.2"
  checksum: 10c0/91d54f18341fcc491ae66d1086642b0cc564be3e08984d7b7042f8b0a721c8115922f7f11d6a09f13ed96ff326eabae11f9d1eb0335fa9d8b6e39e4df096010e
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.3":
  version: 0.5.3
  resolution: "content-disposition@npm:0.5.3"
  dependencies:
    safe-buffer: "npm:5.1.2"
  checksum: 10c0/988f131fedb2b79002337b5480951cc73f86e876b3e7feb6617b92e40a01f633db6f4c7765d486c02b468890465b2df96b7652b7e39caf22cc63517cf2e99839
  languageName: node
  linkType: hard

"content-disposition@npm:^0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: "npm:5.2.1"
  checksum: 10c0/bac0316ebfeacb8f381b38285dc691c9939bf0a78b0b7c2d5758acadad242d04783cee5337ba7d12a565a19075af1b3c11c728e1e4946de73c6ff7ce45f3f1bb
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 10c0/b76ebed15c000aee4678c3707e0860cb6abd4e680a598c0a26e17f0bfae723ec9cc2802f0ff1bc6e4d80603719010431d2231018373d4dde10f9ccff9dadf5af
  languageName: node
  linkType: hard

"convert-hrtime@npm:^3.0.0":
  version: 3.0.0
  resolution: "convert-hrtime@npm:3.0.0"
  checksum: 10c0/3db1f27ecd8052f5b5eeb38c7180d3ee8f45705edad67eb6c89dd1ddfaf683d378169ace0ea147e7b61c5b038ff1bf59a0c0453d2812300e86977e03584df929
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: 10c0/b36fd0d4e3fef8456915fcf7742e58fbfcc12a17a018e0eb9501c9d5ef6893b596466f03b0564b81af29ff2538fd0aa4b9d54fe5ccbfb4c90ea50ad29fe2d221
  languageName: node
  linkType: hard

"cookie@npm:0.4.0":
  version: 0.4.0
  resolution: "cookie@npm:0.4.0"
  checksum: 10c0/71508a1c8a4e97bb88f42635542ef24ebe7e713f82573ac61e9b289616334d14bfb28210d7979d9ada24b0254f5fb563af938cac13bc8c0c3f60f47a2257f791
  languageName: node
  linkType: hard

"cookie@npm:^0.5.0":
  version: 0.5.0
  resolution: "cookie@npm:0.5.0"
  checksum: 10c0/c01ca3ef8d7b8187bae434434582288681273b5a9ed27521d4d7f9f7928fe0c920df0decd9f9d3bbd2d14ac432b8c8cf42b98b3bdd5bfe0e6edddeebebe8b61d
  languageName: node
  linkType: hard

"cookiejar@npm:^2.1.4":
  version: 2.1.4
  resolution: "cookiejar@npm:2.1.4"
  checksum: 10c0/2dae55611c6e1678f34d93984cbd4bda58f4fe3e5247cc4993f4a305cd19c913bbaf325086ed952e892108115073a747596453d3dc1c34947f47f731818b8ad1
  languageName: node
  linkType: hard

"copy-descriptor@npm:^0.1.0":
  version: 0.1.1
  resolution: "copy-descriptor@npm:0.1.1"
  checksum: 10c0/161f6760b7348c941007a83df180588fe2f1283e0867cc027182734e0f26134e6cc02de09aa24a95dc267b2e2025b55659eef76c8019df27bc2d883033690181
  languageName: node
  linkType: hard

"core-util-is@npm:^1.0.2":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 10c0/90a0e40abbddfd7618f8ccd63a74d88deea94e77d0e8dbbea059fa7ebebb8fbb4e2909667fe26f3a467073de1a542ebe6ae4c73a73745ac5833786759cd906c9
  languageName: node
  linkType: hard

"cp-file@npm:^7.0.0":
  version: 7.0.0
  resolution: "cp-file@npm:7.0.0"
  dependencies:
    graceful-fs: "npm:^4.1.2"
    make-dir: "npm:^3.0.0"
    nested-error-stacks: "npm:^2.0.0"
    p-event: "npm:^4.1.0"
  checksum: 10c0/db3ef3e3e466742f392ae71edb9b2cdbb314e855d97630a65de57bc1097bacf6e844f6d9d44882b8678c0de26ba7e656c2c915960435970067823372e807eafa
  languageName: node
  linkType: hard

"cp-file@npm:^9.1.0":
  version: 9.1.0
  resolution: "cp-file@npm:9.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.2"
    make-dir: "npm:^3.0.0"
    nested-error-stacks: "npm:^2.0.0"
    p-event: "npm:^4.1.0"
  checksum: 10c0/866a884b11b6a6c54263aaa9bbf802943025fcfe402e1f0d75600d4877c04fd106b9f6f61168fdedff01314763c544438c727610f2de8bc61a2fc324de46743f
  languageName: node
  linkType: hard

"cpy@npm:^8.1.2":
  version: 8.1.2
  resolution: "cpy@npm:8.1.2"
  dependencies:
    arrify: "npm:^2.0.1"
    cp-file: "npm:^7.0.0"
    globby: "npm:^9.2.0"
    has-glob: "npm:^1.0.0"
    junk: "npm:^3.1.0"
    nested-error-stacks: "npm:^2.1.0"
    p-all: "npm:^2.1.0"
    p-filter: "npm:^2.1.0"
    p-map: "npm:^3.0.0"
  checksum: 10c0/84611fdd526a0582ae501a0fa1e1d55e16348c69110eb17be5fc0c087b7b2aa6caec014286b669e4f123750d01e0c4db77d32fdcdb9840c3df4d161a137a345a
  languageName: node
  linkType: hard

"cron-parser@npm:^4.6.0":
  version: 4.8.1
  resolution: "cron-parser@npm:4.8.1"
  dependencies:
    luxon: "npm:^3.2.1"
  checksum: 10c0/d14bb09277969085068e97b9d65b71175f66065c842f11499890903ac137d476e7d349cf2c026d8e53ac4a5503101ca1f96b6ecfb646063a1ed2440533729776
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.2, cross-spawn@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-spawn@npm:7.0.3"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/5738c312387081c98d69c98e105b6327b069197f864a60593245d64c8089c8a0a744e16349281210d56835bb9274130d825a78b2ad6853ca13cfbeffc0c31750
  languageName: node
  linkType: hard

"crypt@npm:~0.0.1":
  version: 0.0.2
  resolution: "crypt@npm:0.0.2"
  checksum: 10c0/adbf263441dd801665d5425f044647533f39f4612544071b1471962209d235042fb703c27eea2795c7c53e1dfc242405173003f83cf4f4761a633d11f9653f18
  languageName: node
  linkType: hard

"cuid@npm:^2.1.8":
  version: 2.1.8
  resolution: "cuid@npm:2.1.8"
  checksum: 10c0/6514d247c6de3e5cd32f485821365f4816ee14b777841fa717d37b7a8f791f3cb268be9e37ca54b110f6f5f625e4f0af581c3226aa14f259790539ba20fdaf61
  languageName: node
  linkType: hard

"dag-map@npm:~1.0.0":
  version: 1.0.2
  resolution: "dag-map@npm:1.0.2"
  checksum: 10c0/1b5ee77cbc9caf61178db592ecc8fa8f6905fd4b0571176af74d2fece2332b68c0e9e8275f1c2c76bc1f0c84a9dc973f87233db7a06375bd13254fae9866867f
  languageName: node
  linkType: hard

"data-uri-to-buffer@npm:^2.0.0":
  version: 2.0.2
  resolution: "data-uri-to-buffer@npm:2.0.2"
  checksum: 10c0/341b6191ed65fa453e97a6d44db06082121ebc2ef3e6e096dfb6a1ebbc75e8be39d4199a5b4dba0f0efc43f2a3b2bcc276d85cf1407eba880eb09ebf17c3c31e
  languageName: node
  linkType: hard

"dateformat@npm:^4.6.3":
  version: 4.6.3
  resolution: "dateformat@npm:4.6.3"
  checksum: 10c0/e2023b905e8cfe2eb8444fb558562b524807a51cdfe712570f360f873271600b5c94aebffaf11efb285e2c072264a7cf243eadb68f3eba0f8cc85fb86cd25df6
  languageName: node
  linkType: hard

"debug@npm:2.2.0":
  version: 2.2.0
  resolution: "debug@npm:2.2.0"
  dependencies:
    ms: "npm:0.7.1"
  checksum: 10c0/d24d6200c9d9bef20e1dcb823a9895193a45c39505606468735f387bdd850a21baf4c7841df1787f9a02596cbf0f111d60726ba3fa7511e22198b91b33440fe9
  languageName: node
  linkType: hard

"debug@npm:2.3.3":
  version: 2.3.3
  resolution: "debug@npm:2.3.3"
  dependencies:
    ms: "npm:0.7.2"
  checksum: 10c0/c53325c15b80b059dcfcab9eae162e0878487d3acc0f12813a488cbe8dd96139730fd0eec5d8d8274fe324bd0d874d0414666a0e29c79ed1fd01de8bce287c86
  languageName: node
  linkType: hard

"debug@npm:2.6.9, debug@npm:^2.2.0, debug@npm:^2.3.3":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: 10c0/121908fb839f7801180b69a7e218a40b5a0b718813b886b7d6bdb82001b931c938e2941d1e4450f33a1b1df1da653f5f7a0440c197f29fbf8a6e9d45ff6ef589
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:4.3.4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.2, debug@npm:^4.3.3, debug@npm:^4.3.4":
  version: 4.3.4
  resolution: "debug@npm:4.3.4"
  dependencies:
    ms: "npm:2.1.2"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/cedbec45298dd5c501d01b92b119cd3faebe5438c3917ff11ae1bff86a6c722930ac9c8659792824013168ba6db7c4668225d845c633fbdafbbf902a6389f736
  languageName: node
  linkType: hard

"decimal.js@npm:^10.3.1":
  version: 10.4.3
  resolution: "decimal.js@npm:10.4.3"
  checksum: 10c0/6d60206689ff0911f0ce968d40f163304a6c1bc739927758e6efc7921cfa630130388966f16bf6ef6b838cb33679fbe8e7a78a2f3c478afce841fd55ac8fb8ee
  languageName: node
  linkType: hard

"decode-uri-component@npm:^0.2.0":
  version: 0.2.2
  resolution: "decode-uri-component@npm:0.2.2"
  checksum: 10c0/1f4fa54eb740414a816b3f6c24818fbfcabd74ac478391e9f4e2282c994127db02010ce804f3d08e38255493cfe68608b3f5c8e09fd6efc4ae46c807691f7a31
  languageName: node
  linkType: hard

"deep-eql@npm:^4.1.2":
  version: 4.1.3
  resolution: "deep-eql@npm:4.1.3"
  dependencies:
    type-detect: "npm:^4.0.0"
  checksum: 10c0/ff34e8605d8253e1bf9fe48056e02c6f347b81d9b5df1c6650a1b0f6f847b4a86453b16dc226b34f853ef14b626e85d04e081b022e20b00cd7d54f079ce9bbdd
  languageName: node
  linkType: hard

"deep-extend@npm:^0.6.0":
  version: 0.6.0
  resolution: "deep-extend@npm:0.6.0"
  checksum: 10c0/1c6b0abcdb901e13a44c7d699116d3d4279fdb261983122a3783e7273844d5f2537dc2e1c454a23fcf645917f93fbf8d07101c1d03c015a87faa662755212566
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10c0/7f0ee496e0dff14a573dc6127f14c95061b448b87b995fc96c017ce0a1e66af1675e73f1d6064407975bc4ea6ab679497a29fff7b5b9c4e99cb10797c1ad0b4c
  languageName: node
  linkType: hard

"default-browser-id@npm:^3.0.0":
  version: 3.0.0
  resolution: "default-browser-id@npm:3.0.0"
  dependencies:
    bplist-parser: "npm:^0.2.0"
    untildify: "npm:^4.0.0"
  checksum: 10c0/8db3ab882eb3e1e8b59d84c8641320e6c66d8eeb17eb4bb848b7dd549b1e6fd313988e4a13542e95fbaeff03f6e9dedc5ad191ad4df7996187753eb0d45c00b7
  languageName: node
  linkType: hard

"default-browser@npm:^4.0.0":
  version: 4.0.0
  resolution: "default-browser@npm:4.0.0"
  dependencies:
    bundle-name: "npm:^3.0.0"
    default-browser-id: "npm:^3.0.0"
    execa: "npm:^7.1.1"
    titleize: "npm:^3.0.0"
  checksum: 10c0/7c8848badc139ecf9d878e562bc4e7ab4301e51ba120b24d8dcb14739c30152115cc612065ac3ab73c02aace4afa29db5a044257b2f0cf234f16e3a58f6c925e
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: "npm:^1.0.2"
  checksum: 10c0/9cfbe498f5c8ed733775db62dfd585780387d93c17477949e1670bfcfb9346e0281ce8c4bf9f4ac1fc0f9b851113bd6dc9e41182ea1644ccd97de639fa13c35a
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "define-lazy-prop@npm:2.0.0"
  checksum: 10c0/db6c63864a9d3b7dc9def55d52764968a5af296de87c1b2cc71d8be8142e445208071953649e0386a8cc37cfcf9a2067a47207f1eb9ff250c2a269658fdae422
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^3.0.0":
  version: 3.0.0
  resolution: "define-lazy-prop@npm:3.0.0"
  checksum: 10c0/5ab0b2bf3fa58b3a443140bbd4cd3db1f91b985cc8a246d330b9ac3fc0b6a325a6d82bddc0b055123d745b3f9931afeea74a5ec545439a1630b9c8512b0eeb49
  languageName: node
  linkType: hard

"define-property@npm:^0.2.5":
  version: 0.2.5
  resolution: "define-property@npm:0.2.5"
  dependencies:
    is-descriptor: "npm:^0.1.0"
  checksum: 10c0/9986915c0893818dedc9ca23eaf41370667762fd83ad8aa4bf026a28563120dbaacebdfbfbf2b18d3b929026b9c6ee972df1dbf22de8fafb5fe6ef18361e4750
  languageName: node
  linkType: hard

"define-property@npm:^1.0.0":
  version: 1.0.0
  resolution: "define-property@npm:1.0.0"
  dependencies:
    is-descriptor: "npm:^1.0.0"
  checksum: 10c0/d7cf09db10d55df305f541694ed51dafc776ad9bb8a24428899c9f2d36b11ab38dce5527a81458d1b5e7c389f8cbe803b4abad6e91a0037a329d153b84fc975e
  languageName: node
  linkType: hard

"define-property@npm:^2.0.2":
  version: 2.0.2
  resolution: "define-property@npm:2.0.2"
  dependencies:
    is-descriptor: "npm:^1.0.2"
    isobject: "npm:^3.0.1"
  checksum: 10c0/f91a08ad008fa764172a2c072adc7312f10217ade89ddaea23018321c6d71b2b68b8c229141ed2064179404e345c537f1a2457c379824813695b51a6ad3e4969
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10c0/d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"delegates@npm:^1.0.0":
  version: 1.0.0
  resolution: "delegates@npm:1.0.0"
  checksum: 10c0/ba05874b91148e1db4bf254750c042bf2215febd23a6d3cda2e64896aef79745fbd4b9996488bd3cafb39ce19dbce0fd6e3b6665275638befffe1c9b312b91b5
  languageName: node
  linkType: hard

"denque@npm:^2.1.0":
  version: 2.1.0
  resolution: "denque@npm:2.1.0"
  checksum: 10c0/f9ef81aa0af9c6c614a727cb3bd13c5d7db2af1abf9e6352045b86e85873e629690f6222f4edd49d10e4ccf8f078bbeec0794fafaf61b659c0589d0c511ec363
  languageName: node
  linkType: hard

"depd@npm:2.0.0, depd@npm:^2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 10c0/58bd06ec20e19529b06f7ad07ddab60e504d9e0faca4bd23079fac2d279c3594334d736508dc350e06e510aba5e22e4594483b3a6562ce7c17dd797f4cc4ad2c
  languageName: node
  linkType: hard

"depd@npm:~1.1.2":
  version: 1.1.2
  resolution: "depd@npm:1.1.2"
  checksum: 10c0/acb24aaf936ef9a227b6be6d495f0d2eb20108a9a6ad40585c5bda1a897031512fef6484e4fdbb80bd249fdaa82841fa1039f416ece03188e677ba11bcfda249
  languageName: node
  linkType: hard

"destroy@npm:1.2.0, destroy@npm:^1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 10c0/bd7633942f57418f5a3b80d5cb53898127bcf53e24cdf5d5f4396be471417671f0fee48a4ebe9a1e9defbde2a31280011af58a57e090ff822f589b443ed4e643
  languageName: node
  linkType: hard

"destroy@npm:~1.0.4":
  version: 1.0.4
  resolution: "destroy@npm:1.0.4"
  checksum: 10c0/eab493808ba17a1fa22c71ef1a4e68d2c4c5222a38040606c966d2ab09117f3a7f3e05c39bffbe41a697f9de552039e43c30e46f0c3eab3faa9f82e800e172a0
  languageName: node
  linkType: hard

"detect-indent@npm:^6.0.0":
  version: 6.1.0
  resolution: "detect-indent@npm:6.1.0"
  checksum: 10c0/dd83cdeda9af219cf77f5e9a0dc31d828c045337386cfb55ce04fad94ba872ee7957336834154f7647b89b899c3c7acc977c57a79b7c776b506240993f97acc7
  languageName: node
  linkType: hard

"dezalgo@npm:^1.0.4":
  version: 1.0.4
  resolution: "dezalgo@npm:1.0.4"
  dependencies:
    asap: "npm:^2.0.0"
    wrappy: "npm:1"
  checksum: 10c0/8a870ed42eade9a397e6141fe5c025148a59ed52f1f28b1db5de216b4d57f0af7a257070c3af7ce3d5508c1ce9dd5009028a76f4b2cc9370dc56551d2355fad8
  languageName: node
  linkType: hard

"diff-sequences@npm:^25.2.6":
  version: 25.2.6
  resolution: "diff-sequences@npm:25.2.6"
  checksum: 10c0/7760d381e49a244d07a575a4ad89df8c044a4a2f11b5a88846e5c0a0a92cab593f77a8c8dd8223a879172040bbf2bc375572ee62f819fd440991a71487340574
  languageName: node
  linkType: hard

"diff-sequences@npm:^29.4.3":
  version: 29.4.3
  resolution: "diff-sequences@npm:29.4.3"
  checksum: 10c0/183800b9fd8523a05a3a50ade0fafe81d4b8a8ac113b077d2bc298052ccdc081e3b896f19bf65768b536daebd8169a493c4764cb70a2195e14c442c12538d121
  languageName: node
  linkType: hard

"dir-glob@npm:^2.2.2":
  version: 2.2.2
  resolution: "dir-glob@npm:2.2.2"
  dependencies:
    path-type: "npm:^3.0.0"
  checksum: 10c0/67575fd496df80ec90969f1a9f881f03b4ef614ca2c07139df81a12f9816250780dff906f482def0f897dd748d22fa13c076b52ac635e0024f7d434846077a3a
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: "npm:^4.0.0"
  checksum: 10c0/dcac00920a4d503e38bb64001acb19df4efc14536ada475725e12f52c16777afdee4db827f55f13a908ee7efc0cb282e2e3dbaeeb98c0993dd93d1802d3bf00c
  languageName: node
  linkType: hard

"doctrine@npm:^3.0.0":
  version: 3.0.0
  resolution: "doctrine@npm:3.0.0"
  dependencies:
    esutils: "npm:^2.0.2"
  checksum: 10c0/c96bdccabe9d62ab6fea9399fdff04a66e6563c1d6fb3a3a063e8d53c3bb136ba63e84250bbf63d00086a769ad53aef92d2bd483f03f837fc97b71cbee6b2520
  languageName: node
  linkType: hard

"dot-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "dot-case@npm:3.0.4"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10c0/5b859ea65097a7ea870e2c91b5768b72ddf7fa947223fd29e167bcdff58fe731d941c48e47a38ec8aa8e43044c8fbd15cd8fa21689a526bc34b6548197cd5b05
  languageName: node
  linkType: hard

"dotenv@npm:^16.0.0":
  version: 16.3.1
  resolution: "dotenv@npm:16.3.1"
  checksum: 10c0/b95ff1bbe624ead85a3cd70dbd827e8e06d5f05f716f2d0cbc476532d54c7c9469c3bc4dd93ea519f6ad711cb522c00ac9a62b6eb340d5affae8008facc3fbd7
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"editorconfig@npm:^0.15.3":
  version: 0.15.3
  resolution: "editorconfig@npm:0.15.3"
  dependencies:
    commander: "npm:^2.19.0"
    lru-cache: "npm:^4.1.5"
    semver: "npm:^5.6.0"
    sigmund: "npm:^1.0.1"
  bin:
    editorconfig: bin/editorconfig
  checksum: 10c0/801f433299a7500f15ed770d2dc9e5b763f71c1eda61c4e9a1222d3bab1be7d591632dfe9698872df845ccfa97bba394bcbf074a2ad367d1c0377a59abe0c00e
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 10c0/b5bb125ee93161bc16bfe6e56c6b04de5ad2aa44234d8f644813cc95d861a6910903132b05093706de2b706599367c4130eb6d170f6b46895686b95f87d017b7
  languageName: node
  linkType: hard

"ejs@npm:3.1.6":
  version: 3.1.6
  resolution: "ejs@npm:3.1.6"
  dependencies:
    jake: "npm:^10.6.1"
  bin:
    ejs: ./bin/cli.js
  checksum: 10c0/0af61e5ccf436d9d6517abe08ac2b8ad18cf0934e885e2d9b788eb7856da7eebdf84f218ebe644310fe93bac6411f520528d225932f2f010dc4ead2bbef1be34
  languageName: node
  linkType: hard

"emittery@npm:^0.10.0, emittery@npm:^0.10.2":
  version: 0.10.2
  resolution: "emittery@npm:0.10.2"
  checksum: 10c0/2caeea7501a0cca9b0e9d8d0a84d7d059cd2319ab02016bb6f81ae8bc2f3353c6734ed50a5fe0e4e2b96ebcc1623c1344b6beec51a4feda34b121942dd50ba55
  languageName: node
  linkType: hard

"emittery@npm:^0.13.1":
  version: 0.13.1
  resolution: "emittery@npm:0.13.1"
  checksum: 10c0/1573d0ae29ab34661b6c63251ff8f5facd24ccf6a823f19417ae8ba8c88ea450325788c67f16c99edec8de4b52ce93a10fe441ece389fd156e88ee7dab9bfa35
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"encodeurl@npm:^1.0.2, encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: 10c0/f6c2387379a9e7c1156c1c3d4f9cb7bb11cf16dd4c1682e1f6746512564b053df5781029b6061296832b59fb22f459dbe250386d217c2f6e203601abb2ee0bec
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: "npm:^1.4.0"
  checksum: 10c0/870b423afb2d54bb8d243c63e07c170409d41e20b47eeef0727547aea5740bd6717aca45597a9f2745525667a6b804c1e7bede41f856818faee5806dd9ff3975
  languageName: node
  linkType: hard

"engine.io-client@npm:~1.8.4":
  version: 1.8.6
  resolution: "engine.io-client@npm:1.8.6"
  dependencies:
    component-emitter: "npm:1.2.1"
    component-inherit: "npm:0.0.3"
    debug: "npm:2.3.3"
    engine.io-parser: "npm:1.3.2"
    has-cors: "npm:1.1.0"
    indexof: "npm:0.0.1"
    parsejson: "npm:0.0.3"
    parseqs: "npm:0.0.5"
    parseuri: "npm:0.0.5"
    ws: "npm:~1.1.5"
    xmlhttprequest-ssl: "npm:1.6.3"
    yeast: "npm:0.1.2"
  checksum: 10c0/1f4c85d470d9550e392d7583e642aff5dece4ce8417d7538cb92180cef1c25b8c083b39e291725568b4272a3e6a4c99c2a62168a9315ba35ace02f1fa22bad89
  languageName: node
  linkType: hard

"engine.io-parser@npm:1.3.2":
  version: 1.3.2
  resolution: "engine.io-parser@npm:1.3.2"
  dependencies:
    after: "npm:0.8.2"
    arraybuffer.slice: "npm:0.0.6"
    base64-arraybuffer: "npm:0.1.5"
    blob: "npm:0.0.4"
    has-binary: "npm:0.1.7"
    wtf-8: "npm:1.0.0"
  checksum: 10c0/fc476ef883d31208ed81aeb2d060a28a9401466ad08295c8680dad68e1bdc9b4e0703fd60ec354f05e50fa047d9cdd3777c633679425c53225283c7310b73dc2
  languageName: node
  linkType: hard

"enquirer@npm:^2.3.6":
  version: 2.3.6
  resolution: "enquirer@npm:2.3.6"
  dependencies:
    ansi-colors: "npm:^4.1.1"
  checksum: 10c0/8e070e052c2c64326a2803db9084d21c8aaa8c688327f133bf65c4a712586beb126fd98c8a01cfb0433e82a4bd3b6262705c55a63e0f7fb91d06b9cedbde9a11
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"es-module-lexer@npm:0.3.26":
  version: 0.3.26
  resolution: "es-module-lexer@npm:0.3.26"
  checksum: 10c0/899867529a4b7e1d1880122635bdcd2768bcb5a02b85ea9e6857f30d5f8fcdc6aa54787ad61eb323e8865bf6d4e1f237b96e90892c4c2d4d67f1138bbbbf238b
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.1.1
  resolution: "escalade@npm:3.1.1"
  checksum: 10c0/afd02e6ca91ffa813e1108b5e7756566173d6bc0d1eb951cb44d6b21702ec17c1cf116cfe75d4a2b02e05acb0b808a7a9387d0d1ca5cf9c04ad03a8445c3e46d
  languageName: node
  linkType: hard

"escape-goat@npm:^2.0.0":
  version: 2.1.1
  resolution: "escape-goat@npm:2.1.1"
  checksum: 10c0/fc0ad656f89c05e86a9641a21bdc5ea37b258714c057430b68a834854fa3e5770cda7d41756108863fc68b1e36a0946463017b7553ac39eaaf64815be07816fc
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 10c0/524c739d776b36c3d29fa08a22e03e8824e3b2fd57500e5e44ecf3cc4707c34c60f9ca0781c0e33d191f2991161504c295e98f68c78fe7baa6e57081ec6ac0a3
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.2":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 10c0/a968ad453dd0c2724e14a4f20e177aaf32bb384ab41b674a8454afe9a41c5e6fe8903323e0a1052f56289d04bd600f81278edf140b0fcc02f5cac98d0f5b5371
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10c0/9497d4dd307d845bd7f75180d8188bb17ea8c151c1edbf6b6717c100e104d629dc2dfb687686181b0f4b7d732c7dfdc4d5e7a8ff72de1b0ca283a75bbb3a9cd9
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^8.8.0":
  version: 8.8.0
  resolution: "eslint-config-prettier@npm:8.8.0"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 10c0/9e3bb602184b7ec59239d2f901b1594cd7cc59ff38c3ddcd812137817e50840f4d65d62b61c515c7eae86d85f8b6fb2ebda659a3f83b2f2c5da75feb15531508
  languageName: node
  linkType: hard

"eslint-plugin-adonis@npm:^2.1.1":
  version: 2.1.1
  resolution: "eslint-plugin-adonis@npm:2.1.1"
  dependencies:
    "@typescript-eslint/eslint-plugin": "npm:^5.37.0"
    "@typescript-eslint/parser": "npm:^5.37.0"
  peerDependencies:
    eslint: ^8.0.0
  checksum: 10c0/4caa7ed4232d17d3923f8487b01ac21f6b81fe5394398a0ef3d347592907986b1aa9f857ca187ac0c7c06b28042192f811a59f8c6fffd255196ef654fa3efcb5
  languageName: node
  linkType: hard

"eslint-plugin-prettier@npm:^5.0.0":
  version: 5.0.0
  resolution: "eslint-plugin-prettier@npm:5.0.0"
  dependencies:
    prettier-linter-helpers: "npm:^1.0.0"
    synckit: "npm:^0.8.5"
  peerDependencies:
    "@types/eslint": ">=8.0.0"
    eslint: ">=8.0.0"
    prettier: ">=3.0.0"
  peerDependenciesMeta:
    "@types/eslint":
      optional: true
    eslint-config-prettier:
      optional: true
  checksum: 10c0/7a3641196fc7305c11ed53faae4b69e82bd1d58ba3894d1ac77d68af7ea91c9e2b1fd50e2645f5dc2831bbcb866bb3897a0710c955c0a7fc9918ef4fb7687b97
  languageName: node
  linkType: hard

"eslint-scope@npm:^5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^4.1.1"
  checksum: 10c0/d30ef9dc1c1cbdece34db1539a4933fe3f9b14e1ffb27ecc85987902ee663ad7c9473bbd49a9a03195a373741e62e2f807c4938992e019b511993d163450e70a
  languageName: node
  linkType: hard

"eslint-scope@npm:^7.2.0":
  version: 7.2.1
  resolution: "eslint-scope@npm:7.2.1"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10c0/7207497acab2be257979d43403368fb07d3172227d576e04f5906218d76ed7ee99e7116ca71c31b4e00ecc7bb0a00efd98b338c74aa9eec7b7dea7010f9e6da8
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.3.0, eslint-visitor-keys@npm:^3.4.1":
  version: 3.4.1
  resolution: "eslint-visitor-keys@npm:3.4.1"
  checksum: 10c0/b4ebd35aed5426cd81b1fb92487825f1acf47a31e91d76597a3ee0664d69627140c4dafaf9b319cfeb1f48c1113a393e21a734c669e6565a72e6fcc311bd9911
  languageName: node
  linkType: hard

"eslint@npm:^8.45.0":
  version: 8.45.0
  resolution: "eslint@npm:8.45.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.2.0"
    "@eslint-community/regexpp": "npm:^4.4.0"
    "@eslint/eslintrc": "npm:^2.1.0"
    "@eslint/js": "npm:8.44.0"
    "@humanwhocodes/config-array": "npm:^0.11.10"
    "@humanwhocodes/module-importer": "npm:^1.0.1"
    "@nodelib/fs.walk": "npm:^1.2.8"
    ajv: "npm:^6.10.0"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.2"
    debug: "npm:^4.3.2"
    doctrine: "npm:^3.0.0"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^7.2.0"
    eslint-visitor-keys: "npm:^3.4.1"
    espree: "npm:^9.6.0"
    esquery: "npm:^1.4.2"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^6.0.1"
    find-up: "npm:^5.0.0"
    glob-parent: "npm:^6.0.2"
    globals: "npm:^13.19.0"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^5.2.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    is-path-inside: "npm:^3.0.3"
    js-yaml: "npm:^4.1.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    levn: "npm:^0.4.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.1.2"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.3"
    strip-ansi: "npm:^6.0.1"
    text-table: "npm:^0.2.0"
  bin:
    eslint: bin/eslint.js
  checksum: 10c0/2a043b8d3b9a5684e2f66bd446c3dc8522cc7afbb0982d0a5be76ea1f578d0e617598a7b289616a861ab8272b57f6056acb2b264bec6302c9b0921a1cfa66fdb
  languageName: node
  linkType: hard

"esm@npm:^3.2.25":
  version: 3.2.25
  resolution: "esm@npm:3.2.25"
  checksum: 10c0/8e60e8075506a7ce28681c30c8f54623fe18a251c364cd481d86719fc77f58aa055b293d80632d9686d5408aaf865ffa434897dc9fd9153c8b3f469fad23f094
  languageName: node
  linkType: hard

"espree@npm:^9.6.0":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: "npm:^8.9.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^3.4.1"
  checksum: 10c0/1a2e9b4699b715347f62330bcc76aee224390c28bb02b31a3752e9d07549c473f5f986720483c6469cf3cfb3c9d05df612ffc69eb1ee94b54b739e67de9bb460
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0, esprima@npm:^4.0.1, esprima@npm:~4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 10c0/ad4bab9ead0808cf56501750fd9d3fb276f6b105f987707d059005d57e182d18a7c9ec7f3a01794ebddcca676773e42ca48a32d67a250c9d35e009ca613caba3
  languageName: node
  linkType: hard

"esquery@npm:^1.4.2":
  version: 1.5.0
  resolution: "esquery@npm:1.5.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: 10c0/a084bd049d954cc88ac69df30534043fb2aee5555b56246493f42f27d1e168f00d9e5d4192e46f10290d312dc30dc7d58994d61a609c579c1219d636996f9213
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10c0/81a37116d1408ded88ada45b9fb16dbd26fba3aadc369ce50fcaf82a0bac12772ebd7b24cd7b91fc66786bf2c1ac7b5f196bc990a473efff972f5cb338877cf5
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: 10c0/9cb46463ef8a8a4905d3708a652d60122a0c20bb58dec7e0e12ab0e7235123d74214fc0141d743c381813e1b992767e2708194f6f6e0f9fd00c1b4e0887b8b6d
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10c0/1ff9447b96263dec95d6d67431c5e0771eb9776427421260a3e2f0fdd5d6bd4f8e37a7338f5ad2880c9f143450c9b1e4fc2069060724570a49cf9cf0312bd107
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10c0/9a2fe69a41bfdade834ba7c42de4723c97ec776e40656919c62cbd13607c45e127a003f05f724a1ea55e5029a4cf2de444b13009f2af71271e42d93a637137c7
  languageName: node
  linkType: hard

"etag@npm:^1.8.1, etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 10c0/12be11ef62fb9817314d790089a0a49fae4e1b50594135dcb8076312b7d7e470884b5100d249b28c18581b7fd52f8b485689ffae22a11ed9ec17377a33a08f84
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 10c0/0255d9f936215fd206156fd4caa9e8d35e62075d720dc7d847e89b417e5e62cf1ce6c9b4e0a1633a9256de0efefaf9f8d26924b1f3c8620cffb9db78e7d3076b
  languageName: node
  linkType: hard

"events@npm:^3.3.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: 10c0/d6b6f2adbccbcda74ddbab52ed07db727ef52e31a61ed26db9feb7dc62af7fc8e060defa65e5f8af9449b86b52cc1a1f6a79f2eafcf4e62add2b7a1fa4a432f6
  languageName: node
  linkType: hard

"execa@npm:^5.0.0, execa@npm:^5.1.1":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    get-stream: "npm:^6.0.0"
    human-signals: "npm:^2.1.0"
    is-stream: "npm:^2.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^4.0.1"
    onetime: "npm:^5.1.2"
    signal-exit: "npm:^3.0.3"
    strip-final-newline: "npm:^2.0.0"
  checksum: 10c0/c8e615235e8de4c5addf2fa4c3da3e3aa59ce975a3e83533b4f6a71750fb816a2e79610dc5f1799b6e28976c9ae86747a36a606655bf8cb414a74d8d507b304f
  languageName: node
  linkType: hard

"execa@npm:^7.1.1":
  version: 7.1.1
  resolution: "execa@npm:7.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    get-stream: "npm:^6.0.1"
    human-signals: "npm:^4.3.0"
    is-stream: "npm:^3.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^5.1.0"
    onetime: "npm:^6.0.0"
    signal-exit: "npm:^3.0.7"
    strip-final-newline: "npm:^3.0.0"
  checksum: 10c0/0da5ee1c895b62142bc3d1567d1974711c28c2cfa6bae96e1923379bd597e476d762a13f282f92815d8ebfa33407949634fa32a0d6db8334a20e625fe11d4351
  languageName: node
  linkType: hard

"expand-brackets@npm:^2.1.4":
  version: 2.1.4
  resolution: "expand-brackets@npm:2.1.4"
  dependencies:
    debug: "npm:^2.3.3"
    define-property: "npm:^0.2.5"
    extend-shallow: "npm:^2.0.1"
    posix-character-classes: "npm:^0.1.0"
    regex-not: "npm:^1.0.0"
    snapdragon: "npm:^0.8.1"
    to-regex: "npm:^3.0.1"
  checksum: 10c0/3e2fb95d2d7d7231486493fd65db913927b656b6fcdfcce41e139c0991a72204af619ad4acb1be75ed994ca49edb7995ef241dbf8cf44dc3c03d211328428a87
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 10c0/160456d2d647e6019640bd07111634d8c353038d9fa40176afb7cd49b0548bdae83b56d05e907c2cce2300b81cae35d800ef92fefb9d0208e190fa3b7d6bb579
  languageName: node
  linkType: hard

"express@npm:4.17.1":
  version: 4.17.1
  resolution: "express@npm:4.17.1"
  dependencies:
    accepts: "npm:~1.3.7"
    array-flatten: "npm:1.1.1"
    body-parser: "npm:1.19.0"
    content-disposition: "npm:0.5.3"
    content-type: "npm:~1.0.4"
    cookie: "npm:0.4.0"
    cookie-signature: "npm:1.0.6"
    debug: "npm:2.6.9"
    depd: "npm:~1.1.2"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    finalhandler: "npm:~1.1.2"
    fresh: "npm:0.5.2"
    merge-descriptors: "npm:1.0.1"
    methods: "npm:~1.1.2"
    on-finished: "npm:~2.3.0"
    parseurl: "npm:~1.3.3"
    path-to-regexp: "npm:0.1.7"
    proxy-addr: "npm:~2.0.5"
    qs: "npm:6.7.0"
    range-parser: "npm:~1.2.1"
    safe-buffer: "npm:5.1.2"
    send: "npm:0.17.1"
    serve-static: "npm:1.14.1"
    setprototypeof: "npm:1.1.1"
    statuses: "npm:~1.5.0"
    type-is: "npm:~1.6.18"
    utils-merge: "npm:1.0.1"
    vary: "npm:~1.1.2"
  checksum: 10c0/17bbe941cb98167d54d24f1b1f252e9e1757ad036b0ba7a836c51d3f1a7bf329ccbf72739d214599818ccec91115b7c5b87ad2d2a006e20142310af4d7c6f7bf
  languageName: node
  linkType: hard

"extend-shallow@npm:^2.0.1":
  version: 2.0.1
  resolution: "extend-shallow@npm:2.0.1"
  dependencies:
    is-extendable: "npm:^0.1.0"
  checksum: 10c0/ee1cb0a18c9faddb42d791b2d64867bd6cfd0f3affb711782eb6e894dd193e2934a7f529426aac7c8ddb31ac5d38000a00aa2caf08aa3dfc3e1c8ff6ba340bd9
  languageName: node
  linkType: hard

"extend-shallow@npm:^3.0.0, extend-shallow@npm:^3.0.2":
  version: 3.0.2
  resolution: "extend-shallow@npm:3.0.2"
  dependencies:
    assign-symbols: "npm:^1.0.0"
    is-extendable: "npm:^1.0.1"
  checksum: 10c0/f39581b8f98e3ad94995e33214fff725b0297cf09f2725b6f624551cfb71e0764accfd0af80becc0182af5014d2a57b31b85ec999f9eb8a6c45af81752feac9a
  languageName: node
  linkType: hard

"extend@npm:^3.0.0":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: 10c0/73bf6e27406e80aa3e85b0d1c4fd987261e628064e170ca781125c0b635a3dabad5e05adbf07595ea0cf1e6c5396cacb214af933da7cbaf24fe75ff14818e8f9
  languageName: node
  linkType: hard

"extglob@npm:^2.0.4":
  version: 2.0.4
  resolution: "extglob@npm:2.0.4"
  dependencies:
    array-unique: "npm:^0.3.2"
    define-property: "npm:^1.0.0"
    expand-brackets: "npm:^2.1.4"
    extend-shallow: "npm:^2.0.1"
    fragment-cache: "npm:^0.2.1"
    regex-not: "npm:^1.0.0"
    snapdragon: "npm:^0.8.1"
    to-regex: "npm:^3.0.1"
  checksum: 10c0/e1a891342e2010d046143016c6c03d58455c2c96c30bf5570ea07929984ee7d48fad86b363aee08f7a8a638f5c3a66906429b21ecb19bc8e90df56a001cd282c
  languageName: node
  linkType: hard

"fast-copy@npm:^3.0.0":
  version: 3.0.1
  resolution: "fast-copy@npm:3.0.1"
  checksum: 10c0/a8310dbcc4c94ed001dc3e0bbc3c3f0491bb04e6c17163abe441a54997ba06cdf1eb532c2f05e54777c6f072c84548c23ef0ecd54665cd611be1d42f37eca258
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10c0/40dedc862eb8992c54579c66d914635afbec43350afbbe991235fdcb4e3a8d5af1b23ae7e79bef7d4882d0ecee06c3197488026998fb19f72dc95acff1d1b1d0
  languageName: node
  linkType: hard

"fast-diff@npm:^1.1.2":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: 10c0/5c19af237edb5d5effda008c891a18a585f74bf12953be57923f17a3a4d0979565fc64dbc73b9e20926b9d895f5b690c618cbb969af0cf022e3222471220ad29
  languageName: node
  linkType: hard

"fast-glob@npm:^2.2.6":
  version: 2.2.7
  resolution: "fast-glob@npm:2.2.7"
  dependencies:
    "@mrmlnc/readdir-enhanced": "npm:^2.2.1"
    "@nodelib/fs.stat": "npm:^1.1.2"
    glob-parent: "npm:^3.1.0"
    is-glob: "npm:^4.0.0"
    merge2: "npm:^1.2.3"
    micromatch: "npm:^3.1.10"
  checksum: 10c0/85bc858e298423d5a1b6eed6eee8556005a19d245c4ae9aceac04d56699ea9885ca0a2afc4f76b562416e94fe2048df6b2f306f3d4b7e51ed37b7a52fc1e4fc7
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.12, fast-glob@npm:^3.2.9, fast-glob@npm:^3.3.0":
  version: 3.3.1
  resolution: "fast-glob@npm:3.3.1"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.4"
  checksum: 10c0/b68431128fb6ce4b804c5f9622628426d990b66c75b21c0d16e3d80e2d1398bf33f7e1724e66a2e3f299285dcf5b8d745b122d0304e7dd66f5231081f33ec67c
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10c0/7f081eb0b8a64e0057b3bb03f974b3ef00135fbf36c1c710895cd9300f13c94ba809bb3a81cf4e1b03f6e5285610a61abbd7602d0652de423144dfee5a389c9b
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 10c0/111972b37338bcb88f7d9e2c5907862c280ebf4234433b95bc611e518d192ccb2d38119c4ac86e26b668d75f7f3894f4ff5c4982899afced7ca78633b08287c4
  languageName: node
  linkType: hard

"fast-redact@npm:^3.0.0, fast-redact@npm:^3.1.1":
  version: 3.2.0
  resolution: "fast-redact@npm:3.2.0"
  checksum: 10c0/a258a178ff8d2f760985be774e589a65df5a573ec12ebc1c72e090950aa860a2a6efb01f673d1be015e9f1170446903cd1ff402e8658b4a84635308dfcef164a
  languageName: node
  linkType: hard

"fast-safe-stringify@npm:^2.0.8, fast-safe-stringify@npm:^2.1.1":
  version: 2.1.1
  resolution: "fast-safe-stringify@npm:2.1.1"
  checksum: 10c0/d90ec1c963394919828872f21edaa3ad6f1dddd288d2bd4e977027afff09f5db40f94e39536d4646f7e01761d704d72d51dce5af1b93717f3489ef808f5f4e4d
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.15.0
  resolution: "fastq@npm:1.15.0"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10c0/5ce4f83afa5f88c9379e67906b4d31bc7694a30826d6cc8d0f0473c966929017fda65c2174b0ec89f064ede6ace6c67f8a4fe04cef42119b6a55b0d465554c24
  languageName: node
  linkType: hard

"file-entry-cache@npm:^6.0.1":
  version: 6.0.1
  resolution: "file-entry-cache@npm:6.0.1"
  dependencies:
    flat-cache: "npm:^3.0.4"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"file-type@npm:^16.5.4":
  version: 16.5.4
  resolution: "file-type@npm:16.5.4"
  dependencies:
    readable-web-to-node-stream: "npm:^3.0.0"
    strtok3: "npm:^6.2.4"
    token-types: "npm:^4.1.1"
  checksum: 10c0/a6c9ab8bc05bc9c212bec239fb0d5bf59ddc9b3912f00c4ef44622e67ae4e553a1cc8372e9e595e14859035188eb305d05d488fa3c5c2a2ad90bb7745b3004ef
  languageName: node
  linkType: hard

"filelist@npm:^1.0.4":
  version: 1.0.4
  resolution: "filelist@npm:1.0.4"
  dependencies:
    minimatch: "npm:^5.0.1"
  checksum: 10c0/426b1de3944a3d153b053f1c0ebfd02dccd0308a4f9e832ad220707a6d1f1b3c9784d6cadf6b2f68f09a57565f63ebc7bcdc913ccf8012d834f472c46e596f41
  languageName: node
  linkType: hard

"fill-range@npm:^4.0.0":
  version: 4.0.0
  resolution: "fill-range@npm:4.0.0"
  dependencies:
    extend-shallow: "npm:^2.0.1"
    is-number: "npm:^3.0.0"
    repeat-string: "npm:^1.6.1"
    to-regex-range: "npm:^2.1.0"
  checksum: 10c0/ccd57b7c43d7e28a1f8a60adfa3c401629c08e2f121565eece95e2386ebc64dedc7128d8c3448342aabf19db0c55a34f425f148400c7a7be9a606ba48749e089
  languageName: node
  linkType: hard

"fill-range@npm:^7.0.1":
  version: 7.0.1
  resolution: "fill-range@npm:7.0.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10c0/7cdad7d426ffbaadf45aeb5d15ec675bbd77f7597ad5399e3d2766987ed20bda24d5fac64b3ee79d93276f5865608bb22344a26b9b1ae6c4d00bd94bf611623f
  languageName: node
  linkType: hard

"finalhandler@npm:~1.1.2":
  version: 1.1.2
  resolution: "finalhandler@npm:1.1.2"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:~2.3.0"
    parseurl: "npm:~1.3.3"
    statuses: "npm:~1.5.0"
    unpipe: "npm:~1.0.0"
  checksum: 10c0/6a96e1f5caab085628c11d9fdceb82ba608d5e426c6913d4d918409baa271037a47f28fbba73279e8ad614f0b8fa71ea791d265e408d760793829edd8c2f4584
  languageName: node
  linkType: hard

"find-cache-dir@npm:^3.3.2":
  version: 3.3.2
  resolution: "find-cache-dir@npm:3.3.2"
  dependencies:
    commondir: "npm:^1.0.1"
    make-dir: "npm:^3.0.2"
    pkg-dir: "npm:^4.1.0"
  checksum: 10c0/92747cda42bff47a0266b06014610981cfbb71f55d60f2c8216bc3108c83d9745507fb0b14ecf6ab71112bed29cd6fb1a137ee7436179ea36e11287e3159e587
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0, find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: "npm:^5.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/0406ee89ebeefa2d507feb07ec366bebd8a6167ae74aa4e34fb4c4abd06cf782a3ce26ae4194d70706f72182841733f00551c209fe575cb00bd92104056e78c1
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: "npm:^6.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/062c5a83a9c02f53cdd6d175a37ecf8f87ea5bbff1fdfb828f04bfa021441bc7583e8ebc0872a4c1baab96221fb8a8a275a19809fb93fbc40bd69ec35634069a
  languageName: node
  linkType: hard

"flat-cache@npm:^3.0.4":
  version: 3.0.4
  resolution: "flat-cache@npm:3.0.4"
  dependencies:
    flatted: "npm:^3.1.0"
    rimraf: "npm:^3.0.2"
  checksum: 10c0/f274dcbadb09ad8d7b6edf2ee9b034bc40bf0c12638f6c4084e9f1d39208cb104a5ebbb24b398880ef048200eaa116852f73d2d8b72e8c9627aba8c3e27ca057
  languageName: node
  linkType: hard

"flatstr@npm:^1.0.12":
  version: 1.0.12
  resolution: "flatstr@npm:1.0.12"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"flatted@npm:^3.1.0":
  version: 3.2.7
  resolution: "flatted@npm:3.2.7"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"flattie@npm:^1.1.0":
  version: 1.1.0
  resolution: "flattie@npm:1.1.0"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.15.0":
  version: 1.15.2
  resolution: "follow-redirects@npm:1.15.2"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10c0/da5932b70e63944d38eecaa16954bac4347036f08303c913d166eda74809d8797d38386e3a0eb1d2fe37d2aaff2764cce8e9dbd99459d860cf2cdfa237923b5f
  languageName: node
  linkType: hard

"for-in@npm:^1.0.2":
  version: 1.0.2
  resolution: "for-in@npm:1.0.2"
  checksum: 10c0/42bb609d564b1dc340e1996868b67961257fd03a48d7fdafd4f5119530b87f962be6b4d5b7e3a3fc84c9854d149494b1d358e0b0ce9837e64c4c6603a49451d6
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.1.1
  resolution: "foreground-child@npm:3.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.0"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/9700a0285628abaeb37007c9a4d92bd49f67210f09067638774338e146c8e9c825c5c877f072b2f75f41dc6a2d0be8664f79ffc03f6576649f54a84fb9b47de0
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.0
  resolution: "form-data@npm:4.0.0"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    mime-types: "npm:^2.1.12"
  checksum: 10c0/cb6f3ac49180be03ff07ba3ff125f9eba2ff0b277fb33c7fc47569fc5e616882c5b1c69b9904c4c4187e97dd0419dd03b134174756f296dec62041e6527e2c6e
  languageName: node
  linkType: hard

"formidable@npm:^2.1.2":
  version: 2.1.2
  resolution: "formidable@npm:2.1.2"
  dependencies:
    dezalgo: "npm:^1.0.4"
    hexoid: "npm:^1.0.0"
    once: "npm:^1.4.0"
    qs: "npm:^6.11.0"
  checksum: 10c0/efba03d11127098daa6ef54c3c0fad25693973eb902fa88ccaaa203baebe8c74d12ba0fe1e113eccf79b9172510fa337e4e107330b124fb3a8c74697b4aa2ce3
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: 10c0/9b67c3fac86acdbc9ae47ba1ddd5f2f81526fa4c8226863ede5600a3f7c7416ef451f6f1e240a3cc32d0fd79fcfe6beb08fd0da454f360032bde70bf80afbb33
  languageName: node
  linkType: hard

"fragment-cache@npm:^0.2.1":
  version: 0.2.1
  resolution: "fragment-cache@npm:0.2.1"
  dependencies:
    map-cache: "npm:^0.2.2"
  checksum: 10c0/5891d1c1d1d5e1a7fb3ccf28515c06731476fa88f7a50f4ede8a0d8d239a338448e7f7cc8b73db48da19c229fa30066104fe6489862065a4f1ed591c42fbeabf
  languageName: node
  linkType: hard

"fresh@npm:0.5.2, fresh@npm:^0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 10c0/c6d27f3ed86cc5b601404822f31c900dd165ba63fff8152a3ef714e2012e7535027063bc67ded4cb5b3a49fa596495d46cacd9f47d6328459cf570f08b7d9e5a
  languageName: node
  linkType: hard

"fs-extra@npm:^10.0.1, fs-extra@npm:^10.1.0":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/5f579466e7109719d162a9249abbeffe7f426eb133ea486e020b89bc6d67a741134076bf439983f2eb79276ceaf6bd7b7c1e43c3fd67fe889863e69072fb0a5e
  languageName: node
  linkType: hard

"fs-extra@npm:^11.1.0":
  version: 11.1.1
  resolution: "fs-extra@npm:11.1.1"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/a2480243d7dcfa7d723c5f5b24cf4eba02a6ccece208f1524a2fbde1c629492cfb9a59e4b6d04faff6fbdf71db9fdc8ef7f396417a02884195a625f5d8dc9427
  languageName: node
  linkType: hard

"fs-extra@npm:^8.1.0":
  version: 8.1.0
  resolution: "fs-extra@npm:8.1.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^4.0.0"
    universalify: "npm:^0.1.0"
  checksum: 10c0/259f7b814d9e50d686899550c4f9ded85c46c643f7fe19be69504888e007fcbc08f306fae8ec495b8b998635e997c9e3e175ff2eeed230524ef1c1684cc96423
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/703d16522b8282d7299337539c3ed6edddd1afe82435e4f5b76e34a79cd74e488a8a0e26a636afc2440e1a23b03878e2122e3a2cfe375a5cf63c37d92b86a004
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.2
  resolution: "fs-minipass@npm:3.0.2"
  dependencies:
    minipass: "npm:^5.0.0"
  checksum: 10c0/34726f25b968ac05f6122ea7e9457fe108c7ae3b82beff0256953b0e405def61af2850570e32be2eb05c1e7660b663f24e14b6ab882d1d8a858314faacc4c972
  languageName: node
  linkType: hard

"fs-monkey@npm:^1.0.4":
  version: 1.0.4
  resolution: "fs-monkey@npm:1.0.4"
  checksum: 10c0/eeb2457ec50f7202c44273de2a42b50868c8e6b2ab4825d517947143d4e727c028e24f6d0f46e6f3e7a149a1c9e7d8b3ca28243c3b10366d280a08016483e829
  languageName: node
  linkType: hard

"fs-readdir-recursive@npm:^1.1.0":
  version: 1.1.0
  resolution: "fs-readdir-recursive@npm:1.1.0"
  checksum: 10c0/7e190393952143e674b6d1ad4abcafa1b5d3e337fcc21b0cb051079a7140a54617a7df193d562ef9faf21bd7b2148a38601b3d5c16261fa76f278d88dc69989c
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10c0/444cf1291d997165dfd4c0d58b69f0e4782bfd9149fd72faa4fe299e68e0e93d6db941660b37dd29153bf7186672ececa3b50b7e7249477b03fdf850f287c948
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.2
  resolution: "fsevents@npm:2.3.2"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/be78a3efa3e181cda3cf7a4637cb527bcebb0bd0ea0440105a3bb45b86f9245b307dc10a2507e8f4498a7d4ec349d1910f4d73e4d4495b16103106e07eee735b
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>":
  version: 2.3.2
  resolution: "fsevents@patch:fsevents@npm%3A2.3.2#optional!builtin<compat/fsevents>::version=2.3.2&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.1":
  version: 1.1.1
  resolution: "function-bind@npm:1.1.1"
  checksum: 10c0/60b74b2407e1942e1ed7f8c284f8ef714d0689dcfce5319985a5b7da3fc727f40b4a59ec72dc55aa83365ad7b8fa4fac3a30d93c850a2b452f29ae03dbc10a1e
  languageName: node
  linkType: hard

"gauge@npm:^4.0.3":
  version: 4.0.4
  resolution: "gauge@npm:4.0.4"
  dependencies:
    aproba: "npm:^1.0.3 || ^2.0.0"
    color-support: "npm:^1.1.3"
    console-control-strings: "npm:^1.1.0"
    has-unicode: "npm:^2.0.1"
    signal-exit: "npm:^3.0.7"
    string-width: "npm:^4.2.3"
    strip-ansi: "npm:^6.0.1"
    wide-align: "npm:^1.1.5"
  checksum: 10c0/ef10d7981113d69225135f994c9f8c4369d945e64a8fc721d655a3a38421b738c9fe899951721d1b47b73c41fdb5404ac87cc8903b2ecbed95d2800363e7e58c
  languageName: node
  linkType: hard

"generate-function@npm:^2.3.1":
  version: 2.3.1
  resolution: "generate-function@npm:2.3.1"
  dependencies:
    is-property: "npm:^1.0.2"
  checksum: 10c0/4645cf1da90375e46a6f1dc51abc9933e5eafa4cd1a44c2f7e3909a30a4e9a1a08c14cd7d5b32da039da2dba2a085e1ed4597b580c196c3245b2d35d8bc0de5d
  languageName: node
  linkType: hard

"get-func-name@npm:^2.0.0":
  version: 2.0.0
  resolution: "get-func-name@npm:2.0.0"
  checksum: 10c0/ed8791f7ba92cfd747259dff7ec8b6cc42734cebd031fb58c99a6e71d24d3532d84b46ad7806cafad6ad21784dd04ae1808a002d2b21001425e21f5f394c34e7
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.0.2":
  version: 1.2.1
  resolution: "get-intrinsic@npm:1.2.1"
  dependencies:
    function-bind: "npm:^1.1.1"
    has: "npm:^1.0.3"
    has-proto: "npm:^1.0.1"
    has-symbols: "npm:^1.0.3"
  checksum: 10c0/49eab47f9de8f1a4f9b458b8b74ee5199fb2614414a91973eb175e07db56b52b6df49b255cc7ff704cb0786490fb93bfe8f2ad138b590a8de09b47116a366bc9
  languageName: node
  linkType: hard

"get-package-type@npm:^0.1.0":
  version: 0.1.0
  resolution: "get-package-type@npm:0.1.0"
  checksum: 10c0/e34cdf447fdf1902a1f6d5af737eaadf606d2ee3518287abde8910e04159368c268568174b2e71102b87b26c2020486f126bfca9c4fb1ceb986ff99b52ecd1be
  languageName: node
  linkType: hard

"get-port@npm:6.1.2":
  version: 6.1.2
  resolution: "get-port@npm:6.1.2"
  checksum: 10c0/cac5f0c600691aed72fdcfacd394b8046080b5208898c3a6b9d10f999466297f162d7907bc6ecbc62d109a904dab7af7cdc0d7933ce2bcecfc5c1fedf7fcfab1
  languageName: node
  linkType: hard

"get-port@npm:^5.1.1":
  version: 5.1.1
  resolution: "get-port@npm:5.1.1"
  checksum: 10c0/2873877a469b24e6d5e0be490724a17edb39fafc795d1d662e7bea951ca649713b4a50117a473f9d162312cb0e946597bd0e049ed2f866e79e576e8e213d3d1c
  languageName: node
  linkType: hard

"get-source@npm:^2.0.12":
  version: 2.0.12
  resolution: "get-source@npm:2.0.12"
  dependencies:
    data-uri-to-buffer: "npm:^2.0.0"
    source-map: "npm:^0.6.1"
  checksum: 10c0/b1db46d28902344fd9407e1f0ed0b8f3a85cb4650f85ba8cee9c0b422fc75118172f12f735706e2c6e034617b13a2fbc5266e7fab617ecb184f0cee074b9dd3e
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0, get-stream@npm:^6.0.1":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: 10c0/49825d57d3fd6964228e6200a58169464b8e8970489b3acdc24906c782fb7f01f9f56f8e6653c4a50713771d6658f7cfe051e5eb8c12e334138c9c918b296341
  languageName: node
  linkType: hard

"get-value@npm:^2.0.3, get-value@npm:^2.0.6":
  version: 2.0.6
  resolution: "get-value@npm:2.0.6"
  checksum: 10c0/f069c132791b357c8fc4adfe9e2929b0a2c6e95f98ca7bc6fcbc27f8a302e552f86b4ae61ec56d9e9ac2544b93b6a39743d479866a37b43fcc104088ba74f0d9
  languageName: node
  linkType: hard

"getopts@npm:2.3.0, getopts@npm:^2.3.0":
  version: 2.3.0
  resolution: "getopts@npm:2.3.0"
  checksum: 10c0/edbcbd7020e9d87dc41e4ad9add5eb3873ae61339a62431bd92a461be2c0eaa9ec33b6fd0d67fa1b44feedffcf1cf28d6f9dbdb7d604cb1617eaba146a33cbca
  languageName: node
  linkType: hard

"glob-parent@npm:^3.1.0":
  version: 3.1.0
  resolution: "glob-parent@npm:3.1.0"
  dependencies:
    is-glob: "npm:^3.1.0"
    path-dirname: "npm:^1.0.0"
  checksum: 10c0/bfa89ce5ae1dfea4c2ece7b61d2ea230d87fcbec7472915cfdb3f4caf688a91ecb0dc86ae39b1e17505adce7e64cae3b971d64dc66091f3a0131169fd631b00d
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10c0/cab87638e2112bee3f839ef5f6e0765057163d39c66be8ec1602f3823da4692297ad4e972de876ea17c44d652978638d2fd583c6713d0eb6591706825020c9ee
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10c0/317034d88654730230b3f43bb7ad4f7c90257a426e872ea0bf157473ac61c99bf5d205fad8f0185f989be8d2fa6d3c7dce1645d99d545b6ea9089c39f838e7f8
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.3.0":
  version: 0.3.0
  resolution: "glob-to-regexp@npm:0.3.0"
  checksum: 10c0/f7e8091288d88b397b715281560d86ba4998246c300cb0d51db483db0a4c68cb48b489af8da9c03262745e8aa5337ba596d82dee61ff9467c5d7c27d70b676aa
  languageName: node
  linkType: hard

"glob@npm:^10.2.2":
  version: 10.3.3
  resolution: "glob@npm:10.3.3"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^2.0.3"
    minimatch: "npm:^9.0.1"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
    path-scurry: "npm:^1.10.1"
  bin:
    glob: dist/cjs/src/bin.js
  checksum: 10c0/50effa4208762e508def5688e4d88242db80b5913f65e9c5d5aefb707c59e66a27e845fbf18127157189f6ed0f055e2c94d7112c97a065b9cbfe002e1b26d330
  languageName: node
  linkType: hard

"glob@npm:^7.1.3, glob@npm:^7.1.4":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10c0/65676153e2b0c9095100fe7f25a778bf45608eeb32c6048cf307f579649bcc30353277b3b898a3792602c65764e5baa4f643714dfbdfd64ea271d210c7a425fe
  languageName: node
  linkType: hard

"glob@npm:^8.0.0, glob@npm:^8.0.3":
  version: 8.1.0
  resolution: "glob@npm:8.1.0"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^5.0.1"
    once: "npm:^1.3.0"
  checksum: 10c0/cb0b5cab17a59c57299376abe5646c7070f8acb89df5595b492dba3bfb43d301a46c01e5695f01154e6553168207cb60d4eaf07d3be4bc3eb9b0457c5c561d0f
  languageName: node
  linkType: hard

"globals@npm:^13.19.0":
  version: 13.20.0
  resolution: "globals@npm:13.20.0"
  dependencies:
    type-fest: "npm:^0.20.2"
  checksum: 10c0/9a028f136f1e7a3574689f430f7d57faa0d699c4c7e92ade00b02882a892be31c314d50dff07b48e607283013117bb8a997406d03a1f7ab4a33a005eb16efd6c
  languageName: node
  linkType: hard

"globby@npm:^11.1.0":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: "npm:^2.1.0"
    dir-glob: "npm:^3.0.1"
    fast-glob: "npm:^3.2.9"
    ignore: "npm:^5.2.0"
    merge2: "npm:^1.4.1"
    slash: "npm:^3.0.0"
  checksum: 10c0/b39511b4afe4bd8a7aead3a27c4ade2b9968649abab0a6c28b1a90141b96ca68ca5db1302f7c7bd29eab66bf51e13916b8e0a3d0ac08f75e1e84a39b35691189
  languageName: node
  linkType: hard

"globby@npm:^9.2.0":
  version: 9.2.0
  resolution: "globby@npm:9.2.0"
  dependencies:
    "@types/glob": "npm:^7.1.1"
    array-union: "npm:^1.0.2"
    dir-glob: "npm:^2.2.2"
    fast-glob: "npm:^2.2.6"
    glob: "npm:^7.1.3"
    ignore: "npm:^4.0.3"
    pify: "npm:^4.0.1"
    slash: "npm:^2.0.0"
  checksum: 10c0/2bd47ec43797b81000f3619feff96803b22591961788c06d746f6c8ba2deb14676b591ee625eb74b197c0047b2236e4a7a2ad662417661231b317c1de67aee94
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 10c0/e951259d8cd2e0d196c72ec711add7115d42eb9a8146c8eeda5b8d3ac91e5dd816b9cd68920726d9fd4490368e7ed86e9c423f40db87e2d8dfafa00fa17c3a31
  languageName: node
  linkType: hard

"has-ansi@npm:^2.0.0":
  version: 2.0.0
  resolution: "has-ansi@npm:2.0.0"
  dependencies:
    ansi-regex: "npm:^2.0.0"
  checksum: 10c0/f54e4887b9f8f3c4bfefd649c48825b3c093987c92c27880ee9898539e6f01aed261e82e73153c3f920fde0db5bf6ebd58deb498ed1debabcb4bc40113ccdf05
  languageName: node
  linkType: hard

"has-binary@npm:0.1.7":
  version: 0.1.7
  resolution: "has-binary@npm:0.1.7"
  dependencies:
    isarray: "npm:0.0.1"
  checksum: 10c0/bf2b3321ca1f54a363effb498d3f9c66f3daa8ef4f53024d46c4d8c0de4f0b48ec65278b0adb643159284ae6322594d93ff89ac97e9e9ed11ec8fde1d6a72cf3
  languageName: node
  linkType: hard

"has-cors@npm:1.1.0":
  version: 1.1.0
  resolution: "has-cors@npm:1.1.0"
  checksum: 10c0/5ca44b97681cb05c4fde04bf3d8d84d0b0a95d6134eb5821e057ea3b09f4c658c8b499bcdfc4d8ad669253b5249767062e2882eba40950eb73e4465748d4f3cf
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10c0/2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"has-glob@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-glob@npm:1.0.0"
  dependencies:
    is-glob: "npm:^3.0.0"
  checksum: 10c0/2546d20b7a667304d8b2e490c2d5a4e20e799a43eb6d97c0d47c0c737bbde082a73731001c791d445b904b3f408d584477df7d2d301183e13c4b3f0a3c81787b
  languageName: node
  linkType: hard

"has-own-prop@npm:^2.0.0":
  version: 2.0.0
  resolution: "has-own-prop@npm:2.0.0"
  checksum: 10c0/2745497283d80228b5c5fbb8c63ab1029e604bce7db8d4b36255e427b3695b2153dc978b176674d0dd2a23f132809e04d7ef41fefc0ab85870a5caa918c5c0d9
  languageName: node
  linkType: hard

"has-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "has-proto@npm:1.0.1"
  checksum: 10c0/c8a8fe411f810b23a564bd5546a8f3f0fff6f1b692740eb7a2fdc9df716ef870040806891e2f23ff4653f1083e3895bf12088703dd1a0eac3d9202d3a4768cd0
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3":
  version: 1.0.3
  resolution: "has-symbols@npm:1.0.3"
  checksum: 10c0/e6922b4345a3f37069cdfe8600febbca791c94988c01af3394d86ca3360b4b93928bbf395859158f88099cb10b19d98e3bbab7c9ff2c1bd09cf665ee90afa2c3
  languageName: node
  linkType: hard

"has-unicode@npm:^2.0.1":
  version: 2.0.1
  resolution: "has-unicode@npm:2.0.1"
  checksum: 10c0/ebdb2f4895c26bb08a8a100b62d362e49b2190bcfd84b76bc4be1a3bd4d254ec52d0dd9f2fbcc093fc5eb878b20c52146f9dfd33e2686ed28982187be593b47c
  languageName: node
  linkType: hard

"has-value@npm:^0.3.1":
  version: 0.3.1
  resolution: "has-value@npm:0.3.1"
  dependencies:
    get-value: "npm:^2.0.3"
    has-values: "npm:^0.1.4"
    isobject: "npm:^2.0.0"
  checksum: 10c0/7a7c2e9d07bc9742c81806150adb154d149bc6155267248c459cd1ce2a64b0759980d26213260e4b7599c8a3754551179f155ded88d0533a0d2bc7bc29028432
  languageName: node
  linkType: hard

"has-value@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-value@npm:1.0.0"
  dependencies:
    get-value: "npm:^2.0.6"
    has-values: "npm:^1.0.0"
    isobject: "npm:^3.0.0"
  checksum: 10c0/17cdccaf50f8aac80a109dba2e2ee5e800aec9a9d382ef9deab66c56b34269e4c9ac720276d5ffa722764304a1180ae436df077da0dd05548cfae0209708ba4d
  languageName: node
  linkType: hard

"has-values@npm:^0.1.4":
  version: 0.1.4
  resolution: "has-values@npm:0.1.4"
  checksum: 10c0/a8f00ad862c20289798c35243d5bd0b0a97dd44b668c2204afe082e0265f2d0bf3b89fc8cc0ef01a52b49f10aa35cf85c336ee3a5f1cac96ed490f5e901cdbf2
  languageName: node
  linkType: hard

"has-values@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-values@npm:1.0.0"
  dependencies:
    is-number: "npm:^3.0.0"
    kind-of: "npm:^4.0.0"
  checksum: 10c0/a6f2a1cc6b2e43eacc68e62e71ad6890def7f4b13d2ef06b4ad3ee156c23e470e6df144b9b467701908e17633411f1075fdff0cab45fb66c5e0584d89b25f35e
  languageName: node
  linkType: hard

"has-yarn@npm:^2.1.0":
  version: 2.1.0
  resolution: "has-yarn@npm:2.1.0"
  checksum: 10c0/b5cab61b4129c2fc0474045b59705371b7f5ddf2aab8ba8725011e52269f017e06f75059a2c8a1d8011e9779c2885ad987263cfc6d1280f611c396b45fd5d74a
  languageName: node
  linkType: hard

"has@npm:^1.0.3":
  version: 1.0.3
  resolution: "has@npm:1.0.3"
  dependencies:
    function-bind: "npm:^1.1.1"
  checksum: 10c0/e1da0d2bd109f116b632f27782cf23182b42f14972ca9540e4c5aa7e52647407a0a4a76937334fddcb56befe94a3494825ec22b19b51f5e5507c3153fd1a5e1b
  languageName: node
  linkType: hard

"haye@npm:^3.0.0":
  version: 3.0.0
  resolution: "haye@npm:3.0.0"
  checksum: 10c0/67058c630a32346c7f56f27e2effb66e176fe0df9cb217deacdbc272575090d59df72ae0ffd11fdccdd32ec2ab3ac0deeb2fbc033d5373ffe92114ac6d4139de
  languageName: node
  linkType: hard

"he@npm:^1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 10c0/a27d478befe3c8192f006cdd0639a66798979dfa6e2125c6ac582a19a5ebfec62ad83e8382e6036170d873f46e4536a7e795bf8b95bf7c247f4cc0825ccc8c17
  languageName: node
  linkType: hard

"header-case@npm:^2.0.4":
  version: 2.0.4
  resolution: "header-case@npm:2.0.4"
  dependencies:
    capital-case: "npm:^1.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10c0/c9f295d9d8e38fa50679281fd70d80726962256e888a76c8e72e526453da7a1832dcb427caa716c1ad5d79841d4537301b90156fa30298fefd3d68f4ea2181bb
  languageName: node
  linkType: hard

"help-me@npm:^4.0.1":
  version: 4.2.0
  resolution: "help-me@npm:4.2.0"
  dependencies:
    glob: "npm:^8.0.0"
    readable-stream: "npm:^3.6.0"
  checksum: 10c0/e61ef2de21c550896f4df40f1456baf22077205f3753af481d4408fd57f191d630306175edd8c083100cb14f7986d5058ce977876eac48e71962451321455b7b
  languageName: node
  linkType: hard

"hexoid@npm:^1.0.0":
  version: 1.0.0
  resolution: "hexoid@npm:1.0.0"
  checksum: 10c0/9c45e8ba676b9eb88455631ebceec4c829a8374a583410dc735472ab9808bf11339fcd074633c3fa30e420901b894d8a92ffd5e2e21eddd41149546e05a91f69
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 10c0/ce1319b8a382eb3cbb4a37c19f6bfe14e5bb5be3d09079e885e8c513ab2d3cd9214902f8a31c9dc4e37022633ceabfc2d697405deeaf1b8f3552bb4ed996fdfc
  languageName: node
  linkType: hard

"http-errors@npm:1.7.2":
  version: 1.7.2
  resolution: "http-errors@npm:1.7.2"
  dependencies:
    depd: "npm:~1.1.2"
    inherits: "npm:2.0.3"
    setprototypeof: "npm:1.1.1"
    statuses: "npm:>= 1.5.0 < 2"
    toidentifier: "npm:1.0.0"
  checksum: 10c0/49d3b2d52ee4bb24110fb4cff13a52e960501f63803d99bf50b6f93825335eab85bfd4809a90b5a5432ed13efe06c3979553a7a967cd196db1b0e23056068365
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0, http-errors@npm:^2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: 10c0/fc6f2715fe188d091274b5ffc8b3657bd85c63e969daa68ccb77afb05b071a4b62841acb7a21e417b5539014dff2ebf9550f0b14a9ff126f2734a7c1387f8e19
  languageName: node
  linkType: hard

"http-errors@npm:~1.7.2":
  version: 1.7.3
  resolution: "http-errors@npm:1.7.3"
  dependencies:
    depd: "npm:~1.1.2"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.1.1"
    statuses: "npm:>= 1.5.0 < 2"
    toidentifier: "npm:1.0.0"
  checksum: 10c0/5c3443c340d35b2f18ce908266c4ae93305b7d900bef765ac8dc56fa90125b9fe18a1ed9ebf6af23dc3ba7763731921a2682bf968e199eccf383eb8f508be6c2
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^5.0.0":
  version: 5.0.0
  resolution: "http-proxy-agent@npm:5.0.0"
  dependencies:
    "@tootallnate/once": "npm:2"
    agent-base: "npm:6"
    debug: "npm:4"
  checksum: 10c0/32a05e413430b2c1e542e5c74b38a9f14865301dd69dff2e53ddb684989440e3d2ce0c4b64d25eb63cf6283e6265ff979a61cf93e3ca3d23047ddfdc8df34a32
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: "npm:6"
    debug: "npm:4"
  checksum: 10c0/6dd639f03434003577c62b27cafdb864784ef19b2de430d8ae2a1d45e31c4fd60719e5637b44db1a88a046934307da7089e03d6089ec3ddacc1189d8de8897d1
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: 10c0/695edb3edfcfe9c8b52a76926cd31b36978782062c0ed9b1192b36bebc75c4c87c82e178dfcb0ed0fc27ca59d434198aac0bd0be18f5781ded775604db22304a
  languageName: node
  linkType: hard

"human-signals@npm:^4.3.0":
  version: 4.3.1
  resolution: "human-signals@npm:4.3.1"
  checksum: 10c0/40498b33fe139f5cc4ef5d2f95eb1803d6318ac1b1c63eaf14eeed5484d26332c828de4a5a05676b6c83d7b9e57727c59addb4b1dea19cb8d71e83689e5b336c
  languageName: node
  linkType: hard

"humanize-ms@npm:^1.2.1":
  version: 1.2.1
  resolution: "humanize-ms@npm:1.2.1"
  dependencies:
    ms: "npm:^2.0.0"
  checksum: 10c0/f34a2c20161d02303c2807badec2f3b49cbfbbb409abd4f95a07377ae01cfe6b59e3d15ac609cffcd8f2521f0eb37b7e1091acf65da99aa2a4f1ad63c21e7e7a
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3"
  checksum: 10c0/c6886a24cc00f2a059767440ec1bc00d334a89f250db8e0f7feb4961c8727118457e27c495ba94d082e51d3baca378726cd110aaf7ded8b9bbfd6a44760cf1d4
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2, iconv-lite@npm:^0.6.3":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"ieee754@npm:^1.2.1":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10c0/b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"igniculus@npm:^1.5.0":
  version: 1.5.0
  resolution: "igniculus@npm:1.5.0"
  checksum: 10c0/b7e68c723f1aeca72d23eeb74cbed1e56a64023793c95c5caba7d6982648ad3e5df352cb1fdc2f47f3ef9e030f24bf9db1bb654d88c989f4b0e9a14fc066fdd2
  languageName: node
  linkType: hard

"ignore@npm:^4.0.3":
  version: 4.0.6
  resolution: "ignore@npm:4.0.6"
  checksum: 10c0/836ee7dc7fd9436096e2dba429359dbb9fa0e33d309e2b2d81692f375f6ca82024fc00567f798613d50c6b989e9cd2ad2b065acf116325cde177f02c86b7d4e0
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0":
  version: 5.2.4
  resolution: "ignore@npm:5.2.4"
  checksum: 10c0/7c7cd90edd9fea6e037f9b9da4b01bf0a86b198ce78345f9bbd983929d68ff14830be31111edc5d70c264921f4962404d75b7262b4d9cc3bc12381eccbd03096
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10c0/7f882953aa6b740d1f0e384d0547158bc86efbf2eea0f1483b8900a6f65c5a5123c2cf09b0d542cc419d0b98a759ecaeb394237e97ea427f2da221dc3cd80cc3
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"inclusion@npm:^1.0.1":
  version: 1.0.1
  resolution: "inclusion@npm:1.0.1"
  dependencies:
    parent-module: "npm:^2.0.0"
  checksum: 10c0/2f017d15b77b6b1308ed0bfd531da129f62342a9f2d70c8dff710c38af3ac6bd41f67194113bd6e65c0e1f554e938b083c36a412104572020b30a64a78111d2e
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 10c0/1e1904ddb0cb3d6cce7cd09e27a90184908b7a5d5c21b92e232c93579d314f0b83c246ffb035493d0504b1e9147ba2c9b21df0030f48673fba0496ecd698161f
  languageName: node
  linkType: hard

"indexof@npm:0.0.1":
  version: 0.0.1
  resolution: "indexof@npm:0.0.1"
  checksum: 10c0/31f2b90def1d5429db21384a10d0ccc0d7bdbf0566d30d7fcabf99c7c2b7ffe420bb8fabadcce85e3d453b0e590c507de64401abe8c3eb238c878e78fcb98d33
  languageName: node
  linkType: hard

"inflation@npm:^2.0.0":
  version: 2.0.0
  resolution: "inflation@npm:2.0.0"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10c0/7faca22584600a9dc5b9fca2cd5feb7135ac8c935449837b315676b4c90aa4f391ec4f42240178244b5a34e8bede1948627fda392ca3191522fc46b34e985ab2
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10c0/4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"inherits@npm:2.0.3":
  version: 2.0.3
  resolution: "inherits@npm:2.0.3"
  checksum: 10c0/6e56402373149ea076a434072671f9982f5fad030c7662be0332122fe6c0fa490acb3cc1010d90b6eff8d640b1167d77674add52dfd1bb85d545cf29e80e73e7
  languageName: node
  linkType: hard

"ini@npm:~1.3.0":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: 10c0/ec93838d2328b619532e4f1ff05df7909760b6f66d9c9e2ded11e5c1897d6f2f9980c54dd638f88654b00919ce31e827040631eab0a3969e4d1abefa0719516a
  languageName: node
  linkType: hard

"interpret@npm:^2.2.0":
  version: 2.2.0
  resolution: "interpret@npm:2.2.0"
  checksum: 10c0/c0ef90daec6c4120bb7a226fa09a9511f6b5618aa9c94cf4641472f486948e643bb3b36efbd0136bbffdee876435af9fdf7bbb4622f5a16778eed5397f8a1946
  languageName: node
  linkType: hard

"ioredis@npm:^5.2.2":
  version: 5.3.2
  resolution: "ioredis@npm:5.3.2"
  dependencies:
    "@ioredis/commands": "npm:^1.1.1"
    cluster-key-slot: "npm:^1.1.0"
    debug: "npm:^4.3.4"
    denque: "npm:^2.1.0"
    lodash.defaults: "npm:^4.2.0"
    lodash.isarguments: "npm:^3.1.0"
    redis-errors: "npm:^1.2.0"
    redis-parser: "npm:^3.0.0"
    standard-as-callback: "npm:^2.1.0"
  checksum: 10c0/0dd2b5b8004e891f5b62edf18ac223194f1f5204698ec827c903e789ea05b0b36f73395491749ec63c66470485bdfb228ccdf1714fbf631a0f78f33211f2c883
  languageName: node
  linkType: hard

"ip@npm:^2.0.0":
  version: 2.0.0
  resolution: "ip@npm:2.0.0"
  checksum: 10c0/8d186cc5585f57372847ae29b6eba258c68862055e18a75cc4933327232cb5c107f89800ce29715d542eef2c254fbb68b382e780a7414f9ee7caf60b7a473958
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: 10c0/0486e775047971d3fdb5fb4f063829bac45af299ae0b82dcf3afa2145338e08290563a2a70f34b732d795ecc8311902e541a8530eeb30d75860a78ff4e94ce2a
  languageName: node
  linkType: hard

"is-accessor-descriptor@npm:^0.1.6":
  version: 0.1.6
  resolution: "is-accessor-descriptor@npm:0.1.6"
  dependencies:
    kind-of: "npm:^3.0.2"
  checksum: 10c0/f2c314b314ec6e8a6e559351bff3c7ee9aed7a5e9c6f61dd8cb9e1382c8bfe33dca3f0e0af13daf9ded9e6e66390ff23b4acfb615d7a249009a51506a7b0f151
  languageName: node
  linkType: hard

"is-accessor-descriptor@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-accessor-descriptor@npm:1.0.0"
  dependencies:
    kind-of: "npm:^6.0.0"
  checksum: 10c0/d68edafd8ef133e9003837f3c80f4e5b82b12ab5456c772d1796857671ae83e3a426ed225a28a7e35bceabbce68c1f1ffdabf47e6d53f5a4d6c4558776ad3c20
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 10c0/a16eaee59ae2b315ba36fad5c5dcaf8e49c3e27318f8ab8fa3cdb8772bf559c8d1ba750a589c2ccb096113bb64497084361a25960899cb6172a6925ab6123d38
  languageName: node
  linkType: hard

"is-buffer@npm:^1.1.5, is-buffer@npm:~1.1.1":
  version: 1.1.6
  resolution: "is-buffer@npm:1.1.6"
  checksum: 10c0/ae18aa0b6e113d6c490ad1db5e8df9bdb57758382b313f5a22c9c61084875c6396d50bbf49315f5b1926d142d74dfb8d31b40d993a383e0a158b15fea7a82234
  languageName: node
  linkType: hard

"is-core-module@npm:^2.11.0":
  version: 2.12.1
  resolution: "is-core-module@npm:2.12.1"
  dependencies:
    has: "npm:^1.0.3"
  checksum: 10c0/ff1d0dfc0b7851310d289398e416eb92ae8a9ac7ea8b8b9737fa8c0725f5a78c5f3db6edd4dff38c9ed731f3aaa1f6410a320233fcb52a2c8f1cf58eebf10a4b
  languageName: node
  linkType: hard

"is-data-descriptor@npm:^0.1.4":
  version: 0.1.4
  resolution: "is-data-descriptor@npm:0.1.4"
  dependencies:
    kind-of: "npm:^3.0.2"
  checksum: 10c0/32fda7e966b2c1f093230d5ef2aad1bb86e43e7280da50961e38ec31dbd8a50570a2911fd45277d321074a0762adc98e8462bb62820462594128857225e90d21
  languageName: node
  linkType: hard

"is-data-descriptor@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-data-descriptor@npm:1.0.0"
  dependencies:
    kind-of: "npm:^6.0.0"
  checksum: 10c0/bed31385d7d1a0dbb2ab3077faf2188acf42609192dca4e320ed7b3dc14a9d70c00658956cdaa2c0402be136c6b56e183973ad81b730fd90ab427fb6fd3608be
  languageName: node
  linkType: hard

"is-descriptor@npm:^0.1.0":
  version: 0.1.6
  resolution: "is-descriptor@npm:0.1.6"
  dependencies:
    is-accessor-descriptor: "npm:^0.1.6"
    is-data-descriptor: "npm:^0.1.4"
    kind-of: "npm:^5.0.0"
  checksum: 10c0/6b8f5617b764ef8c6be3d54830184357e6cdedd8e0eddf1b97d0658616ac170bfdbc7c1ad00e0aa9f5b767acdb9d6c63d4df936501784b34936bd0f9acf3b665
  languageName: node
  linkType: hard

"is-descriptor@npm:^1.0.0, is-descriptor@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-descriptor@npm:1.0.2"
  dependencies:
    is-accessor-descriptor: "npm:^1.0.0"
    is-data-descriptor: "npm:^1.0.0"
    kind-of: "npm:^6.0.2"
  checksum: 10c0/a05169c7a87feb88fc155e3ada469090cfabb5a548a3f794358b511cc47a0871b8b95e7345be4925a22ef3df585c3923b31943b3ad6255ce563a9d97f2e221e0
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0, is-docker@npm:^2.1.1":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 10c0/e828365958d155f90c409cdbe958f64051d99e8aedc2c8c4cd7c89dcf35329daed42f7b99346f7828df013e27deb8f721cf9408ba878c76eb9e8290235fbcdcc
  languageName: node
  linkType: hard

"is-docker@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-docker@npm:3.0.0"
  bin:
    is-docker: cli.js
  checksum: 10c0/d2c4f8e6d3e34df75a5defd44991b6068afad4835bb783b902fa12d13ebdb8f41b2a199dcb0b5ed2cb78bfee9e4c0bbdb69c2d9646f4106464674d3e697a5856
  languageName: node
  linkType: hard

"is-extendable@npm:^0.1.0, is-extendable@npm:^0.1.1":
  version: 0.1.1
  resolution: "is-extendable@npm:0.1.1"
  checksum: 10c0/dd5ca3994a28e1740d1e25192e66eed128e0b2ff161a7ea348e87ae4f616554b486854de423877a2a2c171d5f7cd6e8093b91f54533bc88a59ee1c9838c43879
  languageName: node
  linkType: hard

"is-extendable@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-extendable@npm:1.0.1"
  dependencies:
    is-plain-object: "npm:^2.0.4"
  checksum: 10c0/1d6678a5be1563db6ecb121331c819c38059703f0179f52aa80c242c223ee9c6b66470286636c0e63d7163e4d905c0a7d82a096e0b5eaeabb51b9f8d0af0d73f
  languageName: node
  linkType: hard

"is-extglob@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-extglob@npm:1.0.0"
  checksum: 10c0/1ce5366d19958f36069a45ca996c1e51ab607f42a01eb0505f0ccffe8f9c91f5bcba6e971605efd8b4d4dfd0111afa3c8df3e1746db5b85b9a8f933f5e7286b7
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.0, is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10c0/5487da35691fbc339700bbb2730430b07777a3c21b9ebaecb3072512dfd7b4ba78ac2381a87e8d78d20ea08affb3f1971b4af629173a6bf435ff8a4c47747912
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-glob@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-glob@npm:2.0.1"
  dependencies:
    is-extglob: "npm:^1.0.0"
  checksum: 10c0/ef156806af0924983325c9218a8b8a838fa50e1a104ed2a11fe94829a5b27c1b05a4c8cf98d96cb3a7fea539c21f14ae2081e1a248f3d5a9eea62f2d4e9f8b0c
  languageName: node
  linkType: hard

"is-glob@npm:^3.0.0, is-glob@npm:^3.1.0":
  version: 3.1.0
  resolution: "is-glob@npm:3.1.0"
  dependencies:
    is-extglob: "npm:^2.1.0"
  checksum: 10c0/ba816a35dcf5285de924a8a4654df7b183a86381d73ea3bbf3df3cc61b3ba61fdddf90ee205709a2235b210ee600ee86e5e8600093cf291a662607fd032e2ff4
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10c0/17fb4014e22be3bbecea9b2e3a76e9e34ff645466be702f1693e8f1ee1adac84710d0be0bd9f967d6354036fd51ab7c2741d954d6e91dae6bb69714de92c197a
  languageName: node
  linkType: hard

"is-inside-container@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-inside-container@npm:1.0.0"
  dependencies:
    is-docker: "npm:^3.0.0"
  bin:
    is-inside-container: cli.js
  checksum: 10c0/a8efb0e84f6197e6ff5c64c52890fa9acb49b7b74fed4da7c95383965da6f0fa592b4dbd5e38a79f87fc108196937acdbcd758fcefc9b140e479b39ce1fcd1cd
  languageName: node
  linkType: hard

"is-invalid-path@npm:^0.1.0":
  version: 0.1.0
  resolution: "is-invalid-path@npm:0.1.0"
  dependencies:
    is-glob: "npm:^2.0.0"
  checksum: 10c0/9f7f74825ddcbd70ceb0aca1155d2961f3767a7a0f1351c255d25047cc7dece161b755d0698aaf8f201693d96ea12e04b4afa00ee9b4f8f47ab5ec2adbe96df8
  languageName: node
  linkType: hard

"is-lambda@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-lambda@npm:1.0.1"
  checksum: 10c0/85fee098ae62ba6f1e24cf22678805473c7afd0fb3978a3aa260e354cb7bcb3a5806cf0a98403188465efedec41ab4348e8e4e79305d409601323855b3839d4d
  languageName: node
  linkType: hard

"is-number@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-number@npm:3.0.0"
  dependencies:
    kind-of: "npm:^3.0.2"
  checksum: 10c0/e639c54640b7f029623df24d3d103901e322c0c25ea5bde97cd723c2d0d4c05857a8364ab5c58d963089dbed6bf1d0ffe975cb6aef917e2ad0ccbca653d31b4f
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10c0/b4686d0d3053146095ccd45346461bc8e53b80aeb7671cc52a4de02dbbf7dc0d1d2a986e2fe4ae206984b4d34ef37e8b795ebc4f4295c978373e6575e295d811
  languageName: node
  linkType: hard

"is-path-inside@npm:^3.0.3":
  version: 3.0.3
  resolution: "is-path-inside@npm:3.0.3"
  checksum: 10c0/cf7d4ac35fb96bab6a1d2c3598fe5ebb29aafb52c0aaa482b5a3ed9d8ba3edc11631e3ec2637660c44b3ce0e61a08d54946e8af30dec0b60a7c27296c68ffd05
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.3, is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: "npm:^3.0.1"
  checksum: 10c0/f050fdd5203d9c81e8c4df1b3ff461c4bc64e8b5ca383bcdde46131361d0a678e80bcf00b5257646f6c636197629644d53bd8e2375aea633de09a82d57e942f4
  languageName: node
  linkType: hard

"is-property@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-property@npm:1.0.2"
  checksum: 10c0/33ab65a136e4ba3f74d4f7d9d2a013f1bd207082e11cedb160698e8d5394644e873c39668d112a402175ccbc58a087cef87198ed46829dbddb479115a0257283
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: 10c0/7c284241313fc6efc329b8d7f08e16c0efeb6baab1b4cd0ba579eb78e5af1aa5da11e68559896a2067cd6c526bd29241dda4eb1225e627d5aa1a89a76d4635a5
  languageName: node
  linkType: hard

"is-stream@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-stream@npm:3.0.0"
  checksum: 10c0/eb2f7127af02ee9aa2a0237b730e47ac2de0d4e76a4a905a50a11557f2339df5765eaea4ceb8029f1efa978586abe776908720bfcb1900c20c6ec5145f6f29d8
  languageName: node
  linkType: hard

"is-valid-path@npm:^0.1.1":
  version: 0.1.1
  resolution: "is-valid-path@npm:0.1.1"
  dependencies:
    is-invalid-path: "npm:^0.1.0"
  checksum: 10c0/05c3533b8d98ac469bec9849e6ee73a07e1f9857e2043c75a9a45d21bae5e11fafb625808d7bd1aaf5cc63e842876c636f9888388a959ee9c33975c7b603c6ba
  languageName: node
  linkType: hard

"is-windows@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-windows@npm:1.0.2"
  checksum: 10c0/b32f418ab3385604a66f1b7a3ce39d25e8881dee0bd30816dc8344ef6ff9df473a732bcc1ec4e84fe99b2f229ae474f7133e8e93f9241686cfcf7eebe53ba7a5
  languageName: node
  linkType: hard

"is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: "npm:^2.0.0"
  checksum: 10c0/a6fa2d370d21be487c0165c7a440d567274fbba1a817f2f0bfa41cc5e3af25041d84267baa22df66696956038a43973e72fca117918c91431920bdef490fa25e
  languageName: node
  linkType: hard

"isarray@npm:0.0.1":
  version: 0.0.1
  resolution: "isarray@npm:0.0.1"
  checksum: 10c0/ed1e62da617f71fe348907c71743b5ed550448b455f8d269f89a7c7ddb8ae6e962de3dab6a74a237b06f5eb7f6ece7a45ada8ce96d87fe972926530f91ae3311
  languageName: node
  linkType: hard

"isarray@npm:1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: 10c0/18b5be6669be53425f0b84098732670ed4e727e3af33bc7f948aac01782110eb9a18b3b329c5323bcdd3acdaae547ee077d3951317e7f133bff7105264b3003d
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isobject@npm:^2.0.0":
  version: 2.1.0
  resolution: "isobject@npm:2.1.0"
  dependencies:
    isarray: "npm:1.0.0"
  checksum: 10c0/c4cafec73b3b2ee11be75dff8dafd283b5728235ac099b07d7873d5182553a707768e208327bbc12931b9422d8822280bf88d894a0024ff5857b3efefb480e7b
  languageName: node
  linkType: hard

"isobject@npm:^3.0.0, isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: 10c0/03344f5064a82f099a0cd1a8a407f4c0d20b7b8485e8e816c39f249e9416b06c322e8dec5b842b6bb8a06de0af9cb48e7bc1b5352f0fadc2f0abac033db3d4db
  languageName: node
  linkType: hard

"jackspeak@npm:^2.0.3":
  version: 2.2.2
  resolution: "jackspeak@npm:2.2.2"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/70b286206a2729f6a2ba8470f68d4d130f7154f6a767fccabf107b9f6b3871395e89018437c2676c849450b258711cb557a4be6d7b2f889f1fa4d8b364533225
  languageName: node
  linkType: hard

"jake@npm:^10.6.1":
  version: 10.8.7
  resolution: "jake@npm:10.8.7"
  dependencies:
    async: "npm:^3.2.3"
    chalk: "npm:^4.0.2"
    filelist: "npm:^1.0.4"
    minimatch: "npm:^3.1.2"
  bin:
    jake: bin/cli.js
  checksum: 10c0/89326d01a8bc110d02d973729a66394c79a34b34461116f5c530a2a2dbc30265683fe6737928f75df9178e9d369ff1442f5753fb983d525e740eefdadc56a103
  languageName: node
  linkType: hard

"jest-diff@npm:^25.5.0":
  version: 25.5.0
  resolution: "jest-diff@npm:25.5.0"
  dependencies:
    chalk: "npm:^3.0.0"
    diff-sequences: "npm:^25.2.6"
    jest-get-type: "npm:^25.2.6"
    pretty-format: "npm:^25.5.0"
  checksum: 10c0/1bdfd1791982d732be93419387a3092e044f89a5985b8d25972276ec37413916f2f4f0c1c3f3720a5316d840970c70b06a675464717877373f267059238639d6
  languageName: node
  linkType: hard

"jest-diff@npm:^29.4.1":
  version: 29.6.1
  resolution: "jest-diff@npm:29.6.1"
  dependencies:
    chalk: "npm:^4.0.0"
    diff-sequences: "npm:^29.4.3"
    jest-get-type: "npm:^29.4.3"
    pretty-format: "npm:^29.6.1"
  checksum: 10c0/f067d977937744df7dd8a269e2948620e4bcb35ff70d9ea1d0fe75a47fa603ce3edc350961b671c94f8de5adb65d6bdeb0002569b59983fba56f02dd4b47d171
  languageName: node
  linkType: hard

"jest-get-type@npm:^25.2.6":
  version: 25.2.6
  resolution: "jest-get-type@npm:25.2.6"
  checksum: 10c0/f113f7698959d808075f5da173d14f3fbb4abbaca1e0923dbec2b905c112114042f3afdd3b927592f738954ae4191b6e8df5fe749790f8fa29165bf76b2cb106
  languageName: node
  linkType: hard

"jest-get-type@npm:^29.4.3":
  version: 29.4.3
  resolution: "jest-get-type@npm:29.4.3"
  checksum: 10c0/874b0ced6b1cc677ff7fcf0dc86d02674617a7d0b73d47097604fb3ca460178d16104efdd3837e8b8bf0520ad5d210838c07483b058802b457b8413e60628fd0
  languageName: node
  linkType: hard

"jest-matcher-utils@npm:^25.5.0":
  version: 25.5.0
  resolution: "jest-matcher-utils@npm:25.5.0"
  dependencies:
    chalk: "npm:^3.0.0"
    jest-diff: "npm:^25.5.0"
    jest-get-type: "npm:^25.2.6"
    pretty-format: "npm:^25.5.0"
  checksum: 10c0/4fa495097eef7abd00d0cf8d33a95b994b8b8d835e74dc990fd736b7e55ff32ee370d0f4fa586226c3e58d9595160d7729e697747c8d73af2958f2ad50313d91
  languageName: node
  linkType: hard

"jest-worker@npm:^27.5.1":
  version: 27.5.1
  resolution: "jest-worker@npm:27.5.1"
  dependencies:
    "@types/node": "npm:*"
    merge-stream: "npm:^2.0.0"
    supports-color: "npm:^8.0.0"
  checksum: 10c0/8c4737ffd03887b3c6768e4cc3ca0269c0336c1e4b1b120943958ddb035ed2a0fc6acab6dc99631720a3720af4e708ff84fb45382ad1e83c27946adf3623969b
  languageName: node
  linkType: hard

"joycon@npm:^3.1.1":
  version: 3.1.1
  resolution: "joycon@npm:3.1.1"
  checksum: 10c0/131fb1e98c9065d067fd49b6e685487ac4ad4d254191d7aa2c9e3b90f4e9ca70430c43cad001602bdbdabcf58717d3b5c5b7461c1bd8e39478c8de706b3fe6ae
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.2":
  version: 3.0.2
  resolution: "js-tokens@npm:3.0.2"
  checksum: 10c0/e3c3ee4d12643d90197628eb022a2884a15f08ea7dcac1ce97fdeee43031fbfc7ede674f2cdbbb582dcd4c94388b22e52d56c6cbeb2ac7d1b57c2f33c405e2ba
  languageName: node
  linkType: hard

"js-yaml@npm:^3.14.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: "npm:^1.0.7"
    esprima: "npm:^4.0.0"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/6746baaaeac312c4db8e75fa22331d9a04cccb7792d126ed8ce6a0bbcfef0cedaddd0c5098fade53db067c09fe00aa1c957674b4765610a8b06a5a189e46433b
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/184a24b4eaacfce40ad9074c64fd42ac83cf74d8c8cd137718d456ced75051229e5061b8633c3366b8aada17945a7a356b337828c19da92b51ae62126575018f
  languageName: node
  linkType: hard

"json-schema-deref-sync@npm:^0.14.0":
  version: 0.14.0
  resolution: "json-schema-deref-sync@npm:0.14.0"
  dependencies:
    clone: "npm:^2.1.2"
    dag-map: "npm:~1.0.0"
    is-valid-path: "npm:^0.1.1"
    lodash: "npm:^4.17.13"
    md5: "npm:~2.2.0"
    memory-cache: "npm:~0.2.0"
    traverse: "npm:~0.6.6"
    valid-url: "npm:~1.0.9"
  checksum: 10c0/f221f1aefec2b8813d556216298cacae6b483fc04fea0bbad4293d35604228e889d0378f4c9439e4646cea2bf9ee3d74be5303760dbf8daf5cc69a68fa09d3d0
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10c0/108fa90d4cc6f08243aedc6da16c408daf81793bf903e9fd5ab21983cda433d5d2da49e40711da016289465ec2e62e0324dcdfbc06275a607fe3233fde4942ce
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 10c0/cb168b61fd4de83e58d09aaa6425ef71001bae30d260e2c57e7d09a5fd82223e2f22a042dedaab8db23b7d9ae46854b08bb1f91675a8be11c5cffebef5fb66a5
  languageName: node
  linkType: hard

"json3@npm:3.3.2":
  version: 3.3.2
  resolution: "json3@npm:3.3.2"
  checksum: 10c0/370300e729f05c315cdd5892aa577a2a3ece43a9f6dcf4d42c2ce8adafbe2787c6b587d0091f0d1360812ea09f3510d417be5aabedb1ab9ed21172cab804fe33
  languageName: node
  linkType: hard

"jsonfile@npm:^4.0.0":
  version: 4.0.0
  resolution: "jsonfile@npm:4.0.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/7dc94b628d57a66b71fb1b79510d460d662eb975b5f876d723f81549c2e9cd316d58a2ddf742b2b93a4fa6b17b2accaf1a738a0e2ea114bdfb13a32e5377e480
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/4f95b5e8a5622b1e9e8f33c96b7ef3158122f595998114d1e7f03985649ea99cb3cd99ce1ed1831ae94c8c8543ab45ebd044207612f31a56fd08462140e46865
  languageName: node
  linkType: hard

"junk@npm:^3.1.0":
  version: 3.1.0
  resolution: "junk@npm:3.1.0"
  checksum: 10c0/820174b9fa9a3af09aeeeeb1022df2481a2b10752ce5f65ac63924a79cb9bba83ea7c288e8d5b448951109742da5ea69a230846f4bf3c17c5c6a1d0603b63db4
  languageName: node
  linkType: hard

"kind-of@npm:^3.0.2, kind-of@npm:^3.0.3, kind-of@npm:^3.2.0":
  version: 3.2.2
  resolution: "kind-of@npm:3.2.2"
  dependencies:
    is-buffer: "npm:^1.1.5"
  checksum: 10c0/7e34bc29d4b02c997f92f080de34ebb92033a96736bbb0bb2410e033a7e5ae6571f1fa37b2d7710018f95361473b816c604234197f4f203f9cf149d8ef1574d9
  languageName: node
  linkType: hard

"kind-of@npm:^4.0.0":
  version: 4.0.0
  resolution: "kind-of@npm:4.0.0"
  dependencies:
    is-buffer: "npm:^1.1.5"
  checksum: 10c0/d6c44c75ee36898142dfc7106afbd50593216c37f96acb81a7ab33ca1a6938ce97d5692b8fc8fccd035f83811a9d97749d68771116441a48eedd0b68e2973165
  languageName: node
  linkType: hard

"kind-of@npm:^5.0.0":
  version: 5.1.0
  resolution: "kind-of@npm:5.1.0"
  checksum: 10c0/fe85b7a2ed4b4d5a12e16e01d00d5c336e1760842fe0da38283605b9880c984288935e87b13138909e4d23d2d197a1d492f7393c6638d2c0fab8a900c4fb0392
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.0, kind-of@npm:^6.0.2, kind-of@npm:^6.0.3":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 10c0/61cdff9623dabf3568b6445e93e31376bee1cdb93f8ba7033d86022c2a9b1791a1d9510e026e6465ebd701a6dd2f7b0808483ad8838341ac52f003f512e0b4c4
  languageName: node
  linkType: hard

"kleur@npm:^3.0.3":
  version: 3.0.3
  resolution: "kleur@npm:3.0.3"
  checksum: 10c0/cd3a0b8878e7d6d3799e54340efe3591ca787d9f95f109f28129bdd2915e37807bf8918bb295ab86afb8c82196beec5a1adcaf29042ce3f2bd932b038fe3aa4b
  languageName: node
  linkType: hard

"kleur@npm:^4.1.5":
  version: 4.1.5
  resolution: "kleur@npm:4.1.5"
  checksum: 10c0/e9de6cb49657b6fa70ba2d1448fd3d691a5c4370d8f7bbf1c2f64c24d461270f2117e1b0afe8cb3114f13bbd8e51de158c2a224953960331904e636a5e4c0f2a
  languageName: node
  linkType: hard

"knex-dynamic-connection@npm:^3.0.1":
  version: 3.1.0
  resolution: "knex-dynamic-connection@npm:3.1.0"
  dependencies:
    debug: "npm:^4.3.4"
    knex: "npm:^2.5.0"
  checksum: 10c0/23251faa8c1ed7245579f91197f3ce18b078fd792f9290703fe260e0250b02d4b084edda516c9fc2efde95f38e0d3c3e0c7adaf7de0c9d52be986841bd03ea7f
  languageName: node
  linkType: hard

"knex@npm:^2.4.2, knex@npm:^2.5.0":
  version: 2.5.1
  resolution: "knex@npm:2.5.1"
  dependencies:
    colorette: "npm:2.0.19"
    commander: "npm:^10.0.0"
    debug: "npm:4.3.4"
    escalade: "npm:^3.1.1"
    esm: "npm:^3.2.25"
    get-package-type: "npm:^0.1.0"
    getopts: "npm:2.3.0"
    interpret: "npm:^2.2.0"
    lodash: "npm:^4.17.21"
    pg-connection-string: "npm:2.6.1"
    rechoir: "npm:^0.8.0"
    resolve-from: "npm:^5.0.0"
    tarn: "npm:^3.0.2"
    tildify: "npm:2.0.0"
  peerDependenciesMeta:
    better-sqlite3:
      optional: true
    mysql:
      optional: true
    mysql2:
      optional: true
    pg:
      optional: true
    pg-native:
      optional: true
    sqlite3:
      optional: true
    tedious:
      optional: true
  bin:
    knex: bin/cli.js
  checksum: 10c0/33580641feaa93074bdc10e78f06382e5017d46a3cd22821c09057e7da303c35772add39a110fffe685c2f438a78751a0eb6f50aad1c4bdb032e8ec9b7879b69
  languageName: node
  linkType: hard

"leven@npm:^3.1.0":
  version: 3.1.0
  resolution: "leven@npm:3.1.0"
  checksum: 10c0/cd778ba3fbab0f4d0500b7e87d1f6e1f041507c56fdcd47e8256a3012c98aaee371d4c15e0a76e0386107af2d42e2b7466160a2d80688aaa03e66e49949f42df
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 10c0/effb03cad7c89dfa5bd4f6989364bfc79994c2042ec5966cb9b95990e2edee5cd8969ddf42616a0373ac49fac1403437deaf6e9050fbbaa3546093a59b9ac94e
  languageName: node
  linkType: hard

"listify@npm:^1.0.0":
  version: 1.0.3
  resolution: "listify@npm:1.0.3"
  checksum: 10c0/be973bd0c96ac482ccfc8c1c80a3b962458d9d59e0e4cb6c5af7b9980391d57469406e2c8010b9010bd3cc3a3d1a7d26eff559962cdfb58885b6f2ab5cc38e4a
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: "npm:^4.1.0"
  checksum: 10c0/33a1c5247e87e022f9713e6213a744557a3e9ec32c5d0b5efb10aa3a38177615bf90221a5592674857039c1a0fd2063b82f285702d37b792d973e9e72ace6c59
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: "npm:^5.0.0"
  checksum: 10c0/d3972ab70dfe58ce620e64265f90162d247e87159b6126b01314dd67be43d50e96a50b517bce2d9452a79409c7614054c277b5232377de50416564a77ac7aad3
  languageName: node
  linkType: hard

"lodash.defaults@npm:^4.2.0":
  version: 4.2.0
  resolution: "lodash.defaults@npm:4.2.0"
  checksum: 10c0/d5b77aeb702caa69b17be1358faece33a84497bcca814897383c58b28a2f8dfc381b1d9edbec239f8b425126a3bbe4916223da2a576bb0411c2cefd67df80707
  languageName: node
  linkType: hard

"lodash.flatten@npm:^4.4.0":
  version: 4.4.0
  resolution: "lodash.flatten@npm:4.4.0"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"lodash.get@npm:^4.4.2":
  version: 4.4.2
  resolution: "lodash.get@npm:4.4.2"
  checksum: 10c0/48f40d471a1654397ed41685495acb31498d5ed696185ac8973daef424a749ca0c7871bf7b665d5c14f5cc479394479e0307e781f61d5573831769593411be6e
  languageName: node
  linkType: hard

"lodash.isarguments@npm:^3.1.0":
  version: 3.1.0
  resolution: "lodash.isarguments@npm:3.1.0"
  checksum: 10c0/5e8f95ba10975900a3920fb039a3f89a5a79359a1b5565e4e5b4310ed6ebe64011e31d402e34f577eca983a1fc01ff86c926e3cbe602e1ddfc858fdd353e62d8
  languageName: node
  linkType: hard

"lodash.isequal@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.isequal@npm:4.5.0"
  checksum: 10c0/dfdb2356db19631a4b445d5f37868a095e2402292d59539a987f134a8778c62a2810c2452d11ae9e6dcac71fc9de40a6fedcb20e2952a15b431ad8b29e50e28f
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.1, lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10c0/402fa16a1edd7538de5b5903a90228aa48eb5533986ba7fa26606a49db2572bf414ff73a2c9f5d5fd36b31c46a5d5c7e1527749c07cbcf965ccff5fbdf32c506
  languageName: node
  linkType: hard

"lodash.set@npm:^4.3.2":
  version: 4.3.2
  resolution: "lodash.set@npm:4.3.2"
  checksum: 10c0/c641d31905e51df43170dce8a1d11a1cff11356e2e2e75fe2615995408e9687d58c3e1d64c3c284c2df2bc519f79a98af737d2944d382ff82ffd244ff6075c29
  languageName: node
  linkType: hard

"lodash@npm:^4.17.11, lodash@npm:^4.17.13, lodash@npm:^4.17.15, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10c0/d8cbea072bb08655bb4c989da418994b073a608dffa608b09ac04b43a791b12aeae7cd7ad919aa4c925f33b48490b5cfe6c1f71d827956071dae2e7bb3a6b74c
  languageName: node
  linkType: hard

"log-update@npm:^4.0.0":
  version: 4.0.0
  resolution: "log-update@npm:4.0.0"
  dependencies:
    ansi-escapes: "npm:^4.3.0"
    cli-cursor: "npm:^3.1.0"
    slice-ansi: "npm:^4.0.0"
    wrap-ansi: "npm:^6.2.0"
  checksum: 10c0/18b299e230432a156f2535660776406d15ba8bb7817dd3eaadd58004b363756d4ecaabcd658f9949f90b62ea7d3354423be3fdeb7a201ab951ec0e8d6139af86
  languageName: node
  linkType: hard

"long@npm:^5.2.1":
  version: 5.2.3
  resolution: "long@npm:5.2.3"
  checksum: 10c0/6a0da658f5ef683b90330b1af76f06790c623e148222da9d75b60e266bbf88f803232dd21464575681638894a84091616e7f89557aa087fd14116c0f4e0e43d9
  languageName: node
  linkType: hard

"loupe@npm:^2.3.1":
  version: 2.3.6
  resolution: "loupe@npm:2.3.6"
  dependencies:
    get-func-name: "npm:^2.0.0"
  checksum: 10c0/a974841ce94ef2a35aac7144e7f9e789e3887f82286cd9ffe7ff00f2ac9d117481989948657465e2b0b102f23136d89ae0a18fd4a32d9015012cd64464453289
  languageName: node
  linkType: hard

"lower-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "lower-case@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10c0/3d925e090315cf7dc1caa358e0477e186ffa23947740e4314a7429b6e62d72742e0bbe7536a5ae56d19d7618ce998aba05caca53c2902bd5742fdca5fc57fd7b
  languageName: node
  linkType: hard

"lru-cache@npm:^4.1.5":
  version: 4.1.5
  resolution: "lru-cache@npm:4.1.5"
  dependencies:
    pseudomap: "npm:^1.0.2"
    yallist: "npm:^2.1.2"
  checksum: 10c0/1ca5306814e5add9ec63556d6fd9b24a4ecdeaef8e9cea52cbf30301e6b88c8d8ddc7cab45b59b56eb763e6c45af911585dc89925a074ab65e1502e3fe8103cf
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/cb53e582785c48187d7a188d3379c181b5ca2a9c78d2bce3e7dee36f32761d1c42983da3fe12b55cb74e1779fa94cdc2e5367c028a9b35317184ede0c07a30a9
  languageName: node
  linkType: hard

"lru-cache@npm:^7.14.1, lru-cache@npm:^7.7.1":
  version: 7.18.3
  resolution: "lru-cache@npm:7.18.3"
  checksum: 10c0/b3a452b491433db885beed95041eb104c157ef7794b9c9b4d647be503be91769d11206bb573849a16b4cc0d03cbd15ffd22df7960997788b74c1d399ac7a4fed
  languageName: node
  linkType: hard

"lru-cache@npm:^8.0.0":
  version: 8.0.5
  resolution: "lru-cache@npm:8.0.5"
  checksum: 10c0/cd95a9c38497611c5a6453de39a881f6eb5865851a2a01b5f14104ff3fee515362a7b1e7de28606028f423802910ba05bdb8ae1aa7b0d54eae70c92f0cec10b2
  languageName: node
  linkType: hard

"lru-cache@npm:^9.1.1 || ^10.0.0":
  version: 10.0.0
  resolution: "lru-cache@npm:10.0.0"
  checksum: 10c0/347b7b391091e9f91182b6f683ce04329932a542376a2d7d300637213b99f06c222a3bb0f0db59adf246dac6cef1bb509cab352451a96621d07c41b10a20495f
  languageName: node
  linkType: hard

"luxon@npm:^3.0.3, luxon@npm:^3.2.1, luxon@npm:^3.3.0":
  version: 3.3.0
  resolution: "luxon@npm:3.3.0"
  checksum: 10c0/47f8e1e96b25441c799b8aa833b3f007fb1854713bcffc8c3384eda8e61fc9af1f038474d137274d2d386492f341c8a8c992fc78c213adfb3143780feba2776c
  languageName: node
  linkType: hard

"macroable@npm:^7.0.2":
  version: 7.0.2
  resolution: "macroable@npm:7.0.2"
  checksum: 10c0/402623a8fea3f78cd9b87d5a5c4c9a14abe99d567b2b722a0d4c35d699d013074dcc2f25803b5c8971071ba5dc731fc3068d5aae757e416d799f2ddcf6f01b30
  languageName: node
  linkType: hard

"make-dir@npm:^3.0.0, make-dir@npm:^3.0.2":
  version: 3.1.0
  resolution: "make-dir@npm:3.1.0"
  dependencies:
    semver: "npm:^6.0.0"
  checksum: 10c0/56aaafefc49c2dfef02c5c95f9b196c4eb6988040cf2c712185c7fe5c99b4091591a7fc4d4eafaaefa70ff763a26f6ab8c3ff60b9e75ea19876f49b18667ecaa
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^11.0.3":
  version: 11.1.1
  resolution: "make-fetch-happen@npm:11.1.1"
  dependencies:
    agentkeepalive: "npm:^4.2.1"
    cacache: "npm:^17.0.0"
    http-cache-semantics: "npm:^4.1.1"
    http-proxy-agent: "npm:^5.0.0"
    https-proxy-agent: "npm:^5.0.0"
    is-lambda: "npm:^1.0.1"
    lru-cache: "npm:^7.7.1"
    minipass: "npm:^5.0.0"
    minipass-fetch: "npm:^3.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^0.6.3"
    promise-retry: "npm:^2.0.1"
    socks-proxy-agent: "npm:^7.0.0"
    ssri: "npm:^10.0.0"
  checksum: 10c0/c161bde51dbc03382f9fac091734526a64dd6878205db6c338f70d2133df797b5b5166bff3091cf7d4785869d4b21e99a58139c1790c2fb1b5eec00f528f5f0b
  languageName: node
  linkType: hard

"map-age-cleaner@npm:^0.1.3":
  version: 0.1.3
  resolution: "map-age-cleaner@npm:0.1.3"
  dependencies:
    p-defer: "npm:^1.0.0"
  checksum: 10c0/7495236c7b0950956c144fd8b4bc6399d4e78072a8840a4232fe1c4faccbb5eb5d842e5c0a56a60afc36d723f315c1c672325ca03c1b328650f7fcc478f385fd
  languageName: node
  linkType: hard

"map-cache@npm:^0.2.2":
  version: 0.2.2
  resolution: "map-cache@npm:0.2.2"
  checksum: 10c0/05e3eb005c1b80b9f949ca007687640e8c5d0fc88dc45c3c3ab4902a3bec79d66a58f3e3b04d6985d90cd267c629c7b46c977e9c34433e8c11ecfcbb9f0fa290
  languageName: node
  linkType: hard

"map-visit@npm:^1.0.0":
  version: 1.0.0
  resolution: "map-visit@npm:1.0.0"
  dependencies:
    object-visit: "npm:^1.0.0"
  checksum: 10c0/fb3475e5311939a6147e339999113db607adc11c7c3cd3103e5e9dbf502898416ecba6b1c7c649c6d4d12941de00cee58b939756bdf20a9efe7d4fa5a5738b73
  languageName: node
  linkType: hard

"marked-terminal@npm:^5.1.1":
  version: 5.2.0
  resolution: "marked-terminal@npm:5.2.0"
  dependencies:
    ansi-escapes: "npm:^6.2.0"
    cardinal: "npm:^2.1.1"
    chalk: "npm:^5.2.0"
    cli-table3: "npm:^0.6.3"
    node-emoji: "npm:^1.11.0"
    supports-hyperlinks: "npm:^2.3.0"
  peerDependencies:
    marked: ^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0
  checksum: 10c0/3f10966cf5c7973453442cf2cf8a5479c68c266723af0de9aa6f0687d40dd30b2820de002bb2c737274223c338ef5fcf1215c7f71092ffa35f448f105713b267
  languageName: node
  linkType: hard

"marked@npm:^4.2.12":
  version: 4.3.0
  resolution: "marked@npm:4.3.0"
  bin:
    marked: bin/marked.js
  checksum: 10c0/0013463855e31b9c88d8bb2891a611d10ef1dc79f2e3cbff1bf71ba389e04c5971298c886af0be799d7fa9aa4593b086a136062d59f1210b0480b026a8c5dc47
  languageName: node
  linkType: hard

"md5@npm:~2.2.0":
  version: 2.2.1
  resolution: "md5@npm:2.2.1"
  dependencies:
    charenc: "npm:~0.0.1"
    crypt: "npm:~0.0.1"
    is-buffer: "npm:~1.1.1"
  checksum: 10c0/e9e7de197a100169f27b956af63ece22348b2d06d40162c8d380d13dcbb7a307c95956857d0cb5ed92059f6448bbdce2d54bc6b922f8e6a36284c303ecc1612d
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: 10c0/d160f31246907e79fed398470285f21bafb45a62869dc469b1c8877f3f064f5eabc4bcc122f9479b8b605bc5c76187d7871cf84c4ee3ecd3e487da1993279928
  languageName: node
  linkType: hard

"media-typer@npm:^1.1.0":
  version: 1.1.0
  resolution: "media-typer@npm:1.1.0"
  checksum: 10c0/7b4baa40b25964bb90e2121ee489ec38642127e48d0cc2b6baa442688d3fde6262bfdca86d6bbf6ba708784afcac168c06840c71facac70e390f5f759ac121b9
  languageName: node
  linkType: hard

"mem@npm:^8.1.1":
  version: 8.1.1
  resolution: "mem@npm:8.1.1"
  dependencies:
    map-age-cleaner: "npm:^0.1.3"
    mimic-fn: "npm:^3.1.0"
  checksum: 10c0/5829c404d024c1accaf76ebacbc7eae9b59e5ce5722d184aa24e8387a8097a499f6aa7e181021003c51eb87b2dcdc9a2270050c58753cce761de206643cba91c
  languageName: node
  linkType: hard

"memfs@npm:^3.4.12, memfs@npm:^3.4.7":
  version: 3.6.0
  resolution: "memfs@npm:3.6.0"
  dependencies:
    fs-monkey: "npm:^1.0.4"
  checksum: 10c0/af567f9038bbb5bbacf100b35d5839e90a89f882d191d8a1c7002faeb224c6cfcebd0e97c0150e9af8be95ec7b5b75a52af56fcd109d0bc18807c1f4e004f053
  languageName: node
  linkType: hard

"memory-cache@npm:~0.2.0":
  version: 0.2.0
  resolution: "memory-cache@npm:0.2.0"
  checksum: 10c0/d4fe58865dfdc252db18ae152ab6c9d62868cfc42d5e7f6cf30732fcf27f5f1f8d7b179c3b6f26f31a28ab1cc5c3937215c60aa9e8ad7ea8ff35e79f69ef14da
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.1":
  version: 1.0.1
  resolution: "merge-descriptors@npm:1.0.1"
  checksum: 10c0/b67d07bd44cfc45cebdec349bb6e1f7b077ee2fd5beb15d1f7af073849208cb6f144fe403e29a36571baf3f4e86469ac39acf13c318381e958e186b2766f54ec
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 10c0/867fdbb30a6d58b011449b8885601ec1690c3e41c759ecd5a9d609094f7aed0096c37823ff4a7190ef0b8f22cc86beb7049196ff68c016e3b3c671d0dac91ce5
  languageName: node
  linkType: hard

"merge2@npm:^1.2.3, merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10c0/254a8a4605b58f450308fc474c82ac9a094848081bf4c06778200207820e5193726dc563a0d2c16468810516a5c97d9d3ea0ca6585d23c58ccfff2403e8dbbeb
  languageName: node
  linkType: hard

"methods@npm:^1.1.2, methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 10c0/bdf7cc72ff0a33e3eede03708c08983c4d7a173f91348b4b1e4f47d4cdbf734433ad971e7d1e8c77247d9e5cd8adb81ea4c67b0a2db526b758b2233d7814b8b2
  languageName: node
  linkType: hard

"micromatch@npm:^3.1.10":
  version: 3.1.10
  resolution: "micromatch@npm:3.1.10"
  dependencies:
    arr-diff: "npm:^4.0.0"
    array-unique: "npm:^0.3.2"
    braces: "npm:^2.3.1"
    define-property: "npm:^2.0.2"
    extend-shallow: "npm:^3.0.2"
    extglob: "npm:^2.0.4"
    fragment-cache: "npm:^0.2.1"
    kind-of: "npm:^6.0.2"
    nanomatch: "npm:^1.2.9"
    object.pick: "npm:^1.3.0"
    regex-not: "npm:^1.0.0"
    snapdragon: "npm:^0.8.1"
    to-regex: "npm:^3.0.2"
  checksum: 10c0/531a32e7ac92bef60657820202be71b63d0f945c08a69cc4c239c0b19372b751483d464a850a2e3a5ff6cc9060641e43d44c303af104c1a27493d137d8af017f
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4":
  version: 4.0.5
  resolution: "micromatch@npm:4.0.5"
  dependencies:
    braces: "npm:^3.0.2"
    picomatch: "npm:^2.3.1"
  checksum: 10c0/3d6505b20f9fa804af5d8c596cb1c5e475b9b0cd05f652c5b56141cf941bd72adaeb7a436fda344235cef93a7f29b7472efc779fcdb83b478eab0867b95cdeff
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:^2.1.35, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/b92cd0adc44888c7135a185bfd0dddc42c32606401c72896a842ae15da71eb88858f17669af41e498b463cd7eb998f7b48939a25b08374c7924a9c8a6f8a81b0
  languageName: node
  linkType: hard

"mime@npm:2.6.0":
  version: 2.6.0
  resolution: "mime@npm:2.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/a7f2589900d9c16e3bdf7672d16a6274df903da958c1643c9c45771f0478f3846dcb1097f31eb9178452570271361e2149310931ec705c037210fc69639c8e6c
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: 10c0/b26f5479d7ec6cc2bce275a08f146cf78f5e7b661b18114e2506dd91ec7ec47e7a25bf4360e5438094db0560bcc868079fb3b1fb3892b833c1ecbf63f80c95a4
  languageName: node
  linkType: hard

"mimic-fn@npm:^3.1.0":
  version: 3.1.0
  resolution: "mimic-fn@npm:3.1.0"
  checksum: 10c0/a07cdd8ed6490c2dff5b11f889b245d9556b80f5a653a552a651d17cff5a2d156e632d235106c2369f00cccef4071704589574cf3601bc1b1400a1f620dff067
  languageName: node
  linkType: hard

"mimic-fn@npm:^4.0.0":
  version: 4.0.0
  resolution: "mimic-fn@npm:4.0.0"
  checksum: 10c0/de9cc32be9996fd941e512248338e43407f63f6d497abe8441fa33447d922e927de54d4cc3c1a3c6d652857acd770389d5a3823f311a744132760ce2be15ccbf
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.5, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10c0/0262810a8fc2e72cca45d6fd86bd349eee435eb95ac6aa45c9ea2180e7ee875ef44c32b55b5973ceabe95ea12682f6e3725cbb63d7a2d1da3ae1163c8b210311
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/3defdfd230914f22a8da203747c42ee3c405c39d4d37ffda284dac5e45b7e1f6c49aa8be606509002898e73091ff2a3bbfc59c2c6c71d4660609f63aa92f98e3
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.1":
  version: 9.0.3
  resolution: "minimatch@npm:9.0.3"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/85f407dcd38ac3e180f425e86553911d101455ca3ad5544d6a7cec16286657e4f8a9aa6695803025c55e31e35a91a2252b5dc8e7d527211278b8b65b4dbd5eac
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 10c0/19d3fcdca050087b84c2029841a093691a91259a47def2f18222f41e7645a0b7c44ef4b40e88a1e58a40c84d2ef0ee6047c55594d298146d0eb3f6b737c20ce6
  languageName: node
  linkType: hard

"minipass-collect@npm:^1.0.2":
  version: 1.0.2
  resolution: "minipass-collect@npm:1.0.2"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/8f82bd1f3095b24f53a991b04b67f4c710c894e518b813f0864a31de5570441a509be1ca17e0bb92b047591a8fdbeb886f502764fefb00d2f144f4011791e898
  languageName: node
  linkType: hard

"minipass-fetch@npm:^3.0.0":
  version: 3.0.3
  resolution: "minipass-fetch@npm:3.0.3"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^5.0.0"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^2.1.2"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/12e0fde7e8fdb1bd923b9243b4788e7d3df305c6ddb3b79ab2da4587fa608c126157c7f6dd43746e8063ee99ec5abbb898d0426c812e9c9b68260c4fea9b279a
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 10c0/a91d8043f691796a8ac88df039da19933ef0f633e3d7f0d35dcd5373af49131cf2399bfc355f41515dc495e3990369c3858cd319e5c2722b4753c90bf3152462
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0":
  version: 7.0.2
  resolution: "minipass@npm:7.0.2"
  checksum: 10c0/5e800acfc9dc75eacac5c4969ab50210463a8afbe8b487de1ae681106e17eb93772513854b6a38462b200b5758af95eeeb481945e050ce76f575ff1150fff4b4
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1, minizlib@npm:^2.1.2":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: "npm:^3.0.0"
    yallist: "npm:^4.0.0"
  checksum: 10c0/64fae024e1a7d0346a1102bb670085b17b7f95bf6cfdf5b128772ec8faf9ea211464ea4add406a3a6384a7d87a0cd1a96263692134323477b4fb43659a6cab78
  languageName: node
  linkType: hard

"mixin-deep@npm:^1.2.0":
  version: 1.3.2
  resolution: "mixin-deep@npm:1.3.2"
  dependencies:
    for-in: "npm:^1.0.2"
    is-extendable: "npm:^1.0.1"
  checksum: 10c0/cb39ffb73c377222391af788b4c83d1a6cecb2d9fceb7015384f8deb46e151a9b030c21ef59a79cb524d4557e3f74c7248ab948a62a6e7e296b42644863d183b
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10c0/46ea0f3ffa8bc6a5bc0c7081ffc3907777f0ed6516888d40a518c5111f8366d97d2678911ad1a6882bf592fa9de6c784fea32e1687bb94e1f4944170af48a5cf
  languageName: node
  linkType: hard

"mrm-core@npm:7.1.13":
  version: 7.1.13
  resolution: "mrm-core@npm:7.1.13"
  dependencies:
    babel-code-frame: "npm:^6.26.0"
    comment-json: "npm:^2.2.0"
    detect-indent: "npm:^6.0.0"
    editorconfig: "npm:^0.15.3"
    find-up: "npm:^4.1.0"
    fs-extra: "npm:^8.1.0"
    kleur: "npm:^3.0.3"
    listify: "npm:^1.0.0"
    lodash: "npm:^4.17.15"
    minimist: "npm:^1.2.0"
    prop-ini: "npm:^0.0.2"
    rc: "npm:^1.2.8"
    readme-badger: "npm:^0.3.0"
    semver: "npm:^6.3.0"
    smpltmpl: "npm:^1.0.2"
    split-lines: "npm:^2.0.0"
    strip-bom: "npm:^4.0.0"
    validate-npm-package-name: "npm:^3.0.0"
    webpack-merge: "npm:^4.2.2"
    yaml: "npm:^2.0.0-1"
  checksum: 10c0/3288b71df7266097518b7d089a91e33199a32cf41335f0cd16ceff9cc9e4a9be075fd737c969a54fb5537b3c768168210eace1af7710af64498932b8d70ff874
  languageName: node
  linkType: hard

"ms@npm:0.7.1":
  version: 0.7.1
  resolution: "ms@npm:0.7.1"
  checksum: 10c0/caeb29c1eef02d38f03781d1937a59ddef897be8686c146507a9d33cda7fa4de678d0c6091f8ce7e45bf35585e655d398d9d5103e5197fd919643c46356887cc
  languageName: node
  linkType: hard

"ms@npm:0.7.2":
  version: 0.7.2
  resolution: "ms@npm:0.7.2"
  checksum: 10c0/f69a17726d1aa329fa37ab4c0d171084149eed599df26a8591944650677f59be19f85e840636c683351ad37fc340a0b65eaea7de68ce96d7c701f19ff56f2305
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 10c0/f8fda810b39fd7255bbdc451c46286e549794fcc700dc9cd1d25658bbc4dc2563a5de6fe7c60f798a16a60c6ceb53f033cb353f493f0cf63e5199b702943159d
  languageName: node
  linkType: hard

"ms@npm:2.1.1":
  version: 2.1.1
  resolution: "ms@npm:2.1.1"
  checksum: 10c0/056140c631e740369fa21142417aba1bd629ab912334715216c666eb681c8f015c622dd4e38bc1d836b30852b05641331661703af13a0397eb0ca420fc1e75d9
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 10c0/a437714e2f90dbf881b5191d35a6db792efbca5badf112f87b9e1c712aace4b4b9b742dd6537f3edf90fd6f684de897cec230abde57e87883766712ddda297cc
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.0.0, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"msgpackr-extract@npm:^3.0.2":
  version: 3.0.2
  resolution: "msgpackr-extract@npm:3.0.2"
  dependencies:
    "@msgpackr-extract/msgpackr-extract-darwin-arm64": "npm:3.0.2"
    "@msgpackr-extract/msgpackr-extract-darwin-x64": "npm:3.0.2"
    "@msgpackr-extract/msgpackr-extract-linux-arm": "npm:3.0.2"
    "@msgpackr-extract/msgpackr-extract-linux-arm64": "npm:3.0.2"
    "@msgpackr-extract/msgpackr-extract-linux-x64": "npm:3.0.2"
    "@msgpackr-extract/msgpackr-extract-win32-x64": "npm:3.0.2"
    node-gyp: "npm:latest"
    node-gyp-build-optional-packages: "npm:5.0.7"
  dependenciesMeta:
    "@msgpackr-extract/msgpackr-extract-darwin-arm64":
      optional: true
    "@msgpackr-extract/msgpackr-extract-darwin-x64":
      optional: true
    "@msgpackr-extract/msgpackr-extract-linux-arm":
      optional: true
    "@msgpackr-extract/msgpackr-extract-linux-arm64":
      optional: true
    "@msgpackr-extract/msgpackr-extract-linux-x64":
      optional: true
    "@msgpackr-extract/msgpackr-extract-win32-x64":
      optional: true
  bin:
    download-msgpackr-prebuilds: bin/download-prebuilds.js
  checksum: 10c0/f14727e0121c241a11cf75824f87822c0a08d65e6b8eba8a3fbf26c7d7287ea3f8ca3ab76887fda781a203bd16e51705207d82593ba6f06abca3181c743a352d
  languageName: node
  linkType: hard

"msgpackr@npm:^1.6.2":
  version: 1.9.5
  resolution: "msgpackr@npm:1.9.5"
  dependencies:
    msgpackr-extract: "npm:^3.0.2"
  dependenciesMeta:
    msgpackr-extract:
      optional: true
  checksum: 10c0/2c114e9521b2e93648840881a82409143ae873a8627982ee5824bfa0d184074998d222e49adf17bc20a0b69f3078e613dc605e088b4ef86d1426badd69d5d510
  languageName: node
  linkType: hard

"mustache@npm:^4.2.0":
  version: 4.2.0
  resolution: "mustache@npm:4.2.0"
  bin:
    mustache: bin/mustache
  checksum: 10c0/1f8197e8a19e63645a786581d58c41df7853da26702dbc005193e2437c98ca49b255345c173d50c08fe4b4dbb363e53cb655ecc570791f8deb09887248dd34a2
  languageName: node
  linkType: hard

"mysql2@npm:^3.5.2":
  version: 3.5.2
  resolution: "mysql2@npm:3.5.2"
  dependencies:
    denque: "npm:^2.1.0"
    generate-function: "npm:^2.3.1"
    iconv-lite: "npm:^0.6.3"
    long: "npm:^5.2.1"
    lru-cache: "npm:^8.0.0"
    named-placeholders: "npm:^1.1.3"
    seq-queue: "npm:^0.0.5"
    sqlstring: "npm:^2.3.2"
  checksum: 10c0/e39867ec328c8214a5f34fd562307ec2ea16c37f90c1970d95c90d12806c5ea4e3004ef866fe6f8c3ca81ca713fec7498a83beb91d84638d85e764a398930639
  languageName: node
  linkType: hard

"named-placeholders@npm:^1.1.3":
  version: 1.1.3
  resolution: "named-placeholders@npm:1.1.3"
  dependencies:
    lru-cache: "npm:^7.14.1"
  checksum: 10c0/cd83b4bbdf358b2285e3c51260fac2039c9d0546632b8a856b3eeabd3bfb3d5b597507ab319b97c281a4a70d748f38bc66fa218a61cb44f55ad997ad5d9c9935
  languageName: node
  linkType: hard

"nanomatch@npm:^1.2.9":
  version: 1.2.13
  resolution: "nanomatch@npm:1.2.13"
  dependencies:
    arr-diff: "npm:^4.0.0"
    array-unique: "npm:^0.3.2"
    define-property: "npm:^2.0.2"
    extend-shallow: "npm:^3.0.2"
    fragment-cache: "npm:^0.2.1"
    is-windows: "npm:^1.0.2"
    kind-of: "npm:^6.0.2"
    object.pick: "npm:^1.3.0"
    regex-not: "npm:^1.0.0"
    snapdragon: "npm:^0.8.1"
    to-regex: "npm:^3.0.1"
  checksum: 10c0/0f5cefa755ca2e20c86332821995effb24acb79551ddaf51c1b9112628cad234a0d8fd9ac6aa56ad1f8bfad6ff6ae86e851acb960943249d9fa44b091479953a
  languageName: node
  linkType: hard

"natural-compare-lite@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare-lite@npm:1.4.0"
  checksum: 10c0/f6cef26f5044515754802c0fc475d81426f3b90fe88c20fabe08771ce1f736ce46e0397c10acb569a4dd0acb84c7f1ee70676122f95d5bfdd747af3a6c6bbaa8
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10c0/f5f9a7974bfb28a91afafa254b197f0f22c684d4a1731763dda960d2c8e375b36c7d690e0d9dc8fba774c537af14a7e979129bca23d88d052fbeb9466955e447
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3, negotiator@npm:^0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 10c0/3ec9fd413e7bf071c937ae60d572bc67155262068ed522cf4b3be5edbe6ddf67d095ec03a3a14ebf8fc8e95f8e1d61be4869db0dbb0de696f6b837358bd43fc2
  languageName: node
  linkType: hard

"nested-error-stacks@npm:^2.0.0, nested-error-stacks@npm:^2.1.0":
  version: 2.1.1
  resolution: "nested-error-stacks@npm:2.1.1"
  checksum: 10c0/feec00417e4778661cfbbe657e6add6ca9918dcc026cd697ac330b4a56a79e4882b36dde8abc138167566b1ce4c5baa17d2d4df727a96f8b96aebace1c3ffca7
  languageName: node
  linkType: hard

"no-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "no-case@npm:3.0.4"
  dependencies:
    lower-case: "npm:^2.0.2"
    tslib: "npm:^2.0.3"
  checksum: 10c0/8ef545f0b3f8677c848f86ecbd42ca0ff3cd9dd71c158527b344c69ba14710d816d8489c746b6ca225e7b615108938a0bda0a54706f8c255933703ac1cf8e703
  languageName: node
  linkType: hard

"node-emoji@npm:^1.11.0":
  version: 1.11.0
  resolution: "node-emoji@npm:1.11.0"
  dependencies:
    lodash: "npm:^4.17.21"
  checksum: 10c0/5dac6502dbef087092d041fcc2686d8be61168593b3a9baf964d62652f55a3a9c2277f171b81cccb851ccef33f2d070f45e633fab1fda3264f8e1ae9041c673f
  languageName: node
  linkType: hard

"node-gyp-build-optional-packages@npm:5.0.7":
  version: 5.0.7
  resolution: "node-gyp-build-optional-packages@npm:5.0.7"
  bin:
    node-gyp-build-optional-packages: bin.js
    node-gyp-build-optional-packages-optional: optional.js
    node-gyp-build-optional-packages-test: build-test.js
  checksum: 10c0/e0edb57358dfa8e31c26b38310ddc5ae81d19fd13b3bf095c41215dfd6a033b1269b510c3ce5e73f7a4ed3d36f101ea47716ec75be38f5e31916d185e7f18905
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 9.4.0
  resolution: "node-gyp@npm:9.4.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    glob: "npm:^7.1.4"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^11.0.3"
    nopt: "npm:^6.0.0"
    npmlog: "npm:^6.0.0"
    rimraf: "npm:^3.0.2"
    semver: "npm:^7.3.5"
    tar: "npm:^6.1.2"
    which: "npm:^2.0.2"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/e8dfbe2b02f23d056f69e01c409381963e92c71cafba6c9cfbf63b038f65ca19ab8183bb6891d080e59c4eb2cc425fc736f42e90afc0f0030ecd97bfc64fb7ad
  languageName: node
  linkType: hard

"node-repl-await@npm:^0.1.2":
  version: 0.1.2
  resolution: "node-repl-await@npm:0.1.2"
  dependencies:
    acorn: "npm:^8.0.5"
    acorn-class-fields: "npm:^1.0.0"
    acorn-private-methods: "npm:^1.0.0"
    acorn-static-class-features: "npm:^1.0.0"
    acorn-walk: "npm:^8.0.2"
  checksum: 10c0/d9b86dbeba2348c8f5817b52a45b0ff8ee0eb28e9e0d445b9fcbc52361c9287fe35e25d728a943e3c3efc8797930aa19fb289fff518ea03df812dddd570485c9
  languageName: node
  linkType: hard

"nopt@npm:^6.0.0":
  version: 6.0.0
  resolution: "nopt@npm:6.0.0"
  dependencies:
    abbrev: "npm:^1.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/837b52c330df16fcaad816b1f54fec6b2854ab1aa771d935c1603fbcf9b023bb073f1466b1b67f48ea4dce127ae675b85b9d9355700e9b109de39db490919786
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10c0/e008c8142bcc335b5e38cf0d63cfd39d6cf2d97480af9abdbe9a439221fd4d749763bab492a8ee708ce7a194bb00c9da6d0a115018672310850489137b3da046
  languageName: node
  linkType: hard

"normalize-url@npm:^6.1.0":
  version: 6.1.0
  resolution: "normalize-url@npm:6.1.0"
  checksum: 10c0/95d948f9bdd2cfde91aa786d1816ae40f8262946e13700bf6628105994fe0ff361662c20af3961161c38a119dc977adeb41fc0b41b1745eb77edaaf9cb22db23
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: "npm:^3.0.0"
  checksum: 10c0/6f9353a95288f8455cf64cbeb707b28826a7f29690244c1e4bb61ec573256e021b6ad6651b394eb1ccfd00d6ec50147253aba2c5fe58a57ceb111fad62c519ac
  languageName: node
  linkType: hard

"npm-run-path@npm:^5.1.0":
  version: 5.1.0
  resolution: "npm-run-path@npm:5.1.0"
  dependencies:
    path-key: "npm:^4.0.0"
  checksum: 10c0/ff6d77514489f47fa1c3b1311d09cd4b6d09a874cc1866260f9dea12cbaabda0436ed7f8c2ee44d147bf99a3af29307c6f63b0f83d242b0b6b0ab25dff2629e3
  languageName: node
  linkType: hard

"npmlog@npm:^6.0.0":
  version: 6.0.2
  resolution: "npmlog@npm:6.0.2"
  dependencies:
    are-we-there-yet: "npm:^3.0.0"
    console-control-strings: "npm:^1.1.0"
    gauge: "npm:^4.0.3"
    set-blocking: "npm:^2.0.0"
  checksum: 10c0/0cacedfbc2f6139c746d9cd4a85f62718435ad0ca4a2d6459cd331dd33ae58206e91a0742c1558634efcde3f33f8e8e7fd3adf1bfe7978310cf00bd55cccf890
  languageName: node
  linkType: hard

"object-component@npm:0.0.3":
  version: 0.0.3
  resolution: "object-component@npm:0.0.3"
  checksum: 10c0/49a67086176e04ea684b76db24e8b871041e558c6d7e3b084ab46cc9d38756aff5fab0d0166604c6869e113e9ca49c372ad85b473863eeeadb6ec959f8cfa9bb
  languageName: node
  linkType: hard

"object-copy@npm:^0.1.0":
  version: 0.1.0
  resolution: "object-copy@npm:0.1.0"
  dependencies:
    copy-descriptor: "npm:^0.1.0"
    define-property: "npm:^0.2.5"
    kind-of: "npm:^3.0.3"
  checksum: 10c0/79314b05e9d626159a04f1d913f4c4aba9eae8848511cf5f4c8e3b04bb3cc313b65f60357f86462c959a14c2d58380fedf89b6b32ecec237c452a5ef3900a293
  languageName: node
  linkType: hard

"object-inspect@npm:^1.9.0":
  version: 1.12.3
  resolution: "object-inspect@npm:1.12.3"
  checksum: 10c0/752bb5f4dc595e214157ea8f442adb77bdb850ace762b078d151d8b6486331ab12364997a89ee6509be1023b15adf2b3774437a7105f8a5043dfda11ed622411
  languageName: node
  linkType: hard

"object-visit@npm:^1.0.0":
  version: 1.0.1
  resolution: "object-visit@npm:1.0.1"
  dependencies:
    isobject: "npm:^3.0.0"
  checksum: 10c0/086b475bda24abd2318d2b187c3e928959b89f5cb5883d6fe5a42d03719b61fc18e765f658de9ac8730e67ba9ff26d61e73d991215948ff9ecefe771e0071029
  languageName: node
  linkType: hard

"object.pick@npm:^1.3.0":
  version: 1.3.0
  resolution: "object.pick@npm:1.3.0"
  dependencies:
    isobject: "npm:^3.0.1"
  checksum: 10c0/cd316ec986e49895a28f2df9182de9cdeee57cd2a952c122aacc86344c28624fe002d9affc4f48b5014ec7c033da9942b08821ddb44db8c5bac5b3ec54bdc31e
  languageName: node
  linkType: hard

"on-exit-leak-free@npm:^2.1.0":
  version: 2.1.0
  resolution: "on-exit-leak-free@npm:2.1.0"
  checksum: 10c0/66cf10b270ffd4df593efd75f9122a16980672cb583380440e44f96db1c41607c42b8986e96f1563c769daed3af270c240e1195fc6e6393da1da5c36233d848b
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1, on-finished@npm:^2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10c0/46fb11b9063782f2d9968863d9cbba33d77aa13c17f895f56129c274318b86500b22af3a160fe9995aa41317efcd22941b6eba747f718ced08d9a73afdb087b4
  languageName: node
  linkType: hard

"on-finished@npm:~2.3.0":
  version: 2.3.0
  resolution: "on-finished@npm:2.3.0"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10c0/c904f9e518b11941eb60279a3cbfaf1289bd0001f600a950255b1dede9fe3df8cd74f38483550b3bb9485165166acb5db500c3b4c4337aec2815c88c96fcc2ea
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10c0/5d48aca287dfefabd756621c5dfce5c91a549a93e9fdb7b8246bc4c4790aa2ec17b34a260530474635147aeb631a2dcc8b32c613df0675f96041cbb8244517d0
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0, onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: "npm:^2.1.0"
  checksum: 10c0/ffcef6fbb2692c3c40749f31ea2e22677a876daea92959b8a80b521d95cca7a668c884d8b2045d1d8ee7d56796aa405c405462af112a1477594cc63531baeb8f
  languageName: node
  linkType: hard

"onetime@npm:^6.0.0":
  version: 6.0.0
  resolution: "onetime@npm:6.0.0"
  dependencies:
    mimic-fn: "npm:^4.0.0"
  checksum: 10c0/4eef7c6abfef697dd4479345a4100c382d73c149d2d56170a54a07418c50816937ad09500e1ed1e79d235989d073a9bade8557122aee24f0576ecde0f392bb6c
  languageName: node
  linkType: hard

"open@npm:^8.4.2":
  version: 8.4.2
  resolution: "open@npm:8.4.2"
  dependencies:
    define-lazy-prop: "npm:^2.0.0"
    is-docker: "npm:^2.1.1"
    is-wsl: "npm:^2.2.0"
  checksum: 10c0/bb6b3a58401dacdb0aad14360626faf3fb7fba4b77816b373495988b724fb48941cad80c1b65d62bb31a17609b2cd91c41a181602caea597ca80dfbcc27e84c9
  languageName: node
  linkType: hard

"open@npm:^9.1.0":
  version: 9.1.0
  resolution: "open@npm:9.1.0"
  dependencies:
    default-browser: "npm:^4.0.0"
    define-lazy-prop: "npm:^3.0.0"
    is-inside-container: "npm:^1.0.0"
    is-wsl: "npm:^2.2.0"
  checksum: 10c0/8073ec0dd8994a7a7d9bac208bd17d093993a65ce10f2eb9b62b6d3a91c9366ae903938a237c275493c130171d339f6dcbdd2a2de7e32953452c0867b97825af
  languageName: node
  linkType: hard

"openapi-schema-validator@npm:^3.0.3":
  version: 3.0.3
  resolution: "openapi-schema-validator@npm:3.0.3"
  dependencies:
    ajv: "npm:^6.5.2"
    lodash.merge: "npm:^4.6.1"
    openapi-types: "npm:1.3.4"
    swagger-schema-official: "npm:2.0.0-bab6bed"
  checksum: 10c0/d384a4efa3f40a3ad49da53bcb0e30714f5ca6361055ed1726c61fbf1977b13161c76ae64edd62e712fe226d7e37e728c5f24b5690f374066547de00112f95b6
  languageName: node
  linkType: hard

"openapi-types@npm:1.3.4":
  version: 1.3.4
  resolution: "openapi-types@npm:1.3.4"
  checksum: 10c0/11e80593046af05ded9c29935400fcd95dca9e11c4a96a0ddc2ca3bbee64f874734075b59b3d184c436862f948848048234bcfc6ad0c964889a5b3de8fbfa058
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.3
  resolution: "optionator@npm:0.9.3"
  dependencies:
    "@aashutoshrathi/word-wrap": "npm:^1.2.3"
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
  checksum: 10c0/66fba794d425b5be51353035cf3167ce6cfa049059cbb93229b819167687e0f48d2bc4603fcb21b091c99acb516aae1083624675b15c4765b2e4693a085e959c
  languageName: node
  linkType: hard

"options@npm:>=0.0.5":
  version: 0.0.6
  resolution: "options@npm:0.0.6"
  checksum: 10c0/864945aabe0e132f1fc2b290d1615f6022d48fd789580f91adf8a2a31d36b7c0f93b09c28d2c7b2dc3057e7c7fc7d608cbbe8e9b1b3f5b332d1a023afaca4f04
  languageName: node
  linkType: hard

"p-all@npm:^2.1.0":
  version: 2.1.0
  resolution: "p-all@npm:2.1.0"
  dependencies:
    p-map: "npm:^2.0.0"
  checksum: 10c0/874eafa2e3f38b258f8beed34549befbc8a52a63818e0981b8beff03f592e1e1f47b8aab2483f844f2745815ffa010def58bf1edbc95614466c55411f02f3049
  languageName: node
  linkType: hard

"p-defer@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-defer@npm:1.0.0"
  checksum: 10c0/ed603c3790e74b061ac2cb07eb6e65802cf58dce0fbee646c113a7b71edb711101329ad38f99e462bd2e343a74f6e9366b496a35f1d766c187084d3109900487
  languageName: node
  linkType: hard

"p-event@npm:^4.1.0":
  version: 4.2.0
  resolution: "p-event@npm:4.2.0"
  dependencies:
    p-timeout: "npm:^3.1.0"
  checksum: 10c0/f1b6a2fb13d47f2a8afc00150da5ece0d28940ce3d8fa562873e091d3337d298e78fee9cb18b768598ff1d11df608b2ae23868309ff6405b864a2451ccd6d25a
  languageName: node
  linkType: hard

"p-filter@npm:^2.1.0":
  version: 2.1.0
  resolution: "p-filter@npm:2.1.0"
  dependencies:
    p-map: "npm:^2.0.0"
  checksum: 10c0/5ac34b74b3b691c04212d5dd2319ed484f591c557a850a3ffc93a08cb38c4f5540be059c6b10a185773c479ca583a91ea00c7d6c9958c815e6b74d052f356645
  languageName: node
  linkType: hard

"p-finally@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-finally@npm:1.0.0"
  checksum: 10c0/6b8552339a71fe7bd424d01d8451eea92d379a711fc62f6b2fe64cad8a472c7259a236c9a22b4733abca0b5666ad503cb497792a0478c5af31ded793d00937e7
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: "npm:^2.0.0"
  checksum: 10c0/8da01ac53efe6a627080fafc127c873da40c18d87b3f5d5492d465bb85ec7207e153948df6b9cbaeb130be70152f874229b8242ee2be84c0794082510af97f12
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10c0/9db675949dbdc9c3763c89e748d0ef8bdad0afbb24d49ceaf4c46c02c77d30db4e0652ed36d0a0a7a95154335fab810d95c86153105bb73b3a90448e2bb14e1a
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: "npm:^2.2.0"
  checksum: 10c0/1b476ad69ad7f6059744f343b26d51ce091508935c1dbb80c4e0a2f397ffce0ca3a1f9f5cd3c7ce19d7929a09719d5c65fe70d8ee289c3f267cd36f2881813e9
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: "npm:^3.0.2"
  checksum: 10c0/2290d627ab7903b8b70d11d384fee714b797f6040d9278932754a6860845c4d3190603a0772a663c8cb5a7b21d1b16acb3a6487ebcafa9773094edc3dfe6009a
  languageName: node
  linkType: hard

"p-map@npm:^2.0.0":
  version: 2.1.0
  resolution: "p-map@npm:2.1.0"
  checksum: 10c0/735dae87badd4737a2dd582b6d8f93e49a1b79eabbc9815a4d63a528d5e3523e978e127a21d784cccb637010e32103a40d2aaa3ab23ae60250b1a820ca752043
  languageName: node
  linkType: hard

"p-map@npm:^3.0.0":
  version: 3.0.0
  resolution: "p-map@npm:3.0.0"
  dependencies:
    aggregate-error: "npm:^3.0.0"
  checksum: 10c0/297930737e52412ad9f5787c52774ad6496fad9a8be5f047e75fd0a3dc61930d8f7a9b2bbe1c4d1404e54324228a4f69721da2538208dadaa4ef4c81773c9f20
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: "npm:^3.0.0"
  checksum: 10c0/592c05bd6262c466ce269ff172bb8de7c6975afca9b50c975135b974e9bdaafbfe80e61aaaf5be6d1200ba08b30ead04b88cfa7e25ff1e3b93ab28c9f62a2c75
  languageName: node
  linkType: hard

"p-timeout@npm:^3.1.0":
  version: 3.2.0
  resolution: "p-timeout@npm:3.2.0"
  dependencies:
    p-finally: "npm:^1.0.0"
  checksum: 10c0/524b393711a6ba8e1d48137c5924749f29c93d70b671e6db761afa784726572ca06149c715632da8f70c090073afb2af1c05730303f915604fd38ee207b70a61
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: 10c0/c36c19907734c904b16994e6535b02c36c2224d433e01a2f1ab777237f4d86e6289fd5fd464850491e940379d4606ed850c03e0f9ab600b0ebddb511312e177f
  languageName: node
  linkType: hard

"param-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "param-case@npm:3.0.4"
  dependencies:
    dot-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10c0/ccc053f3019f878eca10e70ec546d92f51a592f762917dafab11c8b532715dcff58356118a6f350976e4ab109e321756f05739643ed0ca94298e82291e6f9e76
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10c0/c63d6e80000d4babd11978e0d3fee386ca7752a02b035fd2435960ffaa7219dc42146f07069fb65e6e8bf1caef89daf9af7535a39bddf354d78bf50d8294f556
  languageName: node
  linkType: hard

"parent-module@npm:^2.0.0":
  version: 2.0.0
  resolution: "parent-module@npm:2.0.0"
  dependencies:
    callsites: "npm:^3.1.0"
  checksum: 10c0/e4c5e34102c709df1932e1065dee53764fbd869f5a673beb8c3b4bcbbd4a7be16e3595f8846b24f52a77b9e96d8d499e68736ec690b108e55d95a5315f41e073
  languageName: node
  linkType: hard

"parse-imports@npm:0.0.5":
  version: 0.0.5
  resolution: "parse-imports@npm:0.0.5"
  dependencies:
    es-module-lexer: "npm:0.3.26"
    slashes: "npm:2.0.2"
  checksum: 10c0/c4d8b544ccdfc68210412d62036e28f8619db1b426697369628af49434ec6c59c9e6da52786a41b063a231abb7a636a5931529f157e01494aea3c3b3535e6880
  languageName: node
  linkType: hard

"parsejson@npm:0.0.3":
  version: 0.0.3
  resolution: "parsejson@npm:0.0.3"
  dependencies:
    better-assert: "npm:~1.0.0"
  checksum: 10c0/eafc71980a8900b256aa526f9ae9b9547fec02f786e620142b010a481e06a70db989a5d0ab4954308430ad08141a881264d798c7873e262f118cbfe100373224
  languageName: node
  linkType: hard

"parseqs@npm:0.0.5":
  version: 0.0.5
  resolution: "parseqs@npm:0.0.5"
  dependencies:
    better-assert: "npm:~1.0.0"
  checksum: 10c0/2290c5de49a54b6b39f6647f794f68ecf8e3e556a73e2c549f0adad8bda5b5e054cb187fe7e8714dc2d3ce315d00fe25ba8705847ae0bf4c2e9073a3f6d7675b
  languageName: node
  linkType: hard

"parseuri@npm:0.0.5":
  version: 0.0.5
  resolution: "parseuri@npm:0.0.5"
  dependencies:
    better-assert: "npm:~1.0.0"
  checksum: 10c0/605d075c504d4d9455f0f8f150026ae9718483d3c797fa0c7909dbb8bc6ce5a0cc6285d3ef126fea1f53478a4f039cb60cbe2b50051155049b8037588a9435b5
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 10c0/90dd4760d6f6174adb9f20cf0965ae12e23879b5f5464f38e92fce8073354341e4b3b76fa3d878351efe7d01e617121955284cfd002ab087fba1a0726ec0b4f5
  languageName: node
  linkType: hard

"pascal-case@npm:^3.1.2":
  version: 3.1.2
  resolution: "pascal-case@npm:3.1.2"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10c0/05ff7c344809fd272fc5030ae0ee3da8e4e63f36d47a1e0a4855ca59736254192c5a27b5822ed4bae96e54048eec5f6907713cfcfff7cdf7a464eaf7490786d8
  languageName: node
  linkType: hard

"pascalcase@npm:^0.1.1":
  version: 0.1.1
  resolution: "pascalcase@npm:0.1.1"
  checksum: 10c0/48dfe90618e33810bf58211d8f39ad2c0262f19ad6354da1ba563935b5f429f36409a1fb9187c220328f7a4dc5969917f8e3e01ee089b5f1627b02aefe39567b
  languageName: node
  linkType: hard

"path-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "path-case@npm:3.0.4"
  dependencies:
    dot-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10c0/b6b14637228a558793f603aaeb2fcd981e738b8b9319421b713532fba96d75aa94024b9f6b9ae5aa33d86755144a5b36697d28db62ae45527dbd672fcc2cf0b7
  languageName: node
  linkType: hard

"path-dirname@npm:^1.0.0":
  version: 1.0.2
  resolution: "path-dirname@npm:1.0.2"
  checksum: 10c0/71e59be2bada7c91f62b976245fd421b7cb01fde3207fe53a82d8880621ad04fd8b434e628c9cf4e796259fc168a107d77cd56837725267c5b2c58cefe2c4e1b
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10c0/8c0bd3f5238188197dc78dced15207a4716c51cc4e3624c44fc97acf69558f5ebb9a2afff486fe1b4ee148e0c133e96c5e11a9aa5c48a3006e3467da070e5e1b
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10c0/127da03c82172a2a50099cddbf02510c1791fc2cc5f7713ddb613a56838db1e8168b121a920079d052e0936c23005562059756d653b7c544c53185efe53be078
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-key@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-key@npm:4.0.0"
  checksum: 10c0/794efeef32863a65ac312f3c0b0a99f921f3e827ff63afa5cb09a377e202c262b671f7b3832a4e64731003fa94af0263713962d317b9887bd1e0c48a342efba3
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10c0/11ce261f9d294cc7a58d6a574b7f1b935842355ec66fba3c3fd79e0f036462eaf07d0aa95bb74ff432f9afef97ce1926c720988c6a7451d8a584930ae7de86e1
  languageName: node
  linkType: hard

"path-scurry@npm:^1.10.1":
  version: 1.10.1
  resolution: "path-scurry@npm:1.10.1"
  dependencies:
    lru-cache: "npm:^9.1.1 || ^10.0.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/e5dc78a7348d25eec61ab166317e9e9c7b46818aa2c2b9006c507a6ff48c672d011292d9662527213e558f5652ce0afcc788663a061d8b59ab495681840c0c1e
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.7":
  version: 0.1.7
  resolution: "path-to-regexp@npm:0.1.7"
  checksum: 10c0/50a1ddb1af41a9e68bd67ca8e331a705899d16fb720a1ea3a41e310480948387daf603abb14d7b0826c58f10146d49050a1291ba6a82b78a382d1c02c0b8f905
  languageName: node
  linkType: hard

"path-type@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-type@npm:3.0.0"
  dependencies:
    pify: "npm:^3.0.0"
  checksum: 10c0/1332c632f1cac15790ebab8dd729b67ba04fc96f81647496feb1c2975d862d046f41e4b975dbd893048999b2cc90721f72924ad820acc58c78507ba7141a8e56
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 10c0/666f6973f332f27581371efaf303fd6c272cc43c2057b37aa99e3643158c7e4b2626549555d88626e99ea9e046f82f32e41bbde5f1508547e9a11b149b52387c
  languageName: node
  linkType: hard

"pathval@npm:^1.1.1":
  version: 1.1.1
  resolution: "pathval@npm:1.1.1"
  checksum: 10c0/f63e1bc1b33593cdf094ed6ff5c49c1c0dc5dc20a646ca9725cc7fe7cd9995002d51d5685b9b2ec6814342935748b711bafa840f84c0bb04e38ff40a335c94dc
  languageName: node
  linkType: hard

"peek-readable@npm:^4.1.0":
  version: 4.1.0
  resolution: "peek-readable@npm:4.1.0"
  checksum: 10c0/f9b81ce3eed185cc9ebbf7dff0b6e130dd6da7b05f1802bbf726a78e4d84990b0a65f8e701959c50eb1124cc2ad352205147954bf39793faba29bb00ce742a44
  languageName: node
  linkType: hard

"pg-connection-string@npm:2.6.1":
  version: 2.6.1
  resolution: "pg-connection-string@npm:2.6.1"
  checksum: 10c0/e5a71a2da143b8dc17143a9db7737679b210643771aa678d3bc60c7bc70da11bbb8e2d531be91c8c4eddd6ac6046307811e793f5850b9ba595a11785c948a417
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0":
  version: 1.0.0
  resolution: "picocolors@npm:1.0.0"
  checksum: 10c0/20a5b249e331c14479d94ec6817a182fd7a5680debae82705747b2db7ec50009a5f6648d0621c561b0572703f84dbef0858abcbd5856d3c5511426afcb1961f7
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10c0/26c02b8d06f03206fc2ab8d16f19960f2ff9e81a658f831ecb656d8f17d9edc799e8364b1f4a7873e89d9702dff96204be0fa26fe4181f6843f040f819dac4be
  languageName: node
  linkType: hard

"pify@npm:^3.0.0":
  version: 3.0.0
  resolution: "pify@npm:3.0.0"
  checksum: 10c0/fead19ed9d801f1b1fcd0638a1ac53eabbb0945bf615f2f8806a8b646565a04a1b0e7ef115c951d225f042cca388fdc1cd3add46d10d1ed6951c20bd2998af10
  languageName: node
  linkType: hard

"pify@npm:^4.0.1":
  version: 4.0.1
  resolution: "pify@npm:4.0.1"
  checksum: 10c0/6f9d404b0d47a965437403c9b90eca8bb2536407f03de165940e62e72c8c8b75adda5516c6b9b23675a5877cc0bcac6bdfb0ef0e39414cd2476d5495da40e7cf
  languageName: node
  linkType: hard

"pino-abstract-transport@npm:^1.0.0, pino-abstract-transport@npm:v1.0.0":
  version: 1.0.0
  resolution: "pino-abstract-transport@npm:1.0.0"
  dependencies:
    readable-stream: "npm:^4.0.0"
    split2: "npm:^4.0.0"
  checksum: 10c0/c7a68e4f77f47565d39fdc33ba23f388ad2594b26a12ccce3060fef833cd7803db73a0c6e4fa7129616f22eb762119ed6107450e0f8ca1e3745d4993017725f7
  languageName: node
  linkType: hard

"pino-pretty@npm:*, pino-pretty@npm:^10.1.0":
  version: 10.2.0
  resolution: "pino-pretty@npm:10.2.0"
  dependencies:
    colorette: "npm:^2.0.7"
    dateformat: "npm:^4.6.3"
    fast-copy: "npm:^3.0.0"
    fast-safe-stringify: "npm:^2.1.1"
    help-me: "npm:^4.0.1"
    joycon: "npm:^3.1.1"
    minimist: "npm:^1.2.6"
    on-exit-leak-free: "npm:^2.1.0"
    pino-abstract-transport: "npm:^1.0.0"
    pump: "npm:^3.0.0"
    readable-stream: "npm:^4.0.0"
    secure-json-parse: "npm:^2.4.0"
    sonic-boom: "npm:^3.0.0"
    strip-json-comments: "npm:^3.1.1"
  bin:
    pino-pretty: bin.js
  checksum: 10c0/5259cf3ceb9e59b604f1c7a18d854b9bf6b4092ddf14d7dc11e04d34643f43b86bd398c0a579e70b10ecf34281103cfce01bfd82ab2cd482d28596ad7122558e
  languageName: node
  linkType: hard

"pino-std-serializers@npm:*, pino-std-serializers@npm:^6.0.0":
  version: 6.2.2
  resolution: "pino-std-serializers@npm:6.2.2"
  checksum: 10c0/8f1c7f0f0d8f91e6c6b5b2a6bfb48f06441abeb85f1c2288319f736f9c6d814fbeebe928d2314efc2ba6018fa7db9357a105eca9fc99fc1f28945a8a8b28d3d5
  languageName: node
  linkType: hard

"pino-std-serializers@npm:^3.1.0":
  version: 3.2.0
  resolution: "pino-std-serializers@npm:3.2.0"
  checksum: 10c0/ae08159372b5bbe69f13770a7f20ba7ded0bb97b2c6f42f780995582135ca907e66504f06371c12f991dbfcd489280f942786c02a9e8e952974d455cb0a477c9
  languageName: node
  linkType: hard

"pino@npm:^6.14.0":
  version: 6.14.0
  resolution: "pino@npm:6.14.0"
  dependencies:
    fast-redact: "npm:^3.0.0"
    fast-safe-stringify: "npm:^2.0.8"
    flatstr: "npm:^1.0.12"
    pino-std-serializers: "npm:^3.1.0"
    process-warning: "npm:^1.0.0"
    quick-format-unescaped: "npm:^4.0.3"
    sonic-boom: "npm:^1.0.2"
  bin:
    pino: bin.js
  checksum: 10c0/5d3cb22c804e2bf2439ace64a46a7901d0a138cb75715ad8a8bbcf3ddb09dc5e33a9fc8a49527c3345d317619748c6de94d28481911ae931c21b953e24048425
  languageName: node
  linkType: hard

"pino@npm:^8.14.1":
  version: 8.14.1
  resolution: "pino@npm:8.14.1"
  dependencies:
    atomic-sleep: "npm:^1.0.0"
    fast-redact: "npm:^3.1.1"
    on-exit-leak-free: "npm:^2.1.0"
    pino-abstract-transport: "npm:v1.0.0"
    pino-std-serializers: "npm:^6.0.0"
    process-warning: "npm:^2.0.0"
    quick-format-unescaped: "npm:^4.0.3"
    real-require: "npm:^0.2.0"
    safe-stable-stringify: "npm:^2.3.1"
    sonic-boom: "npm:^3.1.0"
    thread-stream: "npm:^2.0.0"
  bin:
    pino: bin.js
  checksum: 10c0/0903238577141814b7bcab0278e0a249078e6cf8198630c9bb7a0dfba555ea303660632ea289d9bd721d8b964939adbfe90fd5a39dee7a6c9f967c0c5e3df8ae
  languageName: node
  linkType: hard

"pirates@npm:^4.0.5":
  version: 4.0.6
  resolution: "pirates@npm:4.0.6"
  checksum: 10c0/00d5fa51f8dded94d7429700fb91a0c1ead00ae2c7fd27089f0c5b63e6eca36197fe46384631872690a66f390c5e27198e99006ab77ae472692ab9c2ca903f36
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.1.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: "npm:^4.0.0"
  checksum: 10c0/c56bda7769e04907a88423feb320babaed0711af8c436ce3e56763ab1021ba107c7b0cafb11cde7529f669cfc22bffcaebffb573645cbd63842ea9fb17cd7728
  languageName: node
  linkType: hard

"pluralize@npm:^8.0.0":
  version: 8.0.0
  resolution: "pluralize@npm:8.0.0"
  checksum: 10c0/2044cfc34b2e8c88b73379ea4a36fc577db04f651c2909041b054c981cd863dd5373ebd030123ab058d194ae615d3a97cfdac653991e499d10caf592e8b3dc33
  languageName: node
  linkType: hard

"posix-character-classes@npm:^0.1.0":
  version: 0.1.1
  resolution: "posix-character-classes@npm:0.1.1"
  checksum: 10c0/cce88011548a973b4af58361cd8f5f7b5a6faff8eef0901565802f067bcabf82597e920d4c97c22068464be3cbc6447af589f6cc8a7d813ea7165be60a0395bc
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 10c0/b00d617431e7886c520a6f498a2e14c75ec58f6d93ba48c3b639cf241b54232d90daa05d83a9e9b9fef6baa63cb7e1e4602c2372fea5bc169668401eb127d0cd
  languageName: node
  linkType: hard

"prettier-linter-helpers@npm:^1.0.0":
  version: 1.0.0
  resolution: "prettier-linter-helpers@npm:1.0.0"
  dependencies:
    fast-diff: "npm:^1.1.2"
  checksum: 10c0/81e0027d731b7b3697ccd2129470ed9913ecb111e4ec175a12f0fcfab0096516373bf0af2fef132af50cafb0a905b74ff57996d615f59512bb9ac7378fcc64ab
  languageName: node
  linkType: hard

"prettier@npm:^3.0.0":
  version: 3.0.0
  resolution: "prettier@npm:3.0.0"
  bin:
    prettier: bin/prettier.cjs
  checksum: 10c0/113e2828c6487f10e19e7c30598e8810684b827f2a1b7747f113728d1049da15583b77cc5e907f37b371b08c49cfdd900aebe5c2a2077be3f0cccb73cca1133b
  languageName: node
  linkType: hard

"pretty-format@npm:^25.5.0":
  version: 25.5.0
  resolution: "pretty-format@npm:25.5.0"
  dependencies:
    "@jest/types": "npm:^25.5.0"
    ansi-regex: "npm:^5.0.0"
    ansi-styles: "npm:^4.0.0"
    react-is: "npm:^16.12.0"
  checksum: 10c0/cbcf79f57a96f5eb9970722614a360539940606a20a924f6202e309433af4ad5b71ba210b6b3efcdcdad178f9aefa74f04a447d86520d721fbe155ff43b33112
  languageName: node
  linkType: hard

"pretty-format@npm:^29.6.1":
  version: 29.6.1
  resolution: "pretty-format@npm:29.6.1"
  dependencies:
    "@jest/schemas": "npm:^29.6.0"
    ansi-styles: "npm:^5.0.0"
    react-is: "npm:^18.0.0"
  checksum: 10c0/decb4ca86b34e53a08e525d2b50be19ef4bffa4bb4122787740b012c11490311879de53dee8b669a82376b6fec06040ec546831f2c3ce0df963c00d743cce664
  languageName: node
  linkType: hard

"pretty-hrtime@npm:^1.0.3":
  version: 1.0.3
  resolution: "pretty-hrtime@npm:1.0.3"
  checksum: 10c0/67cb3fc283a72252b49ac488647e6a01b78b7aa1b8f2061834aa1650691229081518ef3ca940f77f41cc8a8f02ba9eeb74b843481596670209e493062f2e89e0
  languageName: node
  linkType: hard

"printable-characters@npm:^1.0.42":
  version: 1.0.42
  resolution: "printable-characters@npm:1.0.42"
  checksum: 10c0/7c94d94c6041a37c385af770c7402ad5a2e8a3429ca4d2505a9f19fde39bac9a8fd1edfbfa02f1eae5b4b0f3536b6b8ee6c84621f7c0fcb41476b2df6ee20e4b
  languageName: node
  linkType: hard

"process-warning@npm:^1.0.0":
  version: 1.0.0
  resolution: "process-warning@npm:1.0.0"
  checksum: 10c0/43ec4229d64eb5c58340c8aacade49eb5f6fd513eae54140abf365929ca20987f0a35c5868125e2b583cad4de8cd257beb5667d9cc539d9190a7a4c3014adf22
  languageName: node
  linkType: hard

"process-warning@npm:^2.0.0":
  version: 2.2.0
  resolution: "process-warning@npm:2.2.0"
  checksum: 10c0/22b252ca6c1edf7fe3c6ab30c39f9a2fa240dc5af46fd0f94c4dcbc577e7570dcccfc1cbfb4510db4759906b9170cb8b18c519d581cdf2ea649e5ac6bb9a0e60
  languageName: node
  linkType: hard

"process@npm:^0.11.10":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: 10c0/40c3ce4b7e6d4b8c3355479df77aeed46f81b279818ccdc500124e6a5ab882c0cc81ff7ea16384873a95a74c4570b01b120f287abbdd4c877931460eca6084b3
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"prop-ini@npm:^0.0.2":
  version: 0.0.2
  resolution: "prop-ini@npm:0.0.2"
  dependencies:
    extend: "npm:^3.0.0"
  checksum: 10c0/b8dc8fb751d3d8a1a00275b370445f8b426a481873df9801ac59985ea0c313f0c0e6c3f8ac6747715c62a803f2b88f0c387db3f650f0e8402fbb9d033fb4337b
  languageName: node
  linkType: hard

"proxy-addr@npm:^2.0.7, proxy-addr@npm:~2.0.5":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: "npm:0.2.0"
    ipaddr.js: "npm:1.9.1"
  checksum: 10c0/c3eed999781a35f7fd935f398b6d8920b6fb00bbc14287bc6de78128ccc1a02c89b95b56742bf7cf0362cc333c61d138532049c7dedc7a328ef13343eff81210
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: 10c0/fe7dd8b1bdbbbea18d1459107729c3e4a2243ca870d26d34c2c1bcd3e4425b7bcc5112362df2d93cc7fb9746f6142b5e272fd1cc5c86ddf8580175186f6ad42b
  languageName: node
  linkType: hard

"pseudomap@npm:^1.0.2":
  version: 1.0.2
  resolution: "pseudomap@npm:1.0.2"
  checksum: 10c0/5a91ce114c64ed3a6a553aa7d2943868811377388bb31447f9d8028271bae9b05b340fe0b6961a64e45b9c72946aeb0a4ab635e8f7cb3715ffd0ff2beeb6a679
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.0
  resolution: "pump@npm:3.0.0"
  dependencies:
    end-of-stream: "npm:^1.1.0"
    once: "npm:^1.3.1"
  checksum: 10c0/bbdeda4f747cdf47db97428f3a135728669e56a0ae5f354a9ac5b74556556f5446a46f720a8f14ca2ece5be9b4d5d23c346db02b555f46739934cc6c093a5478
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.0
  resolution: "punycode@npm:2.3.0"
  checksum: 10c0/8e6f7abdd3a6635820049e3731c623bbef3fedbf63bbc696b0d7237fdba4cefa069bc1fa62f2938b0fbae057550df7b5318f4a6bcece27f1907fc75c54160bee
  languageName: node
  linkType: hard

"qs@npm:6.7.0":
  version: 6.7.0
  resolution: "qs@npm:6.7.0"
  checksum: 10c0/04e6934d8cfa4f352e5bf5fe16eeed75dccad16d1e03b53ece849839b7439940f0df8bf0bc4750306d65baf95ebe165315f61122067e33bfee7b7ef4e3945813
  languageName: node
  linkType: hard

"qs@npm:^6.11.0, qs@npm:^6.11.2":
  version: 6.11.2
  resolution: "qs@npm:6.11.2"
  dependencies:
    side-channel: "npm:^1.0.4"
  checksum: 10c0/4f95d4ff18ed480befcafa3390022817ffd3087fc65f146cceb40fc5edb9fa96cb31f648cae2fa96ca23818f0798bd63ad4ca369a0e22702fcd41379b3ab6571
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10c0/900a93d3cdae3acd7d16f642c29a642aea32c2026446151f0778c62ac089d4b8e6c986811076e1ae180a694cedf077d453a11b58ff0a865629a4f82ab558e102
  languageName: node
  linkType: hard

"quick-format-unescaped@npm:^4.0.3":
  version: 4.0.4
  resolution: "quick-format-unescaped@npm:4.0.4"
  checksum: 10c0/fe5acc6f775b172ca5b4373df26f7e4fd347975578199e7d74b2ae4077f0af05baa27d231de1e80e8f72d88275ccc6028568a7a8c9ee5e7368ace0e18eff93a4
  languageName: node
  linkType: hard

"random-bytes@npm:~1.0.0":
  version: 1.0.0
  resolution: "random-bytes@npm:1.0.0"
  checksum: 10c0/71e7a600e0976e9ebc269793a0577d47b965fa678fcc9e9623e427f909d1b3669db5b3a178dbf61229f0724ea23dba64db389f0be0ba675c6a6b837c02f29b8f
  languageName: node
  linkType: hard

"range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 10c0/96c032ac2475c8027b7a4e9fe22dc0dfe0f6d90b85e496e0f016fbdb99d6d066de0112e680805075bd989905e2123b3b3d002765149294dce0c1f7f01fcc2ea0
  languageName: node
  linkType: hard

"raw-body@npm:2.4.0":
  version: 2.4.0
  resolution: "raw-body@npm:2.4.0"
  dependencies:
    bytes: "npm:3.1.0"
    http-errors: "npm:1.7.2"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: 10c0/c7ff86d9d4a91f0d9ab3e2eb45b2197d2534e0f24fded16989085fe71207539f63100a6fd49507a5ff1907ff38511e510a3e6098102b9e8711cd84d7344a703a
  languageName: node
  linkType: hard

"raw-body@npm:^2.5.1":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: 10c0/b201c4b66049369a60e766318caff5cb3cc5a900efd89bdac431463822d976ad0670912c931fdbdcf5543207daf6f6833bca57aa116e1661d2ea91e12ca692c4
  languageName: node
  linkType: hard

"rc@npm:^1.2.8":
  version: 1.2.8
  resolution: "rc@npm:1.2.8"
  dependencies:
    deep-extend: "npm:^0.6.0"
    ini: "npm:~1.3.0"
    minimist: "npm:^1.2.0"
    strip-json-comments: "npm:~2.0.1"
  bin:
    rc: ./cli.js
  checksum: 10c0/24a07653150f0d9ac7168e52943cc3cb4b7a22c0e43c7dff3219977c2fdca5a2760a304a029c20811a0e79d351f57d46c9bde216193a0f73978496afc2b85b15
  languageName: node
  linkType: hard

"react-is@npm:^16.12.0":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: 10c0/33977da7a5f1a287936a0c85639fec6ca74f4f15ef1e59a6bc20338fc73dc69555381e211f7a3529b8150a1f71e4225525b41b60b52965bda53ce7d47377ada1
  languageName: node
  linkType: hard

"react-is@npm:^18.0.0":
  version: 18.2.0
  resolution: "react-is@npm:18.2.0"
  checksum: 10c0/6eb5e4b28028c23e2bfcf73371e72cd4162e4ac7ab445ddae2afe24e347a37d6dc22fae6e1748632cd43c6d4f9b8f86dcf26bf9275e1874f436d129952528ae0
  languageName: node
  linkType: hard

"readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: 10c0/e37be5c79c376fdd088a45fa31ea2e423e5d48854be7a22a58869b4e84d25047b193f6acb54f1012331e1bcd667ffb569c01b99d36b0bd59658fb33f513511b7
  languageName: node
  linkType: hard

"readable-stream@npm:^4.0.0":
  version: 4.4.2
  resolution: "readable-stream@npm:4.4.2"
  dependencies:
    abort-controller: "npm:^3.0.0"
    buffer: "npm:^6.0.3"
    events: "npm:^3.3.0"
    process: "npm:^0.11.10"
    string_decoder: "npm:^1.3.0"
  checksum: 10c0/cf7cc8daa2b57872d120945a20a1458c13dcb6c6f352505421115827b18ac4df0e483ac1fe195cb1f5cd226e1073fc55b92b569269d8299e8530840bcdbba40c
  languageName: node
  linkType: hard

"readable-web-to-node-stream@npm:^3.0.0":
  version: 3.0.2
  resolution: "readable-web-to-node-stream@npm:3.0.2"
  dependencies:
    readable-stream: "npm:^3.6.0"
  checksum: 10c0/533d5cd1580232a2c753e52a245be13fc552e6f82c5053a8a8da7ea1063d73a34f936a86b3d4433cdb4a13dd683835cfc87f230936cb96d329a1e28b6040f42e
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 10c0/6fa848cf63d1b82ab4e985f4cf72bd55b7dcfd8e0a376905804e48c3634b7e749170940ba77b32804d5fe93b3cc521aa95a8d7e7d725f830da6d93f3669ce66b
  languageName: node
  linkType: hard

"readme-badger@npm:^0.3.0":
  version: 0.3.0
  resolution: "readme-badger@npm:0.3.0"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/3aa92b1059c51f34701bf4f6557dc8ff7d735a052588ec04365c79af2f7ef44a6497e4ea86c6dda3c07e9186e101110d6b4047b8c41c312b549e0dc59571b886
  languageName: node
  linkType: hard

"real-require@npm:^0.2.0":
  version: 0.2.0
  resolution: "real-require@npm:0.2.0"
  checksum: 10c0/23eea5623642f0477412ef8b91acd3969015a1501ed34992ada0e3af521d3c865bb2fe4cdbfec5fe4b505f6d1ef6a03e5c3652520837a8c3b53decff7e74b6a0
  languageName: node
  linkType: hard

"rechoir@npm:^0.8.0":
  version: 0.8.0
  resolution: "rechoir@npm:0.8.0"
  dependencies:
    resolve: "npm:^1.20.0"
  checksum: 10c0/1a30074124a22abbd5d44d802dac26407fa72a0a95f162aa5504ba8246bc5452f8b1a027b154d9bdbabcd8764920ff9333d934c46a8f17479c8912e92332f3ff
  languageName: node
  linkType: hard

"redeyed@npm:~2.1.0":
  version: 2.1.1
  resolution: "redeyed@npm:2.1.1"
  dependencies:
    esprima: "npm:~4.0.0"
  checksum: 10c0/350f5e39aebab3886713a170235c38155ee64a74f0f7e629ecc0144ba33905efea30c2c3befe1fcbf0b0366e344e7bfa34e6b2502b423c9a467d32f1306ef166
  languageName: node
  linkType: hard

"redis-errors@npm:^1.0.0, redis-errors@npm:^1.2.0":
  version: 1.2.0
  resolution: "redis-errors@npm:1.2.0"
  checksum: 10c0/5b316736e9f532d91a35bff631335137a4f974927bb2fb42bf8c2f18879173a211787db8ac4c3fde8f75ed6233eb0888e55d52510b5620e30d69d7d719c8b8a7
  languageName: node
  linkType: hard

"redis-info@npm:^3.0.8":
  version: 3.1.0
  resolution: "redis-info@npm:3.1.0"
  dependencies:
    lodash: "npm:^4.17.11"
  checksum: 10c0/ec0f31d97893c5828cec7166486d74198c92160c60073b6f2fe805cdf575a10ddcccc7641737d44b8f451355f0ab5b6c7b0d79e8fc24742b75dd625f91ffee38
  languageName: node
  linkType: hard

"redis-parser@npm:^3.0.0":
  version: 3.0.0
  resolution: "redis-parser@npm:3.0.0"
  dependencies:
    redis-errors: "npm:^1.0.0"
  checksum: 10c0/ee16ac4c7b2a60b1f42a2cdaee22b005bd4453eb2d0588b8a4939718997ae269da717434da5d570fe0b05030466eeb3f902a58cf2e8e1ca058bf6c9c596f632f
  languageName: node
  linkType: hard

"reflect-metadata@npm:^0.1.13":
  version: 0.1.13
  resolution: "reflect-metadata@npm:0.1.13"
  checksum: 10c0/728bff0b376b05639fd11ed80c648b61f7fe653c5b506d7ca118e58b6752b9b00810fe0c86227ecf02bd88da6251ab3eb19fd403aaf2e9ff5ef36a2fda643026
  languageName: node
  linkType: hard

"regex-not@npm:^1.0.0, regex-not@npm:^1.0.2":
  version: 1.0.2
  resolution: "regex-not@npm:1.0.2"
  dependencies:
    extend-shallow: "npm:^3.0.2"
    safe-regex: "npm:^1.1.0"
  checksum: 10c0/a0f8d6045f63b22e9759db10e248369c443b41cedd7dba0922d002b66c2734bc2aef0d98c4d45772d1f756245f4c5203856b88b9624bba2a58708858a8d485d6
  languageName: node
  linkType: hard

"repeat-element@npm:^1.1.2":
  version: 1.1.4
  resolution: "repeat-element@npm:1.1.4"
  checksum: 10c0/81aa8d82bc845780803ef52df3533fa399974b99df571d0bb86e91f0ffca9ee4b9c4e8e5e72af087938cc28d2aef93d106a6d01da685d72ce96455b90a9f9f69
  languageName: node
  linkType: hard

"repeat-string@npm:^1.6.1":
  version: 1.6.1
  resolution: "repeat-string@npm:1.6.1"
  checksum: 10c0/87fa21bfdb2fbdedc44b9a5b118b7c1239bdd2c2c1e42742ef9119b7d412a5137a1d23f1a83dc6bb686f4f27429ac6f542e3d923090b44181bafa41e8ac0174d
  languageName: node
  linkType: hard

"require-all@npm:^3.0.0":
  version: 3.0.0
  resolution: "require-all@npm:3.0.0"
  checksum: 10c0/e3ceaff229b512baa817ac8b3a5a2afd82eb32449410587d0dadf939e3a41166f2c23246bd1255cf6fcefe4e37084c52c40521411719f7f33cb1bb5fdd2e94d7
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10c0/8408eec31a3112ef96e3746c37be7d64020cda07c03a920f5024e77290a218ea758b26ca9529fd7b1ad283947f34b2291c1c0f6aa0ed34acfdda9c6014c8d190
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 10c0/b21cb7f1fb746de8107b9febab60095187781137fd803e6a59a76d421444b1531b641bba5857f5dc011974d8a5c635d61cec49e6bd3b7fc20e01f0fafc4efbf2
  languageName: node
  linkType: hard

"resolve-url@npm:^0.2.1":
  version: 0.2.1
  resolution: "resolve-url@npm:0.2.1"
  checksum: 10c0/c285182cfcddea13a12af92129ce0569be27fb0074ffaefbd3ba3da2eac2acecdfc996d435c4982a9fa2b4708640e52837c9153a5ab9255886a00b0b9e8d2a54
  languageName: node
  linkType: hard

"resolve@npm:^1.20.0":
  version: 1.22.2
  resolution: "resolve@npm:1.22.2"
  dependencies:
    is-core-module: "npm:^2.11.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/f9f424a8117d1c68371b4fbc64e6ac045115a3beacc4bd3617b751f7624b69ad40c47dc995585c7f13d4a09723a8f167847defb7d39fad70b0d43bbba05ff851
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.20.0#optional!builtin<compat/resolve>":
  version: 1.22.2
  resolution: "resolve@patch:resolve@npm%3A1.22.2#optional!builtin<compat/resolve>::version=1.22.2&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.11.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/dcf068c4391941734efda06b6f778c013fd349cd4340f126de17c265a7b006c67de7e80e7aa06ecd29f3922e49f5561622b9faf98531f16aa9a896d22148c661
  languageName: node
  linkType: hard

"restore-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "restore-cursor@npm:3.1.0"
  dependencies:
    onetime: "npm:^5.1.0"
    signal-exit: "npm:^3.0.2"
  checksum: 10c0/8051a371d6aa67ff21625fa94e2357bd81ffdc96267f3fb0fc4aaf4534028343836548ef34c240ffa8c25b280ca35eb36be00b3cb2133fa4f51896d7e73c6b4f
  languageName: node
  linkType: hard

"ret@npm:~0.1.10":
  version: 0.1.15
  resolution: "ret@npm:0.1.15"
  checksum: 10c0/01f77cad0f7ea4f955852c03d66982609893edc1240c0c964b4c9251d0f9fb6705150634060d169939b096d3b77f4c84d6b6098a5b5d340160898c8581f1f63f
  languageName: node
  linkType: hard

"retry@npm:0.13.1":
  version: 0.13.1
  resolution: "retry@npm:0.13.1"
  checksum: 10c0/9ae822ee19db2163497e074ea919780b1efa00431d197c7afdb950e42bf109196774b92a49fc9821f0b8b328a98eea6017410bfc5e8a0fc19c85c6d11adb3772
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: 10c0/c19ef26e4e188f408922c46f7ff480d38e8dfc55d448310dfb518736b23ed2c4f547fb64a6ed5bdba92cd7e7ddc889d36ff78f794816d5e71498d645ef476107
  languageName: node
  linkType: hard

"rev-hash@npm:^3.0.0":
  version: 3.0.0
  resolution: "rev-hash@npm:3.0.0"
  checksum: 10c0/0630914b224403bd04333a2e57e773e062ce43b6466949fc3f26223801962a1718cc829e2dc8e245ea178b30419c43aa1131c8f35818511f08d9b2afb65d18d3
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: bin.js
  checksum: 10c0/9cb7757acb489bd83757ba1a274ab545eafd75598a9d817e0c3f8b164238dd90eba50d6b848bd4dcc5f3040912e882dc7ba71653e35af660d77b25c381d402e8
  languageName: node
  linkType: hard

"run-applescript@npm:^5.0.0":
  version: 5.0.0
  resolution: "run-applescript@npm:5.0.0"
  dependencies:
    execa: "npm:^5.0.0"
  checksum: 10c0/f9977db5770929f3f0db434b8e6aa266498c70dec913c84320c0a06add510cf44e3a048c44da088abee312006f9cbf572fd065cdc8f15d7682afda8755f4114c
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10c0/200b5ab25b5b8b7113f9901bfe3afc347e19bb7475b267d55ad0eb86a62a46d77510cb0f232507c9e5d497ebda569a08a9867d0d14f57a82ad5564d991588b39
  languageName: node
  linkType: hard

"safe-buffer@npm:5.1.2":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 10c0/780ba6b5d99cc9a40f7b951d47152297d0e260f0df01472a1b99d4889679a4b94a13d644f7dbc4f022572f09ae9005fa2fbb93bbbd83643316f365a3e9a45b21
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10c0/6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safe-regex@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex@npm:1.1.0"
  dependencies:
    ret: "npm:~0.1.10"
  checksum: 10c0/547d58aa5184cbef368fd5ed5f28d20f911614748c5da6b35f53fd6626396707587251e6e3d1e3010fd3ff1212e413841b8825eaa5f317017ca62a30899af31a
  languageName: node
  linkType: hard

"safe-stable-stringify@npm:^2.3.1":
  version: 2.4.3
  resolution: "safe-stable-stringify@npm:2.4.3"
  checksum: 10c0/81dede06b8f2ae794efd868b1e281e3c9000e57b39801c6c162267eb9efda17bd7a9eafa7379e1f1cacd528d4ced7c80d7460ad26f62ada7c9e01dec61b2e768
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"secure-json-parse@npm:^2.4.0":
  version: 2.7.0
  resolution: "secure-json-parse@npm:2.7.0"
  checksum: 10c0/f57eb6a44a38a3eeaf3548228585d769d788f59007454214fab9ed7f01fbf2e0f1929111da6db28cf0bcc1a2e89db5219a59e83eeaec3a54e413a0197ce879e4
  languageName: node
  linkType: hard

"semver@npm:^5.6.0":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: 10c0/e4cf10f86f168db772ae95d86ba65b3fd6c5967c94d97c708ccb463b778c2ee53b914cd7167620950fc07faf5a564e6efe903836639e512a1aa15fbc9667fa25
  languageName: node
  linkType: hard

"semver@npm:^6.0.0, semver@npm:^6.3.0":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/e3d79b609071caa78bcb6ce2ad81c7966a46a7431d9d58b8800cfa9cb6a63699b3899a0e4bcce36167a284578212d9ae6942b6929ba4aa5015c079a67751d42d
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.3.7, semver@npm:^7.3.8":
  version: 7.5.4
  resolution: "semver@npm:7.5.4"
  dependencies:
    lru-cache: "npm:^6.0.0"
  bin:
    semver: bin/semver.js
  checksum: 10c0/5160b06975a38b11c1ab55950cb5b8a23db78df88275d3d8a42ccf1f29e55112ac995b3a26a522c36e3b5f76b0445f1eef70d696b8c7862a2b4303d7b0e7609e
  languageName: node
  linkType: hard

"send@npm:0.17.1":
  version: 0.17.1
  resolution: "send@npm:0.17.1"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:~1.1.2"
    destroy: "npm:~1.0.4"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:~1.7.2"
    mime: "npm:1.6.0"
    ms: "npm:2.1.1"
    on-finished: "npm:~2.3.0"
    range-parser: "npm:~1.2.1"
    statuses: "npm:~1.5.0"
  checksum: 10c0/712e27d5d4f38d6097a649bbe8846a30a6f9d1995e78e1c133a7a351ec26508b0d8fb707dadb6e003f3753d3f9310667e04633522883b81300abd9978b28afd2
  languageName: node
  linkType: hard

"send@npm:0.18.0":
  version: 0.18.0
  resolution: "send@npm:0.18.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10c0/0eb134d6a51fc13bbcb976a1f4214ea1e33f242fae046efc311e80aff66c7a43603e26a79d9d06670283a13000e51be6e0a2cb80ff0942eaf9f1cd30b7ae736a
  languageName: node
  linkType: hard

"sentence-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "sentence-case@npm:3.0.4"
  dependencies:
    no-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
    upper-case-first: "npm:^2.0.2"
  checksum: 10c0/9a90527a51300cf5faea7fae0c037728f9ddcff23ac083883774c74d180c0a03c31aab43d5c3347512e8c1b31a0d4712512ec82beb71aa79b85149f9abeb5467
  languageName: node
  linkType: hard

"seq-queue@npm:^0.0.5":
  version: 0.0.5
  resolution: "seq-queue@npm:0.0.5"
  checksum: 10c0/ec870fc392f0e6e99ec0e551c3041c1a66144d1580efabae7358e572de127b0ad2f844c95a4861d2e6203f836adea4c8196345b37bed55331ead8f22d99ac84c
  languageName: node
  linkType: hard

"serve-static@npm:1.14.1":
  version: 1.14.1
  resolution: "serve-static@npm:1.14.1"
  dependencies:
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.17.1"
  checksum: 10c0/f4ebc459bff763ae372e4148c2af13e2b813033f384cb2bc4e1c129c722fa14bfaf6e85f41c95363d49f97de7244e7961c929b2f942ddbd4c520c9610322dae5
  languageName: node
  linkType: hard

"serve-static@npm:^1.15.0":
  version: 1.15.0
  resolution: "serve-static@npm:1.15.0"
  dependencies:
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.18.0"
  checksum: 10c0/fa9f0e21a540a28f301258dfe1e57bb4f81cd460d28f0e973860477dd4acef946a1f41748b5bd41c73b621bea2029569c935faa38578fd34cd42a9b4947088ba
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 10c0/9f8c1b2d800800d0b589de1477c753492de5c1548d4ade52f57f1d1f5e04af5481554d75ce5e5c43d4004b80a3eb714398d6907027dc0534177b7539119f4454
  languageName: node
  linkType: hard

"set-cookie-parser@npm:^2.5.1":
  version: 2.6.0
  resolution: "set-cookie-parser@npm:2.6.0"
  checksum: 10c0/739da029f0e56806a103fcd5501d9c475e19e77bd8274192d7ae5c374ae714a82bba9a7ac00b0330a18227c5644b08df9e442240527be578f5a6030f9bb2bb80
  languageName: node
  linkType: hard

"set-value@npm:^2.0.0, set-value@npm:^2.0.1":
  version: 2.0.1
  resolution: "set-value@npm:2.0.1"
  dependencies:
    extend-shallow: "npm:^2.0.1"
    is-extendable: "npm:^0.1.1"
    is-plain-object: "npm:^2.0.3"
    split-string: "npm:^3.0.1"
  checksum: 10c0/4c40573c4f6540456e4b38b95f570272c4cfbe1d12890ad4057886da8535047cd772dfadf5b58e2e87aa244dfb4c57e3586f6716b976fc47c5144b6b09e1811b
  languageName: node
  linkType: hard

"setprototypeof@npm:1.1.1":
  version: 1.1.1
  resolution: "setprototypeof@npm:1.1.1"
  checksum: 10c0/1084b783f2d77908b0a593619e1214c2118c44c7c3277f6099dd7ca8acfc056c009e5d1b2860eae5e8b0ba9bc0a978c15613ff102ccc1093bb48aa6e0ed75e2f
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 10c0/68733173026766fa0d9ecaeb07f0483f4c2dc70ca376b3b7c40b7cda909f94b0918f6c5ad5ce27a9160bdfb475efaa9d5e705a11d8eaae18f9835d20976028bc
  languageName: node
  linkType: hard

"shallow-clone@npm:^3.0.0":
  version: 3.0.1
  resolution: "shallow-clone@npm:3.0.1"
  dependencies:
    kind-of: "npm:^6.0.2"
  checksum: 10c0/7bab09613a1b9f480c85a9823aebec533015579fa055ba6634aa56ba1f984380670eaf33b8217502931872aa1401c9fcadaa15f9f604d631536df475b05bcf1e
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.4":
  version: 1.0.4
  resolution: "side-channel@npm:1.0.4"
  dependencies:
    call-bind: "npm:^1.0.0"
    get-intrinsic: "npm:^1.0.2"
    object-inspect: "npm:^1.9.0"
  checksum: 10c0/054a5d23ee35054b2c4609b9fd2a0587760737782b5d765a9c7852264710cc39c6dcb56a9bbd6c12cd84071648aea3edb2359d2f6e560677eedadce511ac1da5
  languageName: node
  linkType: hard

"sigmund@npm:^1.0.1":
  version: 1.0.1
  resolution: "sigmund@npm:1.0.1"
  checksum: 10c0/0cc9cf0acf4ee1e29bc324ec60b81865c30c4cf6738c6677646b101df1b1b1663759106d96de4199648e5fff3d1d2468ba06ec437cfcef16ee8ff19133fcbb9d
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.3, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 10c0/25d272fa73e146048565e08f3309d5b942c1979a6f4a58a8c59d5fa299728e9c2fcd1a759ec870863b1fd38653670240cd420dad2ad9330c71f36608a6a1c912
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"slash@npm:^2.0.0":
  version: 2.0.0
  resolution: "slash@npm:2.0.0"
  checksum: 10c0/f83dbd3cb62c41bb8fcbbc6bf5473f3234b97fa1d008f571710a9d3757a28c7169e1811cad1554ccb1cc531460b3d221c9a7b37f549398d9a30707f0a5af9193
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 10c0/e18488c6a42bdfd4ac5be85b2ced3ccd0224773baae6ad42cfbb9ec74fc07f9fa8396bd35ee638084ead7a2a0818eb5e7151111544d4731ce843019dab4be47b
  languageName: node
  linkType: hard

"slashes@npm:2.0.2":
  version: 2.0.2
  resolution: "slashes@npm:2.0.2"
  checksum: 10c0/791045fbe4ef3e0eb996f332b00d9ab75f93b557318ed1eb66372ff9e9110e703ec3d6f66b8e745ef9f383add7e8dd6af3a5ea30e0f80ff233802db7338a3ce5
  languageName: node
  linkType: hard

"slice-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "slice-ansi@npm:4.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    astral-regex: "npm:^2.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
  checksum: 10c0/6c25678db1270d4793e0327620f1e0f9f5bea4630123f51e9e399191bc52c87d6e6de53ed33538609e5eacbd1fab769fae00f3705d08d029f02102a540648918
  languageName: node
  linkType: hard

"slugify@npm:^1.6.5":
  version: 1.6.6
  resolution: "slugify@npm:1.6.6"
  checksum: 10c0/e7e63f08f389a371d6228bc19d64ec84360bf0a538333446cc49dbbf3971751a6d180d2f31551188dd007a65ca771e69f574e0283290a7825a818e90b75ef44d
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"smpltmpl@npm:^1.0.2":
  version: 1.0.2
  resolution: "smpltmpl@npm:1.0.2"
  dependencies:
    babel-code-frame: "npm:^6.26.0"
  checksum: 10c0/12aa346b8351f1dc7e4b2851bc0942b1e8a80865602305186e5cdb7d04439160cd2a925e7d8a6c604c9698b128dbf239d8210f38f39a0c6178d3f477357db774
  languageName: node
  linkType: hard

"snake-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "snake-case@npm:3.0.4"
  dependencies:
    dot-case: "npm:^3.0.4"
    tslib: "npm:^2.0.3"
  checksum: 10c0/ab19a913969f58f4474fe9f6e8a026c8a2142a01f40b52b79368068343177f818cdfef0b0c6b9558f298782441d5ca8ed5932eb57822439fad791d866e62cecd
  languageName: node
  linkType: hard

"snapdragon-node@npm:^2.0.1":
  version: 2.1.1
  resolution: "snapdragon-node@npm:2.1.1"
  dependencies:
    define-property: "npm:^1.0.0"
    isobject: "npm:^3.0.0"
    snapdragon-util: "npm:^3.0.1"
  checksum: 10c0/7616e6a1ca054afe3ad8defda17ebe4c73b0800d2e0efd635c44ee1b286f8ac7900517314b5330862ce99b28cd2782348ee78bae573ff0f55832ad81d9657f3f
  languageName: node
  linkType: hard

"snapdragon-util@npm:^3.0.1":
  version: 3.0.1
  resolution: "snapdragon-util@npm:3.0.1"
  dependencies:
    kind-of: "npm:^3.2.0"
  checksum: 10c0/4441856d343399ba7f37f79681949d51b922e290fcc07e7bc94655a50f584befa4fb08f40c3471cd160e004660161964d8ff140cba49baa59aa6caba774240e3
  languageName: node
  linkType: hard

"snapdragon@npm:^0.8.1":
  version: 0.8.2
  resolution: "snapdragon@npm:0.8.2"
  dependencies:
    base: "npm:^0.11.1"
    debug: "npm:^2.2.0"
    define-property: "npm:^0.2.5"
    extend-shallow: "npm:^2.0.1"
    map-cache: "npm:^0.2.2"
    source-map: "npm:^0.5.6"
    source-map-resolve: "npm:^0.5.0"
    use: "npm:^3.1.0"
  checksum: 10c0/dfdac1f73d47152d72fc07f4322da09bbddfa31c1c9c3ae7346f252f778c45afa5b03e90813332f02f04f6de8003b34a168c456f8bb719024d092f932520ffca
  languageName: node
  linkType: hard

"socket.io-client@npm:^1.4.0":
  version: 1.7.4
  resolution: "socket.io-client@npm:1.7.4"
  dependencies:
    backo2: "npm:1.0.2"
    component-bind: "npm:1.0.0"
    component-emitter: "npm:1.2.1"
    debug: "npm:2.3.3"
    engine.io-client: "npm:~1.8.4"
    has-binary: "npm:0.1.7"
    indexof: "npm:0.0.1"
    object-component: "npm:0.0.3"
    parseuri: "npm:0.0.5"
    socket.io-parser: "npm:2.3.1"
    to-array: "npm:0.1.4"
  checksum: 10c0/a5cf35ea25ac9a5ff3932972ed799ed788ed82c62c60d1b4c753a719d171fc890dfcb9f6739a787693be649ed1cfc367bb6cb8620db94dd1b366a5ba4c8bc703
  languageName: node
  linkType: hard

"socket.io-parser@npm:2.3.1":
  version: 2.3.1
  resolution: "socket.io-parser@npm:2.3.1"
  dependencies:
    component-emitter: "npm:1.1.2"
    debug: "npm:2.2.0"
    isarray: "npm:0.0.1"
    json3: "npm:3.3.2"
  checksum: 10c0/0e2806b023e9c626417bef82da1716754208fc06d801aef6f005d9b8d0c478525e6011c5762412e5442236d680eeff986f77563973e0c91175c5befb04387204
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^7.0.0":
  version: 7.0.0
  resolution: "socks-proxy-agent@npm:7.0.0"
  dependencies:
    agent-base: "npm:^6.0.2"
    debug: "npm:^4.3.3"
    socks: "npm:^2.6.2"
  checksum: 10c0/b859f7eb8e96ec2c4186beea233ae59c02404094f3eb009946836af27d6e5c1627d1975a69b4d2e20611729ed543b6db3ae8481eb38603433c50d0345c987600
  languageName: node
  linkType: hard

"socks@npm:^2.6.2":
  version: 2.7.1
  resolution: "socks@npm:2.7.1"
  dependencies:
    ip: "npm:^2.0.0"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/43f69dbc9f34fc8220bc51c6eea1c39715ab3cfdb115d6e3285f6c7d1a603c5c75655668a5bbc11e3c7e2c99d60321fb8d7ab6f38cda6a215fadd0d6d0b52130
  languageName: node
  linkType: hard

"sonic-boom@npm:^1.0.2":
  version: 1.4.1
  resolution: "sonic-boom@npm:1.4.1"
  dependencies:
    atomic-sleep: "npm:^1.0.0"
    flatstr: "npm:^1.0.12"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"sonic-boom@npm:^2.1.0":
  version: 2.8.0
  resolution: "sonic-boom@npm:2.8.0"
  dependencies:
    atomic-sleep: "npm:^1.0.0"
  checksum: 10c0/6b40f2e91a999819b1dc24018a5d1c8b74e66e5d019eabad17d5b43fc309b32255b7c405ed6ec885693c8f2b969099ce96aeefde027180928bc58c034234a86d
  languageName: node
  linkType: hard

"sonic-boom@npm:^3.0.0, sonic-boom@npm:^3.1.0":
  version: 3.3.0
  resolution: "sonic-boom@npm:3.3.0"
  dependencies:
    atomic-sleep: "npm:^1.0.0"
  checksum: 10c0/c5d387d9e35726a60afe5b5c54317db2428158ecaf3769fb16418e50d2a47176cbd800bf664a410dc0aab0792b421d394ce6edaf63b796ac3c7986f01933cddd
  languageName: node
  linkType: hard

"source-map-resolve@npm:^0.5.0":
  version: 0.5.3
  resolution: "source-map-resolve@npm:0.5.3"
  dependencies:
    atob: "npm:^2.1.2"
    decode-uri-component: "npm:^0.2.0"
    resolve-url: "npm:^0.2.1"
    source-map-url: "npm:^0.4.0"
    urix: "npm:^0.1.0"
  checksum: 10c0/410acbe93882e058858d4c1297be61da3e1533f95f25b95903edddc1fb719654e705663644677542d1fb78a66390238fad1a57115fc958a0724cf9bb509caf57
  languageName: node
  linkType: hard

"source-map-support@npm:^0.5.21":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10c0/9ee09942f415e0f721d6daad3917ec1516af746a8120bba7bb56278707a37f1eb8642bde456e98454b8a885023af81a16e646869975f06afc1a711fb90484e7d
  languageName: node
  linkType: hard

"source-map-url@npm:^0.4.0":
  version: 0.4.1
  resolution: "source-map-url@npm:0.4.1"
  checksum: 10c0/f8af0678500d536c7f643e32094d6718a4070ab4ca2d2326532512cfbe2d5d25a45849b4b385879326f2d7523bb3b686d0360dd347a3cda09fd89a5c28d4bc58
  languageName: node
  linkType: hard

"source-map@npm:^0.5.6":
  version: 0.5.7
  resolution: "source-map@npm:0.5.7"
  checksum: 10c0/904e767bb9c494929be013017380cbba013637da1b28e5943b566031e29df04fba57edf3f093e0914be094648b577372bd8ad247fa98cfba9c600794cd16b599
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10c0/ab55398007c5e5532957cb0beee2368529618ac0ab372d789806f5718123cc4367d57de3904b4e6a4170eb5a0b0f41373066d02ca0735a0c4d75c7d328d3e011
  languageName: node
  linkType: hard

"split-lines@npm:^2.0.0":
  version: 2.1.0
  resolution: "split-lines@npm:2.1.0"
  checksum: 10c0/73eed5c8c4857d5f6a21d8f9c91f5d1f71060d59dc4505659c0d51e578f83ec1c4365fd7ae76d093f702b0463040d148c72bc58d79cb3ad4eb26ee1e2b38e4c8
  languageName: node
  linkType: hard

"split-string@npm:^3.0.1, split-string@npm:^3.0.2":
  version: 3.1.0
  resolution: "split-string@npm:3.1.0"
  dependencies:
    extend-shallow: "npm:^3.0.0"
  checksum: 10c0/72d7cd625445c7af215130e1e2bc183013bb9dd48a074eda1d35741e2b0dcb355e6df5b5558a62543a24dcec37dd1d6eb7a6228ff510d3c9de0f3dc1d1da8a70
  languageName: node
  linkType: hard

"split2@npm:^4.0.0":
  version: 4.2.0
  resolution: "split2@npm:4.2.0"
  checksum: 10c0/b292beb8ce9215f8c642bb68be6249c5a4c7f332fc8ecadae7be5cbdf1ea95addc95f0459ef2e7ad9d45fd1064698a097e4eb211c83e772b49bc0ee423e91534
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 10c0/ecadcfe4c771890140da5023d43e190b7566d9cf8b2d238600f31bec0fc653f328da4450eb04bd59a431771a8e9cc0e118f0aa3974b683a4981b4e07abc2a5bb
  languageName: node
  linkType: hard

"sqlstring@npm:^2.3.2":
  version: 2.3.3
  resolution: "sqlstring@npm:2.3.3"
  checksum: 10c0/3b5dd7badb3d6312f494cfa6c9a381ee630fbe3dbd571c4c9eb8ecdb99a7bf5a1f7a5043191d768797f6b3c04eed5958ac6a5f948b998f0a138294c6d3125fbd
  languageName: node
  linkType: hard

"ssri@npm:^10.0.0":
  version: 10.0.4
  resolution: "ssri@npm:10.0.4"
  dependencies:
    minipass: "npm:^5.0.0"
  checksum: 10c0/d085474ea6b439623a9a6a2c67570cb9e68e1bb6060e46e4d387f113304d75a51946d57c524be3a90ebfa3c73026edf76eb1a2d79a7f6cff0b04f21d99f127ab
  languageName: node
  linkType: hard

"stacktracey@npm:^2.1.8":
  version: 2.1.8
  resolution: "stacktracey@npm:2.1.8"
  dependencies:
    as-table: "npm:^1.0.36"
    get-source: "npm:^2.0.12"
  checksum: 10c0/e17357d0a532d303138899b910ab660572009a1f4cde1cbf73b99416957a2378e6e1c791b3c31b043cf7c5f37647da1dd114e66c9203f23c65b34f783665405b
  languageName: node
  linkType: hard

"standard-as-callback@npm:^2.1.0":
  version: 2.1.0
  resolution: "standard-as-callback@npm:2.1.0"
  checksum: 10c0/012677236e3d3fdc5689d29e64ea8a599331c4babe86956bf92fc5e127d53f85411c5536ee0079c52c43beb0026b5ce7aa1d834dd35dd026e82a15d1bcaead1f
  languageName: node
  linkType: hard

"static-extend@npm:^0.1.1":
  version: 0.1.2
  resolution: "static-extend@npm:0.1.2"
  dependencies:
    define-property: "npm:^0.2.5"
    object-copy: "npm:^0.1.0"
  checksum: 10c0/284f5865a9e19d079f1badbcd70d5f9f82e7a08393f818a220839cd5f71729e89105e1c95322bd28e833161d484cee671380ca443869ae89578eef2bf55c0653
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 10c0/34378b207a1620a24804ce8b5d230fea0c279f00b18a7209646d5d47e419d1cc23e7cbf33a25a1e51ac38973dc2ac2e1e9c647a8e481ef365f77668d72becfd0
  languageName: node
  linkType: hard

"statuses@npm:>= 1.5.0 < 2, statuses@npm:~1.5.0":
  version: 1.5.0
  resolution: "statuses@npm:1.5.0"
  checksum: 10c0/e433900956357b3efd79b1c547da4d291799ac836960c016d10a98f6a810b1b5c0dcc13b5a7aa609a58239b5190e1ea176ad9221c2157d2fd1c747393e6b2940
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^1.0.2 || 2 || 3 || 4, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.2, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1, string_decoder@npm:^1.3.0":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 10c0/810614ddb030e271cd591935dcd5956b2410dd079d64ff92a1844d6b7588bf992b3e1b69b0f4d34a3e06e0bd73046ac646b5264c1987b20d0601f81ef35d731d
  languageName: node
  linkType: hard

"stringify-attributes@npm:^2.0.0":
  version: 2.0.0
  resolution: "stringify-attributes@npm:2.0.0"
  dependencies:
    escape-goat: "npm:^2.0.0"
  checksum: 10c0/846240499b9b6415d11e4bf5ab13a8f9a8dbd87aa9cc28816da7b4bdeb13e9956cb7b6d7641d994733c3c3f3de8a1abdc892b824e26ebda0eca805ba4ae45dee
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^3.0.0":
  version: 3.0.1
  resolution: "strip-ansi@npm:3.0.1"
  dependencies:
    ansi-regex: "npm:^2.0.0"
  checksum: 10c0/f6e7fbe8e700105dccf7102eae20e4f03477537c74b286fd22cfc970f139002ed6f0d9c10d0e21aa9ed9245e0fa3c9275930e8795c5b947da136e4ecb644a70f
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-bom@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-bom@npm:4.0.0"
  checksum: 10c0/26abad1172d6bc48985ab9a5f96c21e440f6e7e476686de49be813b5a59b3566dccb5c525b831ec54fe348283b47f3ffb8e080bc3f965fde12e84df23f6bb7ef
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 10c0/bddf8ccd47acd85c0e09ad7375409d81653f645fda13227a9d459642277c253d877b68f2e5e4d819fe75733b0e626bac7e954c04f3236f6d196f79c94fa4a96f
  languageName: node
  linkType: hard

"strip-final-newline@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-final-newline@npm:3.0.0"
  checksum: 10c0/a771a17901427bac6293fd416db7577e2bc1c34a19d38351e9d5478c3c415f523f391003b42ed475f27e33a78233035df183525395f731d3bfb8cdcbd4da08ce
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10c0/9681a6257b925a7fa0f285851c0e613cc934a50661fa7bb41ca9cbbff89686bb4a0ee366e6ecedc4daafd01e83eee0720111ab294366fe7c185e935475ebcecd
  languageName: node
  linkType: hard

"strip-json-comments@npm:~2.0.1":
  version: 2.0.1
  resolution: "strip-json-comments@npm:2.0.1"
  checksum: 10c0/b509231cbdee45064ff4f9fd73609e2bcc4e84a4d508e9dd0f31f70356473fde18abfb5838c17d56fb236f5a06b102ef115438de0600b749e818a35fbbc48c43
  languageName: node
  linkType: hard

"strtok3@npm:^6.2.4":
  version: 6.3.0
  resolution: "strtok3@npm:6.3.0"
  dependencies:
    "@tokenizer/token": "npm:^0.3.0"
    peek-readable: "npm:^4.1.0"
  checksum: 10c0/8f1483a2a6758404502f2fc431586fcf37d747b10b125596ab5ec92319c247dd1195f82ba0bc2eaa582db3d807b5cca4b67ff61411756fec6622d051f8e255c2
  languageName: node
  linkType: hard

"superagent@npm:^8.0.9":
  version: 8.0.9
  resolution: "superagent@npm:8.0.9"
  dependencies:
    component-emitter: "npm:^1.3.0"
    cookiejar: "npm:^2.1.4"
    debug: "npm:^4.3.4"
    fast-safe-stringify: "npm:^2.1.1"
    form-data: "npm:^4.0.0"
    formidable: "npm:^2.1.2"
    methods: "npm:^1.1.2"
    mime: "npm:2.6.0"
    qs: "npm:^6.11.0"
    semver: "npm:^7.3.8"
  checksum: 10c0/74a5d2a155b8673f1c862033a326fdad492ace870f4a41ec6f1aa5642752dd66d011190e32c0ccd1e385e886aa33d9250aea945e234d7cfec8bb40e850cd4e81
  languageName: node
  linkType: hard

"supports-color@npm:^2.0.0":
  version: 2.0.0
  resolution: "supports-color@npm:2.0.0"
  checksum: 10c0/570e0b63be36cccdd25186350a6cb2eaad332a95ff162fa06d9499982315f2fe4217e69dd98e862fbcd9c81eaff300a825a1fe7bf5cc752e5b84dfed042b0dda
  languageName: node
  linkType: hard

"supports-color@npm:^7.0.0, supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/ea1d3c275dd604c974670f63943ed9bd83623edc102430c05adb8efc56ba492746b6e95386e7831b872ec3807fd89dd8eb43f735195f37b5ec343e4234cc7e89
  languageName: node
  linkType: hard

"supports-hyperlinks@npm:^2.3.0":
  version: 2.3.0
  resolution: "supports-hyperlinks@npm:2.3.0"
  dependencies:
    has-flag: "npm:^4.0.0"
    supports-color: "npm:^7.0.0"
  checksum: 10c0/4057f0d86afb056cd799602f72d575b8fdd79001c5894bcb691176f14e870a687e7981e50bc1484980e8b688c6d5bcd4931e1609816abb5a7dc1486b7babf6a1
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10c0/6c4032340701a9950865f7ae8ef38578d8d7053f5e10518076e6554a9381fa91bd9c6850193695c141f32b21f979c985db07265a758867bac95de05f7d8aeb39
  languageName: node
  linkType: hard

"swagger-parser@npm:^10.0.3":
  version: 10.0.3
  resolution: "swagger-parser@npm:10.0.3"
  dependencies:
    "@apidevtools/swagger-parser": "npm:10.0.3"
  checksum: 10c0/d1a5c05f651f21a23508a36416071630b83e91dfffd52a6d44b06ca2cd1b86304c0dd2f4c04526c999b70062fa89bde3f5d54a1436626f4350590b6c6265a098
  languageName: node
  linkType: hard

"swagger-schema-official@npm:2.0.0-bab6bed":
  version: 2.0.0-bab6bed
  resolution: "swagger-schema-official@npm:2.0.0-bab6bed"
  checksum: 10c0/58d1f8302aaa14fc64658b2821d70a6d06883a5270c71c210e8b6e5655ee2d7e01543383ddb2948ada368b51a22d71a9f9fcc0b903041719897f866b9c65d201
  languageName: node
  linkType: hard

"synckit@npm:^0.8.5":
  version: 0.8.5
  resolution: "synckit@npm:0.8.5"
  dependencies:
    "@pkgr/utils": "npm:^2.3.1"
    tslib: "npm:^2.5.0"
  checksum: 10c0/9827f828cabc404b3a147c38f824c8d5b846eb6f65189d965aa0b71ea8ecda5048f8f50b4bdfd8813148844175233cff56c6bc8d87a7118cf10707df870519f4
  languageName: node
  linkType: hard

"tar@npm:^6.1.11, tar@npm:^6.1.2":
  version: 6.1.15
  resolution: "tar@npm:6.1.15"
  dependencies:
    chownr: "npm:^2.0.0"
    fs-minipass: "npm:^2.0.0"
    minipass: "npm:^5.0.0"
    minizlib: "npm:^2.1.1"
    mkdirp: "npm:^1.0.3"
    yallist: "npm:^4.0.0"
  checksum: 10c0/bb2babe7b14442f690d83c2b2c571c9dd0bf802314773e05f4a3e4a241fdecd7fb560b8e4e7d6ea34533c8cd692e1b8418a3b8ba3b9687fe78a683dfbad7f82d
  languageName: node
  linkType: hard

"tarn@npm:^3.0.2":
  version: 3.0.2
  resolution: "tarn@npm:3.0.2"
  checksum: 10c0/ea2344e3d21936111176375bd6f34eba69a38ef1bc59434d523fd313166f8a28a47b0a847846c119f72dcf2c1e1231596d74ac3fcfc3cc73966b3d293a327269
  languageName: node
  linkType: hard

"term-size@npm:^2.2.1":
  version: 2.2.1
  resolution: "term-size@npm:2.2.1"
  checksum: 10c0/89f6bba1d05d425156c0910982f9344d9e4aebf12d64bfa1f460d93c24baa7bc4c4a21d355fbd7153c316433df0538f64d0ae6e336cc4a69fdda4f85d62bc79d
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: 10c0/02805740c12851ea5982686810702e2f14369a5f4c5c40a836821e3eefc65ffeec3131ba324692a37608294b0fd8c1e55a2dd571ffed4909822787668ddbee5c
  languageName: node
  linkType: hard

"thread-stream@npm:^2.0.0":
  version: 2.3.0
  resolution: "thread-stream@npm:2.3.0"
  dependencies:
    real-require: "npm:^0.2.0"
  checksum: 10c0/06f60892adab3f3b5b8930857a0049d29b89cbb95581b079274c87ee0aea718d12d8d56e8ad3aafa5ded3321aaf51a4e1b1ce70340d650fbfa1cdd6d0d399bc2
  languageName: node
  linkType: hard

"tildify@npm:2.0.0":
  version: 2.0.0
  resolution: "tildify@npm:2.0.0"
  checksum: 10c0/57961810a6915f47bdba7da7fa66a5f12597a0495fa016785de197b02e7ba9994ffebb30569294061bbf6d9395c6b1319d830076221e5a3f49f1318bc749565c
  languageName: node
  linkType: hard

"time-span@npm:^4.0.0":
  version: 4.0.0
  resolution: "time-span@npm:4.0.0"
  dependencies:
    convert-hrtime: "npm:^3.0.0"
  checksum: 10c0/0b3841e34ff331cc9790e231164fbac1f1d9460467d684c2a36d035982708b39384c3dfa070c6852aa596e99dac88e953a2a4645429a6c81932ff439794daa68
  languageName: node
  linkType: hard

"titleize@npm:^3.0.0":
  version: 3.0.0
  resolution: "titleize@npm:3.0.0"
  checksum: 10c0/5ae6084ba299b5782f95e3fe85ea9f0fa4d74b8ae722b6b3208157e975589fbb27733aeba4e5080fa9314a856044ef52caa61b87caea4b1baade951a55c06336
  languageName: node
  linkType: hard

"tmp-cache@npm:^1.1.0":
  version: 1.1.0
  resolution: "tmp-cache@npm:1.1.0"
  checksum: 10c0/730e78ffc443a50929bb2a2bd701fb984e582c3ef6f73a30553d0eb9d2eec3e2b00cb365da47be31313799dd2658ce50b2dd59cbe3f0a424f82badc5c9191dc5
  languageName: node
  linkType: hard

"to-array@npm:0.1.4":
  version: 0.1.4
  resolution: "to-array@npm:0.1.4"
  checksum: 10c0/80c8d5677bcff082d68805625fc1282717c4ccd27885093372131cfbcd719e498c2e86c1787d0064f23529b6f2334031094c087061d20fa06430250ea7caef53
  languageName: node
  linkType: hard

"to-object-path@npm:^0.3.0":
  version: 0.3.0
  resolution: "to-object-path@npm:0.3.0"
  dependencies:
    kind-of: "npm:^3.0.2"
  checksum: 10c0/731832a977614c03a770363ad2bd9e9c82f233261861724a8e612bb90c705b94b1a290a19f52958e8e179180bb9b71121ed65e245691a421467726f06d1d7fc3
  languageName: node
  linkType: hard

"to-regex-range@npm:^2.1.0":
  version: 2.1.1
  resolution: "to-regex-range@npm:2.1.1"
  dependencies:
    is-number: "npm:^3.0.0"
    repeat-string: "npm:^1.6.1"
  checksum: 10c0/440d82dbfe0b2e24f36dd8a9467240406ad1499fc8b2b0f547372c22ed1d092ace2a3eb522bb09bfd9c2f39bf1ca42eb78035cf6d2b8c9f5c78da3abc96cd949
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10c0/487988b0a19c654ff3e1961b87f471702e708fa8a8dd02a298ef16da7206692e8552a0250e8b3e8759270f62e9d8314616f6da274734d3b558b1fc7b7724e892
  languageName: node
  linkType: hard

"to-regex@npm:^3.0.1, to-regex@npm:^3.0.2":
  version: 3.0.2
  resolution: "to-regex@npm:3.0.2"
  dependencies:
    define-property: "npm:^2.0.2"
    extend-shallow: "npm:^3.0.2"
    regex-not: "npm:^1.0.2"
    safe-regex: "npm:^1.1.0"
  checksum: 10c0/99d0b8ef397b3f7abed4bac757b0f0bb9f52bfd39167eb7105b144becfaa9a03756892352d01ac6a911f0c1ceef9f81db68c46899521a3eed054082042796120
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.0":
  version: 1.0.0
  resolution: "toidentifier@npm:1.0.0"
  checksum: 10c0/27a37b8b21126e7216d40c02f410065b1de35b0f844368d0ccaabba7987595703006d45e5c094b086220cbbc5864d4b99766b460110e4bc15b9db574c5c58be2
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 10c0/93937279934bd66cc3270016dd8d0afec14fb7c94a05c72dc57321f8bd1fa97e5bea6d1f7c89e728d077ca31ea125b78320a616a6c6cd0e6b9cb94cb864381c1
  languageName: node
  linkType: hard

"token-types@npm:^4.1.1":
  version: 4.2.1
  resolution: "token-types@npm:4.2.1"
  dependencies:
    "@tokenizer/token": "npm:^0.3.0"
    ieee754: "npm:^1.2.1"
  checksum: 10c0/e9a4a139deba9515770cd7ac36a8f53f953b9d035d309e88a66d706760dba0df420753f2b8bdee6b9f3cbff8d66b24e69571e8dea27baa7b378229ab1bcca399
  languageName: node
  linkType: hard

"traverse@npm:~0.6.6":
  version: 0.6.7
  resolution: "traverse@npm:0.6.7"
  checksum: 10c0/97312cbcce0fdc640cf871a33c3f8efa85fbc2e21020bcbbf48b50883db4c41cfef580f3deaab67217291b761be4558fff34aab1baff7eb2b65323412458a489
  languageName: node
  linkType: hard

"truncatise@npm:0.0.8":
  version: 0.0.8
  resolution: "truncatise@npm:0.0.8"
  checksum: 10c0/4b77f9d99c5eaded0778175e4f12c835dfdd0789b3277dfc5ff406928b423115b112ea9eb390f670b9d75547655b73c7f99ea1ea38bcf165e8d4926a2da18c48
  languageName: node
  linkType: hard

"tslib@npm:^1.8.1":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: 10c0/69ae09c49eea644bc5ebe1bca4fa4cc2c82b7b3e02f43b84bd891504edf66dbc6b2ec0eef31a957042de2269139e4acff911e6d186a258fb14069cd7f6febce2
  languageName: node
  linkType: hard

"tslib@npm:^2.0.0, tslib@npm:^2.0.3, tslib@npm:^2.5.0, tslib@npm:^2.6.0":
  version: 2.6.1
  resolution: "tslib@npm:2.6.1"
  checksum: 10c0/a0382d386f5f1d6e3a39ab22bc56d1e08493da99ab3daf550e63bae6c08fdd6dd4fd20623ef387cad8262ce3fede98439257054fc025f2103cd4603b4509a052
  languageName: node
  linkType: hard

"tsutils@npm:^3.21.0":
  version: 3.21.0
  resolution: "tsutils@npm:3.21.0"
  dependencies:
    tslib: "npm:^1.8.1"
  peerDependencies:
    typescript: ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"
  checksum: 10c0/02f19e458ec78ead8fffbf711f834ad8ecd2cc6ade4ec0320790713dccc0a412b99e7fd907c4cda2a1dc602c75db6f12e0108e87a5afad4b2f9e90a24cabd5a2
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 10c0/7b3fd0ed43891e2080bf0c5c504b418fbb3e5c7b9708d3d015037ba2e6323a28152ec163bcb65212741fa5d2022e3075ac3c76440dbd344c9035f818e8ecee58
  languageName: node
  linkType: hard

"type-detect@npm:^4.0.0, type-detect@npm:^4.0.5":
  version: 4.0.8
  resolution: "type-detect@npm:4.0.8"
  checksum: 10c0/8fb9a51d3f365a7de84ab7f73b653534b61b622aa6800aecdb0f1095a4a646d3f5eb295322127b6573db7982afcd40ab492d038cf825a42093a58b1e1353e0bd
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 10c0/dea9df45ea1f0aaa4e2d3bed3f9a0bfe9e5b2592bddb92eb1bf06e50bcf98dbb78189668cd8bc31a0511d3fc25539b4cd5c704497e53e93e2d40ca764b10bfc3
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: 10c0/902bd57bfa30d51d4779b641c2bc403cdf1371fb9c91d3c058b0133694fcfdb817aef07a47f40faf79039eecbaa39ee9d3c532deff244f3a19ce68cea71a61e8
  languageName: node
  linkType: hard

"type-fest@npm:^3.0.0":
  version: 3.13.1
  resolution: "type-fest@npm:3.13.1"
  checksum: 10c0/547d22186f73a8c04590b70dcf63baff390078c75ea8acd366bbd510fd0646e348bd1970e47ecf795b7cff0b41d26e9c475c1fedd6ef5c45c82075fbf916b629
  languageName: node
  linkType: hard

"type-is@npm:^1.6.18, type-is@npm:~1.6.17, type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: "npm:0.3.0"
    mime-types: "npm:~2.1.24"
  checksum: 10c0/a23daeb538591b7efbd61ecf06b6feb2501b683ffdc9a19c74ef5baba362b4347e42f1b4ed81f5882a8c96a3bfff7f93ce3ffaf0cbbc879b532b04c97a55db9d
  languageName: node
  linkType: hard

"typescript@npm:~4.6":
  version: 4.6.4
  resolution: "typescript@npm:4.6.4"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/92e2c0328485a4f7bd7435f5b105f03addff32f867e241dc3be8c372ed801a138c732d9a55697696d2f82a80dd6ad4bddff1ad6b0d1884bf4a24b92e71094c44
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A~4.6#optional!builtin<compat/typescript>":
  version: 4.6.4
  resolution: "typescript@patch:typescript@npm%3A4.6.4#optional!builtin<compat/typescript>::version=4.6.4&hash=5d3a66"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/0e3fa814d454942a689daf4c00f82328d323e7ecd4077e3265d45375e64642611631f4c882a71be87774468ba03793e9b8ff4bccfac3018194a9e36d8f72c251
  languageName: node
  linkType: hard

"uid-safe@npm:2.1.5":
  version: 2.1.5
  resolution: "uid-safe@npm:2.1.5"
  dependencies:
    random-bytes: "npm:~1.0.0"
  checksum: 10c0/ec96862e859fd12175f3da7fda9d1359a2cf412fd521e10837cbdc6d554774079ce252f366981df9401283841c8924782f6dbee8f82a3a81f805ed8a8584595d
  languageName: node
  linkType: hard

"ultron@npm:1.0.x":
  version: 1.0.2
  resolution: "ultron@npm:1.0.2"
  checksum: 10c0/67a4d8c8f2fd52879de20ba38af29ced455457f454e33dcf47cd2ded01f16d5004ffa4fed3690ade63b6581ee5d8dc65a14566385e8d1fa682b65a0bdb681b6e
  languageName: node
  linkType: hard

"union-value@npm:^1.0.0":
  version: 1.0.1
  resolution: "union-value@npm:1.0.1"
  dependencies:
    arr-union: "npm:^3.1.0"
    get-value: "npm:^2.0.6"
    is-extendable: "npm:^0.1.1"
    set-value: "npm:^2.0.1"
  checksum: 10c0/8758d880cb9545f62ce9cfb9b791b2b7a206e0ff5cc4b9d7cd6581da2c6839837fbb45e639cf1fd8eef3cae08c0201b614b7c06dd9f5f70d9dbe7c5fe2fbf592
  languageName: node
  linkType: hard

"unique-filename@npm:^3.0.0":
  version: 3.0.0
  resolution: "unique-filename@npm:3.0.0"
  dependencies:
    unique-slug: "npm:^4.0.0"
  checksum: 10c0/6363e40b2fa758eb5ec5e21b3c7fb83e5da8dcfbd866cc0c199d5534c42f03b9ea9ab069769cc388e1d7ab93b4eeef28ef506ab5f18d910ef29617715101884f
  languageName: node
  linkType: hard

"unique-slug@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-slug@npm:4.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/cb811d9d54eb5821b81b18205750be84cb015c20a4a44280794e915f5a0a70223ce39066781a354e872df3572e8155c228f43ff0cce94c7cbf4da2cc7cbdd635
  languageName: node
  linkType: hard

"universalify@npm:^0.1.0":
  version: 0.1.2
  resolution: "universalify@npm:0.1.2"
  checksum: 10c0/e70e0339f6b36f34c9816f6bf9662372bd241714dc77508d231d08386d94f2c4aa1ba1318614f92015f40d45aae1b9075cd30bd490efbe39387b60a76ca3f045
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.0
  resolution: "universalify@npm:2.0.0"
  checksum: 10c0/07092b9f46df61b823d8ab5e57f0ee5120c178b39609a95e4a15a98c42f6b0b8e834e66fbb47ff92831786193be42f1fd36347169b88ce8639d0f9670af24a71
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 10c0/193400255bd48968e5c5383730344fbb4fa114cdedfab26e329e50dd2d81b134244bb8a72c6ac1b10ab0281a58b363d06405632c9d49ca9dfd5e90cbd7d0f32c
  languageName: node
  linkType: hard

"unset-value@npm:^1.0.0":
  version: 1.0.0
  resolution: "unset-value@npm:1.0.0"
  dependencies:
    has-value: "npm:^0.3.1"
    isobject: "npm:^3.0.0"
  checksum: 10c0/68a796dde4a373afdbf017de64f08490a3573ebee549136da0b3a2245299e7f65f647ef70dc13c4ac7f47b12fba4de1646fa0967a365638578fedce02b9c0b1f
  languageName: node
  linkType: hard

"untildify@npm:^4.0.0":
  version: 4.0.0
  resolution: "untildify@npm:4.0.0"
  checksum: 10c0/d758e624c707d49f76f7511d75d09a8eda7f2020d231ec52b67ff4896bcf7013be3f9522d8375f57e586e9a2e827f5641c7e06ee46ab9c435fc2b2b2e9de517a
  languageName: node
  linkType: hard

"upper-case-first@npm:^2.0.2":
  version: 2.0.2
  resolution: "upper-case-first@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10c0/ccad6a0b143310ebfba2b5841f30bef71246297385f1329c022c902b2b5fc5aee009faf1ac9da5ab3ba7f615b88f5dc1cd80461b18a8f38cb1d4c3eb92538ea9
  languageName: node
  linkType: hard

"upper-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "upper-case@npm:2.0.2"
  dependencies:
    tslib: "npm:^2.0.3"
  checksum: 10c0/5ac176c9d3757abb71400df167f9abb46d63152d5797c630d1a9f083fbabd89711fb4b3dc6de06ff0138fe8946fa5b8518b4fcdae9ca8a3e341417075beae069
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2, uri-js@npm:^4.4.1":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10c0/4ef57b45aa820d7ac6496e9208559986c665e49447cb072744c13b66925a362d96dd5a46c4530a6b8e203e5db5fe849369444440cb22ecfc26c679359e5dfa3c
  languageName: node
  linkType: hard

"urix@npm:^0.1.0":
  version: 0.1.0
  resolution: "urix@npm:0.1.0"
  checksum: 10c0/264f1b29360c33c0aec5fb9819d7e28f15d1a3b83175d2bcc9131efe8583f459f07364957ae3527f1478659ec5b2d0f1ad401dfb625f73e4d424b3ae35fc5fc0
  languageName: node
  linkType: hard

"use@npm:^3.1.0":
  version: 3.1.1
  resolution: "use@npm:3.1.1"
  checksum: 10c0/75b48673ab80d5139c76922630d5a8a44e72ed58dbaf54dee1b88352d10e1c1c1fc332066c782d8ae9a56503b85d3dc67ff6d2ffbd9821120466d1280ebb6d6e
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10c0/41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 10c0/02ba649de1b7ca8854bfe20a82f1dfbdda3fb57a22ab4a8972a63a34553cf7aa51bc9081cf7e001b035b88186d23689d69e71b510e610a09a4c66f68aa95b672
  languageName: node
  linkType: hard

"uuid@npm:^9.0.0":
  version: 9.0.0
  resolution: "uuid@npm:9.0.0"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/8867e438990d1d33ac61093e2e4e3477a2148b844e4fa9e3c2360fa4399292429c4b6ec64537eb1659c97b2d10db349c673ad58b50e2824a11e0d3630de3c056
  languageName: node
  linkType: hard

"v3-integration@workspace:.":
  version: 0.0.0-use.local
  resolution: "v3-integration@workspace:."
  dependencies:
    "@adonisjs/assembler": "npm:^5.9.5"
    "@adonisjs/core": "npm:^5.8.0"
    "@adonisjs/lucid": "npm:^18.4.0"
    "@adonisjs/repl": "npm:^3.1.0"
    "@adonisjs/session": "npm:^6.4.0"
    "@japa/preset-adonis": "npm:^1.2.0"
    "@japa/runner": "npm:^2.5.1"
    "@rocketseat/adonis-bull": "npm:^1.0.4"
    "@types/proxy-addr": "npm:^2.0.0"
    "@types/source-map-support": "npm:^0.5.6"
    "@youngkiu/pino-slack-webhook": "npm:^0.1.2"
    adonis-preset-ts: "npm:^2.1.0"
    axios: "npm:^1.4.0"
    eslint: "npm:^8.45.0"
    eslint-config-prettier: "npm:^8.8.0"
    eslint-plugin-adonis: "npm:^2.1.1"
    eslint-plugin-prettier: "npm:^5.0.0"
    luxon: "npm:^3.3.0"
    mysql2: "npm:^3.5.2"
    pino: "npm:^8.14.1"
    pino-pretty: "npm:^10.1.0"
    prettier: "npm:^3.0.0"
    proxy-addr: "npm:^2.0.7"
    qs: "npm:^6.11.2"
    reflect-metadata: "npm:^0.1.13"
    socket.io-client: "npm:^1.4.0"
    source-map-support: "npm:^0.5.21"
    typescript: "npm:~4.6"
    youch: "npm:^3.2.3"
    youch-terminal: "npm:^2.2.2"
  languageName: unknown
  linkType: soft

"valid-url@npm:~1.0.9":
  version: 1.0.9
  resolution: "valid-url@npm:1.0.9"
  checksum: 10c0/3995e65f9942dbcb1621754c0f9790335cec61e9e9310c0a809e9ae0e2ae91bb7fc6a471fba788e979db0418d9806639f681ecebacc869bc8c3de88efa562ee6
  languageName: node
  linkType: hard

"validate-npm-package-name@npm:^3.0.0":
  version: 3.0.0
  resolution: "validate-npm-package-name@npm:3.0.0"
  dependencies:
    builtins: "npm:^1.0.3"
  checksum: 10c0/064f21f59aefae6cc286dd4a50b15d14adb0227e0facab4316197dfb8d06801669e997af5081966c15f7828a5e6ff1957bd20886aeb6b9d0fa430e4cb5db9c4a
  languageName: node
  linkType: hard

"validator@npm:^13.7.0":
  version: 13.9.0
  resolution: "validator@npm:13.9.0"
  checksum: 10c0/0a0af4b37779671b53ef790aa9d36f71a605c9d41c6daf198d2a1051ce549bcdca3313fa3b52c8fa24577e1a4968ec9404ad8a928d3607d51bccef6d6e33bee7
  languageName: node
  linkType: hard

"vary@npm:^1.1.2, vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: 10c0/f15d588d79f3675135ba783c91a4083dcd290a2a5be9fcb6514220a1634e23df116847b1cc51f66bfb0644cf9353b2abb7815ae499bab06e46dd33c1a6bf1f4f
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.0":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: "npm:^1.0.3"
  checksum: 10c0/5b61ca583a95e2dd85d7078400190efd452e05751a64accb8c06ce4db65d7e0b0cde9917d705e826a2e05cc2548f61efde115ffa374c3e436d04be45c889e5b4
  languageName: node
  linkType: hard

"webpack-merge@npm:^4.2.2":
  version: 4.2.2
  resolution: "webpack-merge@npm:4.2.2"
  dependencies:
    lodash: "npm:^4.17.15"
  checksum: 10c0/283cb4ffe4d4ae6de23d595154868780126835ded241748da0b070c6cca6974c229493ac0b6b7160c2c92950c950c8e5edf036a192da78e32e22a9c81593ad16
  languageName: node
  linkType: hard

"which@npm:^2.0.1, which@npm:^2.0.2":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"wide-align@npm:^1.1.5":
  version: 1.1.5
  resolution: "wide-align@npm:1.1.5"
  dependencies:
    string-width: "npm:^1.0.2 || 2 || 3 || 4"
  checksum: 10c0/1d9c2a3e36dfb09832f38e2e699c367ef190f96b82c71f809bc0822c306f5379df87bab47bed27ea99106d86447e50eb972d3c516c2f95782807a9d082fbea95
  languageName: node
  linkType: hard

"wordwrap@npm:^1.0.0":
  version: 1.0.0
  resolution: "wordwrap@npm:1.0.0"
  checksum: 10c0/7ed2e44f3c33c5c3e3771134d2b0aee4314c9e49c749e37f464bf69f2bcdf0cbf9419ca638098e2717cff4875c47f56a007532f6111c3319f557a2ca91278e92
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^6.2.0":
  version: 6.2.0
  resolution: "wrap-ansi@npm:6.2.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/baad244e6e33335ea24e86e51868fe6823626e3a3c88d9a6674642afff1d34d9a154c917e74af8d845fd25d170c4ea9cf69a47133c3f3656e1252b3d462d9f6c
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10c0/56fece1a4018c6a6c8e28fbc88c87e0fbf4ea8fd64fc6c63b18f4acc4bd13e0ad2515189786dd2c30d3eec9663d70f4ecf699330002f8ccb547e4a18231fc9f0
  languageName: node
  linkType: hard

"ws@npm:~1.1.5":
  version: 1.1.5
  resolution: "ws@npm:1.1.5"
  dependencies:
    options: "npm:>=0.0.5"
    ultron: "npm:1.0.x"
  checksum: 10c0/6d4fba17187cded3d12f3ca5c0dec54f83ad4b6320b1e42dbd2ebea869208552e284276f65c7f4c3f2a0789aeb72ff34fb360e6a796bf3c316603bffcc5e287b
  languageName: node
  linkType: hard

"wtf-8@npm:1.0.0":
  version: 1.0.0
  resolution: "wtf-8@npm:1.0.0"
  checksum: 10c0/0e7d54d18a2c0d18c12b7ec84f4a23cdd2b976099b4705f4c7d9d5ef2374c5b0cbe26d2d43821eafa752256f04389b43525718d3c18c823ab9e8240f004a2a2c
  languageName: node
  linkType: hard

"xmlhttprequest-ssl@npm:1.6.3":
  version: 1.6.3
  resolution: "xmlhttprequest-ssl@npm:1.6.3"
  checksum: 10c0/aa0b71d855866eddf71f52f7b512d3696bb15cf539dcf957268eb1265e6cd836fa7cdceefa55554a9cc4928882cd3399a08f1dbc919080b068b2d5ca45986333
  languageName: node
  linkType: hard

"yallist@npm:^2.1.2":
  version: 2.1.2
  resolution: "yallist@npm:2.1.2"
  checksum: 10c0/0b9e25aa00adf19e01d2bcd4b208aee2b0db643d9927131797b7af5ff69480fc80f1c3db738cbf3946f0bddf39d8f2d0a5709c644fd42d4aa3a4e6e786c087b5
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yaml@npm:^2.0.0-1":
  version: 2.3.1
  resolution: "yaml@npm:2.3.1"
  checksum: 10c0/ed4c21a907fb1cd60a25177612fa46d95064a144623d269199817908475fe85bef20fb17406e3bdc175351b6488056a6f84beb7836e8c262646546a0220188e3
  languageName: node
  linkType: hard

"yeast@npm:0.1.2":
  version: 0.1.2
  resolution: "yeast@npm:0.1.2"
  checksum: 10c0/74530f4ac042e6ff768cb4a35deb1330a092ad239e13f97989aa82496dfb73fcae689eec2f785af1ef904b92ca33cbbffe3d3a7ee937bf29aa033b970af728bb
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10c0/dceb44c28578b31641e13695d200d34ec4ab3966a5729814d5445b194933c096b7ced71494ce53a0e8820685d1d010df8b2422e5bf2cdea7e469d97ffbea306f
  languageName: node
  linkType: hard

"youch-terminal@npm:^2.2.0, youch-terminal@npm:^2.2.2":
  version: 2.2.2
  resolution: "youch-terminal@npm:2.2.2"
  dependencies:
    kleur: "npm:^4.1.5"
    string-width: "npm:^4.2.3"
    wordwrap: "npm:^1.0.0"
  checksum: 10c0/2333ce7aeca4840f6590605c485ab4e4591d50e53f696c7ba008cd9fadfd42c6bf3cff279ceff3942bf28138ead3efc4227e8c72113403a2dd124cb097e5b469
  languageName: node
  linkType: hard

"youch@npm:^3.2.3":
  version: 3.2.3
  resolution: "youch@npm:3.2.3"
  dependencies:
    cookie: "npm:^0.5.0"
    mustache: "npm:^4.2.0"
    stacktracey: "npm:^2.1.8"
  checksum: 10c0/a6992afaa15d0ea3a4cc42b71fd20d21c7368e07da6f9de45d4d3b9e1bb0f1d74f51105a720419bc71ae8691f49ed33b6d17631c66a3b0be669a964f0dd0f1a0
  languageName: node
  linkType: hard

"z-schema@npm:^5.0.1":
  version: 5.0.6
  resolution: "z-schema@npm:5.0.6"
  dependencies:
    commander: "npm:^10.0.0"
    lodash.get: "npm:^4.4.2"
    lodash.isequal: "npm:^4.5.0"
    validator: "npm:^13.7.0"
  dependenciesMeta:
    commander:
      optional: true
  bin:
    z-schema: bin/z-schema
  checksum: 10c0/3242da6b2d8da3bc9a66876ef01a1d5f0d0ad7bd70b0e3e24f5dc6ef5f6213e6e660f14f3dceee9b000692a47b86b365c0ea43b5340153efcb2808ccbfb3fc6f
  languageName: node
  linkType: hard
