rm ../.env
echo "PORT=3333" >> ../.env
echo "HOST=0.0.0.0" >> ../.env
echo "NODE_ENV=production" >> ../.env
echo "APP_KEY=m9D3KMvjHoWyEMx2XMy4Me-aj3s4lsXj" >> ../.env
echo "DRIVE_DISK=local" >> ../.env
echo "DB_CONNECTION=mysql" >> ../.env
echo "MYSQL_HOST=localhost" >> ../.env
echo "MYSQL_PORT=3306" >> ../.env
echo "MYSQL_USER=dermacare" >> ../.env
echo "MYSQL_PASSWORD=Admin1q2w--" >> ../.env
echo "MYSQL_DB_NAME=dermacare" >> ../.env
echo "BULL_REDIS_HOST='*************'" >> ../.env
echo "BULL_REDIS_PORT=6379" >> ../.env
echo "BULL_REDIS_PASSWORD='m8fit81nVGrdBWIBK2bFwbnwk5eC5ngah+iYajyabukFtghUcQmBsBW51cjUPFPPpdu8/Y1bL7jid1ld'" >> ../.env
echo "AP_CLIENT_ID='64b051c0152e7d066034e285-lk1jw4zx'" >> ../.env
echo "AP_CLIENT_SECRET='457342b3-1a3d-45a7-8482-bae7be489b5c'" >> ../.env
echo "AP_SCOPE='businesses.readonly businesses.write calendars.readonly calendars/events.readonly calendars/events.write conversations.readonly conversations.write conversations/message.readonly conversations/message.write campaigns.readonly contacts.readonly contacts.write locations.readonly locations/customValues.readonly locations/customValues.write locations/customFields.readonly locations/customFields.write opportunities.readonly opportunities.write links.readonly links.write workflows.readonly forms.readonly users.readonly users.write surveys.readonly locations/tags.readonly locations/tags.write locations/templates.readonly locations/tasks.readonly'" >> ../.env
echo "AP_CALLBACK_URL='https://dermacare.apconnect.co/ap-callback'" >> ../.env
echo "AP_AGENCY_API='eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb21wYW55X2lkIjoiaUU3dTRiTXJoQ3BuMVVpdWNJalgiLCJ2ZXJzaW9uIjoxLCJpYXQiOjE2NzQxNjg2NzYxMjMsInN1YiI6ImdNN09na0lUZ2ZmQVIxNXRVN2tWIn0.rppaiPml_sBQ8beh7basY_KSCgDpppLDsZ2vKa1868M'" >> ../.env
echo "SESSION_DRIVER=file" >> ../.env
