#!/bin/bash

# Check if the script is being run as root
if [[ $(id -u) -ne 0 ]]; then
  echo "Please run this script as root or with sudo."
  exit 1
fi

# Get user inputs
read -p "Enter your domain name (e.g., your_domain.com): " DOMAIN
read -p "Enter the Node.js app port (e.g., 3000): " APP_PORT

# Validate the domain name (allowing subdomains)
if ! [[ $DOMAIN =~ ^([a-zA-Z0-9.-]+\.)?([a-zA-Z0-9.-]+\.)+[a-zA-Z]{2,}$ ]]; then
  echo "Invalid domain name format."
  exit 1
fi

if ! command -v nginx &> /dev/null
then
    echo "Nginx is not installed. Installing..."
    sudo apt-get update
    sudo apt-get install nginx -y
    echo "Nginx installed successfully."
    sudo ufw allow 'Nginx HTTP'
    sudo ufw status
else
    echo "Nginx is already installed."
fi

# Create the Nginx server block configuration file
CONFIG_FILE="/etc/nginx/sites-available/$DOMAIN.conf"

if [ -e "$CONFIG_FILE" ]; then
    echo "Config File already exists. Deleting..."
    rm "$CONFIG_FILE"
    echo "Config File deleted."
fi

echo "Creating Nginx server block configuration file..."

cat << EOF > $CONFIG_FILE
server {
    listen 80;
    server_name $DOMAIN;

    location / {
        proxy_pass http://127.0.0.1:$APP_PORT;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

# Create a symbolic link to enable the server block
ENABLED_LINK="/etc/nginx/sites-enabled/$DOMAIN.conf"
if [ -L "$ENABLED_LINK" ]; then
    echo "Symlink already exists. Deleting..."
    rm "$ENABLED_LINK"
    echo "Old Symlink deleted."
fi

ln -s $CONFIG_FILE $ENABLED_LINK

# Test Nginx configuration
nginx -t

# Reload Nginx to apply the changes
systemctl reload nginx

echo "Nginx server block for $DOMAIN has been created and enabled."

echo "Is MariaDB exist?"
if ! command -v mysql &> /dev/null
then
    echo "MariaDB is not installed. Installing..."
    sudo apt-get update
    sudo apt-get install mariadb-server -y
    echo "MariaDB installed successfully."
    read -p "Enter new MariaDB root password: " new_password

    # Set the root password
    sudo mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '$new_password'; FLUSH PRIVILEGES;"
    sudo mysql -e "ALTER USER 'root'@'127.0.0.1' IDENTIFIED BY '$new_password'; FLUSH PRIVILEGES;"
else
    echo "MariaDB is already installed."
fi

# Check if Redis is already installed
echo "Is Redis exists?"
if ! command -v redis-server &> /dev/null
then
    # Install Redis
    echo "Redis is not installed. Installing..."
    sudo apt-get update
    sudo apt-get install redis-server -y
    echo "Redis installed successfully."
else
    echo "Redis is already installed."
fi


echo "Starting pm2..."
pm2 start

# Check if the user wants to install SSL with Certbot
read -p "Do you want to install SSL with Certbot? (yes/no): " INSTALL_SSL

if [[ $INSTALL_SSL =~ ^[Yy][Ee][Ss]$ ]]; then

    # Check if Certbot is installed
    if ! command -v certbot &>/dev/null; then
        echo "Certbot not found. Installing Certbot..."
        if [[ -f /etc/os-release ]]; then
            source /etc/os-release
            if [[ $ID == "ubuntu" || $ID == "debian" ]]; then
                if ! command -v snap &>/dev/null; then
                    echo "Snapd not found. Installing Snapd..."
                    sudo apt update
                    sudo apt install -y snapd
                fi
                sudo snap install --classic certbot
                sudo ln -s /snap/bin/certbot /usr/bin/certbot
            else
                echo "Unsupported operating system. Please install Certbot manually."
                exit 1
            fi
        else
            echo "Unable to determine the operating system. Please install Certbot manually."
            exit 1
        fi
    fi

    # Install SSL with Certbot
    echo "Installing SSL with Certbot..."
    sudo certbot --nginx -d $DOMAIN
fi

echo

echo -e "We're done, Now you can visit https://$DOMAIN"
