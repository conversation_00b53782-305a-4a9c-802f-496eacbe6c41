{"name": "v3-integration", "version": "1.0.0", "private": true, "scripts": {"dev": "node ace serve --watch", "build": "node ace build --production && copy ecosystem.config.js ./build && copy ./.env ./build && copy ./scripts/CreateNginxServerBlock.sh ./build", "start": "pm2 stop all && pm2 start all", "test": "node ace test", "lint": "eslint . --ext=.ts --fix", "format": "prettier --write .", "dbup": "node ace migration:run"}, "eslintConfig": {"extends": ["plugin:adonis/typescriptApp", "prettier"], "plugins": ["prettier"], "rules": {"prettier/prettier": ["error"]}}, "eslintIgnore": ["build"], "prettier": {"trailingComma": "es5", "semi": false, "singleQuote": true, "useTabs": false, "quoteProps": "consistent", "bracketSpacing": true, "arrowParens": "always", "printWidth": 100}, "devDependencies": {"@adonisjs/assembler": "^5.9.5", "@japa/preset-adonis": "^1.2.0", "@japa/runner": "^2.5.1", "@types/proxy-addr": "^2.0.0", "@types/source-map-support": "^0.5.6", "adonis-preset-ts": "^2.1.0", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-adonis": "^2.1.1", "eslint-plugin-prettier": "^5.0.0", "pino-pretty": "^10.1.0", "prettier": "^3.0.0", "typescript": "~4.6", "youch": "^3.2.3", "youch-terminal": "^2.2.2"}, "dependencies": {"@adonisjs/core": "^5.8.0", "@adonisjs/lucid": "^18.4.0", "@adonisjs/repl": "^3.1.0", "@adonisjs/session": "^6.4.0", "@rocketseat/adonis-bull": "^1.0.4", "@youngkiu/pino-slack-webhook": "^0.1.2", "axios": "^1.4.0", "luxon": "^3.3.0", "mysql2": "^3.5.2", "pino": "^8.14.1", "proxy-addr": "^2.0.7", "qs": "^6.11.2", "reflect-metadata": "^0.1.13", "socket.io-client": "^1.4.0", "source-map-support": "^0.5.21"}}