import OAuth from 'App/Models/OAuth'
import HttpContext from '@ioc:Adonis/Core/HttpContext'

let apiAuth: OAuth | null = null

export const getAPIAuth = (): OAuth => {
  if (apiAuth) return apiAuth
  const ctx = HttpContext.get()
  if (ctx && ctx.session && ctx.session.has('auth')) {
    apiAuth = ctx.session.get('auth')
    if (apiAuth) return apiAuth
  }
  throw new Error('Auth not set yet')
}
export const setAPIAuth = async (a: number) => {
  if (typeof a === 'number') {
    apiAuth = await OAuth.findByOrFail('id', a)
  }
  return apiAuth ? apiAuth : null
}
