export const cLog = (msg: string, data: any = null) => {
  console.log('='.repeat(100))
  console.log(' ')
  console.log(msg)
  if (data) {
    console.log(' ')
    console.log('-'.repeat(100))
    console.log(' ')
    console.log(JSON.stringify(data))
  }
  console.log(' ')
  console.log('='.repeat(100))
}

export const removeNullEmptyProperties = (obj: any) => {
  if (Array.isArray(obj)) {
    for (let i = obj.length - 1; i >= 0; i--) {
      obj[i] = removeNullEmptyProperties(obj[i])
      if (obj[i] === null || obj[i] === '') {
        obj.splice(i, 1)
      }
    }
    if (obj.length === 0) {
      obj = undefined
    }
  } else if (typeof obj === 'object' && obj !== null) {
    for (const key in obj) {
      obj[key] = removeNullEmptyProperties(obj[key])
      if (obj[key] === null || obj[key] === '') {
        delete obj[key]
      }
    }
    if (Object.keys(obj).length === 0) {
      obj = undefined
    }
  }
  return obj
}

export const reduceCustomFieldValue = (values) => {
  if (Array.isArray(values))
    return values.reduce((val1, val2) => `${val1}${contactHelper(val1)}${val2}`, '')
  return values
}
const contactHelper = (str: any) => (str ? ', ' : '')

// make a function to remove html tags from string
export const removeHtmlTags = (str: string): string => {
  const pattern = /<\S[^>]*?>/g
  const pattern2 = /\[?\]?/g
  const pattern3 = /\s{2,}/g
  return str.replace(pattern, '').replace(pattern2, '').replace(pattern3, ' ')
}
